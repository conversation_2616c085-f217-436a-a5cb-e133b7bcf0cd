{"dns": {"rules": [{"outbound": ["any"], "server": "local"}, {"clash_mode": "global", "server": "remote"}, {"clash_mode": "direct", "server": "local"}, {"rule_set": ["geosite-cn"], "server": "local"}], "servers": [{"address": "https://*******/dns-query", "detour": "节点选择", "tag": "remote"}, {"address": "https://*********/dns-query", "detour": "direct", "tag": "local"}, {"address": "rcode://success", "tag": "block"}], "strategy": "prefer_ipv4"}, "experimental": {"cache_file": {"enabled": true, "path": "cache.db", "cache_id": "cache_db", "store_fakeip": true}}, "inbounds": [{"auto_route": true, "domain_strategy": "prefer_ipv4", "endpoint_independent_nat": true, "address": ["**********/30", "2001:0470:f9da:fdfa::1/64"], "mtu": 9000, "sniff": true, "sniff_override_destination": true, "stack": "system", "strict_route": true, "type": "tun"}, {"domain_strategy": "prefer_ipv4", "listen": "127.0.0.1", "listen_port": 2333, "sniff": true, "sniff_override_destination": true, "tag": "socks-in", "type": "socks", "users": []}, {"domain_strategy": "prefer_ipv4", "listen": "127.0.0.1", "listen_port": 2334, "sniff": true, "sniff_override_destination": true, "tag": "mixed-in", "type": "mixed", "users": []}], "outbounds": [{"tag": "节点选择", "type": "selector", "default": "自动选择", "outbounds": ["自动选择"]}, {"tag": "direct", "type": "direct"}, {"tag": "block", "type": "block"}, {"tag": "dns-out", "type": "dns"}, {"tag": "自动选择", "type": "urltest", "outbounds": []}], "route": {"auto_detect_interface": true, "rules": [{"outbound": "dns-out", "protocol": "dns"}, {"clash_mode": "direct", "outbound": "direct"}, {"clash_mode": "global", "outbound": "节点选择"}, {"ip_is_private": true, "outbound": "direct"}, {"rule_set": ["geosite-cn", "geoip-cn"], "outbound": "direct"}], "rule_set": [{"tag": "geosite-cn", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/SagerNet/sing-geosite/rule-set/geosite-cn.srs", "download_detour": "自动选择"}, {"tag": "geoip-cn", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/SagerNet/sing-geoip/rule-set/geoip-cn.srs", "download_detour": "自动选择"}]}}