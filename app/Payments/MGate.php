<?php

/**
 * 自己写别抄，抄NMB抄
 */
namespace App\Payments;

use App\Exceptions\ApiException;
use \Curl\Curl;
use App\Contracts\PaymentInterface;
class MGate implements PaymentInterface
{
    private $config;

    public function __construct($config)
    {
        $this->config = $config;
    }

    public function form(): array
    {
        return [
            'mgate_url' => [
                'label' => 'API地址',
                'description' => '',
                'type' => 'input',
            ],
            'mgate_app_id' => [
                'label' => 'APPID',
                'description' => '',
                'type' => 'input',
            ],
            'mgate_app_secret' => [
                'label' => 'AppSecret',
                'description' => '',
                'type' => 'input',
            ],
            'mgate_source_currency' => [
                'label' => '源货币',
                'description' => '默认CNY',
                'type' => 'input'
            ]
        ];
    }

    public function pay($order): array
    {
        $params = [
            'out_trade_no' => $order['trade_no'],
            'total_amount' => $order['total_amount'],
            'notify_url' => $order['notify_url'],
            'return_url' => $order['return_url']
        ];
        if (isset($this->config['mgate_source_currency'])) {
            $params['source_currency'] = $this->config['mgate_source_currency'];
        }
        $params['app_id'] = $this->config['mgate_app_id'];
        ksort($params);
        $str = http_build_query($params) . $this->config['mgate_app_secret'];
        $params['sign'] = md5($str);
        $curl = new Curl();
        $curl->setUserAgent('MGate');
        $curl->setOpt(CURLOPT_SSL_VERIFYPEER, 0);
        $curl->post($this->config['mgate_url'] . '/v1/gateway/fetch', http_build_query($params));
        $result = $curl->response;
        if (!$result) {
            throw new ApiException('网络异常');
        }
        if ($curl->error) {
            if (isset($result->errors)) {
                $errors = (array) $result->errors;
                throw new ApiException($errors[array_keys($errors)[0]][0]);
            }
            if (isset($result->message)) {
                throw new ApiException($result->message);
            }
            throw new ApiException('未知错误');
        }
        $curl->close();
        if (!isset($result->data->trade_no)) {
            throw new ApiException('接口请求失败');
        }
        return [
            'type' => 1, // 0:qrcode 1:url
            'data' => $result->data->pay_url
        ];
    }

    public function notify($params): array|bool
    {
        $sign = $params['sign'];
        unset($params['sign']);
        ksort($params);
        reset($params);
        $str = http_build_query($params) . $this->config['mgate_app_secret'];
        if ($sign !== md5($str)) {
            return false;
        }
        return [
            'trade_no' => $params['out_trade_no'],
            'callback_no' => $params['trade_no']
        ];
    }
}
