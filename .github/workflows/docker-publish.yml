name: Docker Build and Publish

on:
  push:
    branches: ["master", "new-dev"]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      id-token: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: 'arm64,amd64'

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64,linux/arm64
          driver-opts: |
            image=moby/buildkit:v0.20.0
            network=host

      - name: Free Disk Space
        run: |
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /usr/local/lib/android
          sudo rm -rf /opt/ghc
          sudo rm -rf /opt/hostedtoolcache/CodeQL
          sudo docker image prune -af

      - name: Login to registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=sha,format=long
            type=raw,value=new,enable=${{ github.ref == 'refs/heads/master' }}
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/master' }}
            type=raw,value=${{ steps.get_version.outputs.version }}
          labels: |
            org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
            org.opencontainers.image.revision=${{ github.sha }}

      - name: Get version
        id: get_version
        run: echo "version=$(git describe --tags --always)" >> $GITHUB_OUTPUT

      - name: Update version in app.php
        run: |
          VERSION=$(date '+%Y%m%d')-$(git rev-parse --short HEAD)
          sed -i "s/'version' => '.*'/'version' => '$VERSION'/g" config/app.php
          echo "Updated version to: $VERSION"

      - name: Build and push
        id: build-and-push
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          platforms: linux/amd64,linux/arm64
          cache-from: type=gha
          cache-to: type=gha,mode=max
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            BUILDKIT_INLINE_CACHE=1
            BUILDKIT_MULTI_PLATFORM=1
            CACHEBUST=${{ github.sha }}
            REPO_URL=https://github.com/${{ github.repository }}
            BRANCH_NAME=${{ github.ref_name }}
          provenance: false
          outputs: type=registry,push=true
          allow: |
            network.host

      - name: Install cosign
        uses: sigstore/cosign-installer@v3.4.0
        with:
          cosign-release: 'v2.2.2'

      - name: Sign image
        if: steps.build-and-push.outputs.digest != ''
        run: |
          echo "${{ steps.meta.outputs.tags }}" | xargs -I {} cosign sign --yes "{}@${{ steps.build-and-push.outputs.digest }}"