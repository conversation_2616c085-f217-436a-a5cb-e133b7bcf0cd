import{r as m,j as e,t as di,c as mi,I as Vn,a as Zs,S as on,u as Is,b as ui,d as cn,R as tl,e as al,f as xi,F as hi,C as pi,L as nl,T as ll,g as rl,h as gi,i as fi,k as ji,l as A,z as x,m as V,n as ye,o as _e,p as ne,q as gs,s as Se,v as ot,w as vi,O as dn,x as bi,y as yi,A as Ni,B as _i,D as wi,E as Ci,Q as Si,G as ki,H as Ti,J as Di,P as Pi,K as Ei,M as Ri,N as Ii,U as Li,V as il,W as ol,X as ja,Y as va,Z as mn,_ as ds,$ as ba,a0 as ya,a1 as cl,a2 as dl,a3 as ml,a4 as un,a5 as ul,a6 as Vi,a7 as xl,a8 as hl,a9 as pl,aa as gl,ab as et,ac as fl,ad as Fi,ae as jl,af as vl,ag as Mi,ah as Oi,ai as zi,aj as $i,ak as <PERSON>,al as qi,am as Hi,an as Ui,ao as <PERSON>,ap as Bi,aq as Gi,ar as bl,as as Wi,at as Yi,au as st,av as yl,aw as Ji,ax as Qi,ay as Nl,az as xn,aA as Xi,aB as Zi,aC as Fn,aD as eo,aE as _l,aF as so,aG as wl,aH as to,aI as ao,aJ as no,aK as lo,aL as ro,aM as io,aN as Cl,aO as oo,aP as co,aQ as mo,aR as He,aS as uo,aT as hn,aU as xo,aV as ho,aW as Sl,aX as kl,aY as Tl,aZ as po,a_ as go,a$ as fo,b0 as Dl,b1 as jo,b2 as pn,b3 as Pl,b4 as vo,b5 as El,b6 as bo,b7 as Rl,b8 as yo,b9 as Il,ba as Ll,bb as No,bc as _o,bd as Vl,be as wo,bf as Co,bg as Fl,bh as So,bi as Ml,bj as ko,bk as To,bl as hs,bm as xs,bn as Kt,bo as Do,bp as Po,bq as Eo,br as Ro,bs as Io,bt as Lo,bu as Mn,bv as On,bw as Vo,bx as Fo,by as Mo,bz as Oo,bA as zo,bB as Qa,bC as At,bD as $o,bE as Ao,bF as Ol,bG as qo,bH as Ho,bI as zl,bJ as Uo,bK as Ko,bL as zn,bM as Xa,bN as Za,bO as Bo,bP as Go,bQ as $l,bR as Wo,bS as Yo,bT as Jo,bU as da,bV as en,bW as Je,bX as gn,bY as Qo,bZ as za,b_ as Xo,b$ as $n,c0 as Ft,c1 as sn,c2 as tn,c3 as Al,c4 as Qe,c5 as ls,c6 as ql,c7 as Hl,c8 as Zo,c9 as ec,ca as sc,cb as tc,cc as ac,cd as Ul,ce as nc,cf as lc,cg as ze,ch as An,ci as rc,cj as Kl,ck as Bl,cl as Gl,cm as Wl,cn as Yl,co as Jl,cp as ic,cq as oc,cr as cc,cs as Na,ct as tt,cu as fs,cv as js,cw as vs,cx as dc,cy as mc,cz as uc,cA as xc,cB as hc,cC as pc,cD as gc,cE as fc,cF as jc,cG as an,cH as fn,cI as jn,cJ as vc,cK as Ls,cL as Vs,cM as _a,cN as bc,cO as ma,cP as yc,cQ as qn,cR as Ql,cS as Hn,cT as ua,cU as Nc,cV as _c,cW as wc,cX as Cc,cY as Xl,cZ as Sc,c_ as kc,c$ as Zl,d0 as nn,d1 as er,d2 as Tc,d3 as sr,d4 as tr,d5 as Dc,d6 as Pc,d7 as Ec,d8 as Rc,d9 as Ic}from"./vendor.js";import"./index.js";var $h=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ah(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}function Lc(s){if(s.__esModule)return s;var a=s.default;if(typeof a=="function"){var t=function l(){return this instanceof l?Reflect.construct(a,arguments,this.constructor):a.apply(this,arguments)};t.prototype=a.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(s).forEach(function(l){var n=Object.getOwnPropertyDescriptor(s,l);Object.defineProperty(t,l,n.get?n:{enumerable:!0,get:function(){return s[l]}})}),t}const Vc={theme:"system",setTheme:()=>null},ar=m.createContext(Vc);function Fc({children:s,defaultTheme:a="system",storageKey:t="vite-ui-theme",...l}){const[n,o]=m.useState(()=>localStorage.getItem(t)||a);m.useEffect(()=>{const c=window.document.documentElement;if(c.classList.remove("light","dark"),n==="system"){const u=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";c.classList.add(u);return}c.classList.add(n)},[n]);const r={theme:n,setTheme:c=>{localStorage.setItem(t,c),o(c)}};return e.jsx(ar.Provider,{...l,value:r,children:s})}const Mc=()=>{const s=m.useContext(ar);if(s===void 0)throw new Error("useTheme must be used within a ThemeProvider");return s},Oc=function(){const a=typeof document<"u"&&document.createElement("link").relList;return a&&a.supports&&a.supports("modulepreload")?"modulepreload":"preload"}(),zc=function(s,a){return new URL(s,a).href},Un={},fe=function(a,t,l){let n=Promise.resolve();if(t&&t.length>0){const r=document.getElementsByTagName("link"),c=document.querySelector("meta[property=csp-nonce]"),u=c?.nonce||c?.getAttribute("nonce");n=Promise.allSettled(t.map(i=>{if(i=zc(i,l),i in Un)return;Un[i]=!0;const d=i.endsWith(".css"),h=d?'[rel="stylesheet"]':"";if(!!l)for(let S=r.length-1;S>=0;S--){const C=r[S];if(C.href===i&&(!d||C.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${h}`))return;const T=document.createElement("link");if(T.rel=d?"stylesheet":Oc,d||(T.as="script"),T.crossOrigin="",T.href=i,u&&T.setAttribute("nonce",u),document.head.appendChild(T),d)return new Promise((S,C)=>{T.addEventListener("load",S),T.addEventListener("error",()=>C(new Error(`Unable to preload CSS for ${i}`)))})}))}function o(r){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=r,window.dispatchEvent(c),!c.defaultPrevented)throw r}return n.then(r=>{for(const c of r||[])c.status==="rejected"&&o(c.reason);return a().catch(o)})};function y(...s){return di(mi(s))}const wt=Zs("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),E=m.forwardRef(({className:s,variant:a,size:t,asChild:l=!1,children:n,disabled:o,loading:r=!1,leftSection:c,rightSection:u,...i},d)=>{const h=l?on:"button";return e.jsxs(h,{className:y(wt({variant:a,size:t,className:s})),disabled:r||o,ref:d,...i,children:[(c&&r||!c&&!u&&r)&&e.jsx(Vn,{className:"mr-2 h-4 w-4 animate-spin"}),!r&&c&&e.jsx("div",{className:"mr-2",children:c}),n,!r&&u&&e.jsx("div",{className:"ml-2",children:u}),u&&r&&e.jsx(Vn,{className:"ml-2 h-4 w-4 animate-spin"})]})});E.displayName="Button";function ct({className:s,minimal:a=!1}){const t=Is(),l=ui(),n=l?.message||l?.statusText||"Unknown error occurred";return e.jsx("div",{className:y("h-svh w-full",s),children:e.jsxs("div",{className:"m-auto flex h-full w-full flex-col items-center justify-center gap-2",children:[!a&&e.jsx("h1",{className:"text-[7rem] font-bold leading-tight",children:"500"}),e.jsxs("span",{className:"font-medium",children:["Oops! Something went wrong ",":')"]}),e.jsxs("p",{className:"text-center text-muted-foreground",children:["We apologize for the inconvenience. ",e.jsx("br",{}),n]}),!a&&e.jsxs("div",{className:"mt-6 flex gap-4",children:[e.jsx(E,{variant:"outline",onClick:()=>t(-1),children:"Go Back"}),e.jsx(E,{onClick:()=>t("/"),children:"Back to Home"})]})]})})}function Kn(){const s=Is();return e.jsx("div",{className:"h-svh",children:e.jsxs("div",{className:"m-auto flex h-full w-full flex-col items-center justify-center gap-2",children:[e.jsx("h1",{className:"text-[7rem] font-bold leading-tight",children:"404"}),e.jsx("span",{className:"font-medium",children:"Oops! Page Not Found!"}),e.jsxs("p",{className:"text-center text-muted-foreground",children:["It seems like the page you're looking for ",e.jsx("br",{}),"does not exist or might have been removed."]}),e.jsxs("div",{className:"mt-6 flex gap-4",children:[e.jsx(E,{variant:"outline",onClick:()=>s(-1),children:"Go Back"}),e.jsx(E,{onClick:()=>s("/"),children:"Back to Home"})]})]})})}function $c(){return e.jsx("div",{className:"h-svh",children:e.jsxs("div",{className:"m-auto flex h-full w-full flex-col items-center justify-center gap-2",children:[e.jsx("h1",{className:"text-[7rem] font-bold leading-tight",children:"503"}),e.jsx("span",{className:"font-medium",children:"Website is under maintenance!"}),e.jsxs("p",{className:"text-center text-muted-foreground",children:["The site is not available at the moment. ",e.jsx("br",{}),"We'll be back online shortly."]}),e.jsx("div",{className:"mt-6 flex gap-4",children:e.jsx(E,{variant:"outline",children:"Learn more"})})]})})}function Ac(s){return typeof s>"u"}function qc(s){return s===null}function Hc(s){return qc(s)||Ac(s)}class Uc{storage;prefixKey;constructor(a){this.storage=a.storage,this.prefixKey=a.prefixKey}getKey(a){return`${this.prefixKey}${a}`.toUpperCase()}set(a,t,l=null){const n=JSON.stringify({value:t,time:Date.now(),expire:l!==null?new Date().getTime()+l*1e3:null});this.storage.setItem(this.getKey(a),n)}get(a,t=null){const l=this.storage.getItem(this.getKey(a));if(!l)return{value:t,time:0};try{const n=JSON.parse(l),{value:o,time:r,expire:c}=n;return Hc(c)||c>new Date().getTime()?{value:o,time:r}:(this.remove(a),{value:t,time:0})}catch{return this.remove(a),{value:t,time:0}}}remove(a){this.storage.removeItem(this.getKey(a))}clear(){this.storage.clear()}}function nr({prefixKey:s="",storage:a=sessionStorage}){return new Uc({prefixKey:s,storage:a})}const lr="Xboard_",Kc=function(s={}){return nr({prefixKey:s.prefixKey||"",storage:localStorage})},Bc=function(s={}){return nr({prefixKey:s.prefixKey||"",storage:sessionStorage})},wa=Kc({prefixKey:lr});Bc({prefixKey:lr});const rr="access_token";function qt(){return wa.get(rr)}function ir(){wa.remove(rr)}const Bn=["/sign-in","/sign-in-2","/sign-up","/forgot-password","/otp"];function Gc({children:s}){const a=Is(),t=cn(),l=qt();return m.useEffect(()=>{if(!l.value&&!Bn.includes(t.pathname)){const n=encodeURIComponent(t.pathname+t.search);a(`/sign-in?redirect=${n}`)}},[l.value,t.pathname,t.search,a]),Bn.includes(t.pathname)||l.value?e.jsx(e.Fragment,{children:s}):null}const ke=m.forwardRef(({className:s,orientation:a="horizontal",decorative:t=!0,...l},n)=>e.jsx(tl,{ref:n,decorative:t,orientation:a,className:y("shrink-0 bg-border",a==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",s),...l}));ke.displayName=tl.displayName;const Wc=Zs("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),xa=m.forwardRef(({className:s,...a},t)=>e.jsx(al,{ref:t,className:y(Wc(),s),...a}));xa.displayName=al.displayName;const we=hi,or=m.createContext({}),v=({...s})=>e.jsx(or.Provider,{value:{name:s.name},children:e.jsx(pi,{...s})}),Ca=()=>{const s=m.useContext(or),a=m.useContext(cr),{getFieldState:t,formState:l}=xi(),n=t(s.name,l);if(!s)throw new Error("useFormField should be used within <FormField>");const{id:o}=a;return{id:o,name:s.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...n}},cr=m.createContext({}),f=m.forwardRef(({className:s,...a},t)=>{const l=m.useId();return e.jsx(cr.Provider,{value:{id:l},children:e.jsx("div",{ref:t,className:y("space-y-2",s),...a})})});f.displayName="FormItem";const j=m.forwardRef(({className:s,...a},t)=>{const{error:l,formItemId:n}=Ca();return e.jsx(xa,{ref:t,className:y(l&&"text-destructive",s),htmlFor:n,...a})});j.displayName="FormLabel";const b=m.forwardRef(({...s},a)=>{const{error:t,formItemId:l,formDescriptionId:n,formMessageId:o}=Ca();return e.jsx(on,{ref:a,id:l,"aria-describedby":t?`${n} ${o}`:`${n}`,"aria-invalid":!!t,...s})});b.displayName="FormControl";const F=m.forwardRef(({className:s,...a},t)=>{const{formDescriptionId:l}=Ca();return e.jsx("p",{ref:t,id:l,className:y("text-[0.8rem] text-muted-foreground",s),...a})});F.displayName="FormDescription";const P=m.forwardRef(({className:s,children:a,...t},l)=>{const{error:n,formMessageId:o}=Ca(),r=n?String(n?.message):a;return r?e.jsx("p",{ref:l,id:o,className:y("text-[0.8rem] font-medium text-destructive",s),...t,children:r}):null});P.displayName="FormMessage";const Bt=gi,Ct=m.forwardRef(({className:s,...a},t)=>e.jsx(nl,{ref:t,className:y("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",s),...a}));Ct.displayName=nl.displayName;const Ee=m.forwardRef(({className:s,...a},t)=>e.jsx(ll,{ref:t,className:y("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",s),...a}));Ee.displayName=ll.displayName;const We=m.forwardRef(({className:s,...a},t)=>e.jsx(rl,{ref:t,className:y("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...a}));We.displayName=rl.displayName;function Ce(s=void 0,a="YYYY-MM-DD HH:mm:ss"){return s==null?"":(Math.floor(s).toString().length===10&&(s=s*1e3),fi(s).format(a))}function Yc(s=void 0,a="YYYY-MM-DD"){return Ce(s,a)}function pt(s){const a=typeof s=="string"?parseFloat(s):s;return isNaN(a)?"0.00":a.toFixed(2)}function Ws(s,a=!0){if(s==null)return a?"¥0.00":"0.00";const t=typeof s=="string"?parseFloat(s):s;if(isNaN(t))return a?"¥0.00":"0.00";const n=(t/100).toFixed(2).replace(/\.?0+$/,o=>o.includes(".")?".00":o);return a?`¥${n}`:n}function ha(s){return new Promise(a=>{(async()=>{try{if(navigator.clipboard)await navigator.clipboard.writeText(s);else{const l=document.createElement("textarea");l.value=s,l.style.position="fixed",l.style.opacity="0",document.body.appendChild(l),l.select();const n=document.execCommand("copy");if(document.body.removeChild(l),!n)throw new Error("execCommand failed")}a(!0)}catch(l){console.error(l),a(!1)}})()})}function Oe(s){const a=s/1024,t=a/1024,l=t/1024,n=l/1024;return n>=1?pt(n)+" TB":l>=1?pt(l)+" GB":t>=1?pt(t)+" MB":pt(a)+" KB"}const Jc="locale";function Qc(){return wa.get(Jc)}function dr(){ir();const s=window.location.pathname,a=s&&!["/404","/sign-in"].includes(s),t=new URL(window.location.href),n=`${t.pathname.split("/")[1]?`/${t.pathname.split("/")[1]}`:""}#/sign-in`;window.location.href=n+(a?`?redirect=${s}`:"")}const Xc=["/passport/auth/login","/passport/auth/token2Login","/passport/auth/register","/guest/comm/config","/passport/comm/sendEmailVerify","/passport/auth/forget"];function Zc(){const s=window.settings?.base_url||"/";return s.endsWith("/")?s+"api/v2":s+"/api/v2"}const gt=ji.create({baseURL:Zc(),timeout:12e3,headers:{"Content-Type":"application/json"}});gt.interceptors.request.use(s=>{s.method?.toLowerCase()==="get"&&(s.params={...s.params,t:Date.now()});const a=qt();if(!Xc.includes(s.url?.split("?")[0]||"")){if(!a.value)return dr(),Promise.reject({code:-1,message:"未登录"});s.headers.Authorization=a.value}return s.headers["Content-Language"]=Qc().value||"zh-CN",s},s=>Promise.reject(s));gt.interceptors.response.use(s=>s?.data||{code:-1,message:"未知错误"},s=>{const a=s.response?.status,t=s.response?.data?.message;return(a===401||a===403)&&dr(),A.error(t||{401:"登录已过期",403:"没有权限",404:"资源或接口不存在"}[a]||"未知异常"),Promise.reject(s.response?.data||{data:null,code:-1,message:"未知错误"})});const M={get:(s,a)=>gt.get(s,a),post:(s,a,t)=>gt.post(s,a,t),put:(s,a,t)=>gt.put(s,a,t),delete:(s,a)=>gt.delete(s,a)},ed="access_token";function sd(s){wa.set(ed,s)}const Bs=window?.settings?.secure_path,pa={getStats:()=>M.get(Bs+"/monitor/api/stats"),getOverride:()=>M.get(Bs+"/stat/getOverride"),getOrderStat:s=>M.get(Bs+"/stat/getOrder",{params:s}),getStatsData:()=>M.get(Bs+"/stat/getStats"),getNodeTrafficData:s=>M.get(Bs+"/stat/getTrafficRank",{params:s}),getServerLastRank:()=>M.get(Bs+"/stat/getServerLastRank"),getServerYesterdayRank:()=>M.get(Bs+"/stat/getServerYesterdayRank")},kt=window?.settings?.secure_path,Mt={getList:()=>M.get(kt+"/theme/getThemes"),getConfig:s=>M.post(kt+"/theme/getThemeConfig",{name:s}),updateConfig:(s,a)=>M.post(kt+"/theme/saveThemeConfig",{name:s,config:a}),upload:s=>{const a=new FormData;return a.append("file",s),M.post(kt+"/theme/upload",a,{headers:{"Content-Type":"multipart/form-data"}})},drop:s=>M.post(kt+"/theme/delete",{name:s})},dt=window?.settings?.secure_path,Js={getList:()=>M.get(dt+"/server/manage/getNodes"),save:s=>M.post(dt+"/server/manage/save",s),drop:s=>M.post(dt+"/server/manage/drop",s),copy:s=>M.post(dt+"/server/manage/copy",s),update:s=>M.post(dt+"/server/manage/update",s),sort:s=>M.post(dt+"/server/manage/sort",s)},$a=window?.settings?.secure_path,at={getList:()=>M.get($a+"/server/group/fetch"),save:s=>M.post($a+"/server/group/save",s),drop:s=>M.post($a+"/server/group/drop",s)},Aa=window?.settings?.secure_path,Sa={getList:()=>M.get(Aa+"/server/route/fetch"),save:s=>M.post(Aa+"/server/route/save",s),drop:s=>M.post(Aa+"/server/route/drop",s)},Gs=window?.settings?.secure_path,Qs={getList:()=>M.get(Gs+"/payment/fetch"),getMethodList:()=>M.get(Gs+"/payment/getPaymentMethods"),getMethodForm:s=>M.post(Gs+"/payment/getPaymentForm",s),save:s=>M.post(Gs+"/payment/save",s),drop:s=>M.post(Gs+"/payment/drop",s),updateStatus:s=>M.post(Gs+"/payment/show",s),sort:s=>M.post(Gs+"/payment/sort",s)},Tt=window?.settings?.secure_path,Ht={getList:()=>M.get(`${Tt}/notice/fetch`),save:s=>M.post(`${Tt}/notice/save`,s),drop:s=>M.post(`${Tt}/notice/drop`,{id:s}),updateStatus:s=>M.post(`${Tt}/notice/show`,{id:s}),sort:s=>M.post(`${Tt}/notice/sort`,{ids:s})},mt=window?.settings?.secure_path,vt={getList:()=>M.get(mt+"/knowledge/fetch"),getInfo:s=>M.get(mt+"/knowledge/fetch?id="+s),save:s=>M.post(mt+"/knowledge/save",s),drop:s=>M.post(mt+"/knowledge/drop",s),updateStatus:s=>M.post(mt+"/knowledge/show",s),sort:s=>M.post(mt+"/knowledge/sort",s)},Dt=window?.settings?.secure_path,es={getList:()=>M.get(Dt+"/plan/fetch"),save:s=>M.post(Dt+"/plan/save",s),update:s=>M.post(Dt+"/plan/update",s),drop:s=>M.post(Dt+"/plan/drop",s),sort:s=>M.post(Dt+"/plan/sort",{ids:s})},ut=window?.settings?.secure_path,Ys={getList:s=>M.post(ut+"/order/fetch",s),getInfo:s=>M.post(ut+"/order/detail",s),markPaid:s=>M.post(ut+"/order/paid",s),makeCancel:s=>M.post(ut+"/order/cancel",s),update:s=>M.post(ut+"/order/update",s),assign:s=>M.post(ut+"/order/assign",s)},Qt=window?.settings?.secure_path,ga={getList:s=>M.post(Qt+"/coupon/fetch",s),save:s=>M.post(Qt+"/coupon/generate",s),drop:s=>M.post(Qt+"/coupon/drop",s),update:s=>M.post(Qt+"/coupon/update",s)},Ns=window?.settings?.secure_path,ws={getList:s=>M.post(`${Ns}/user/fetch`,s),update:s=>M.post(`${Ns}/user/update`,s),resetSecret:s=>M.post(`${Ns}/user/resetSecret`,{id:s}),generate:s=>s.download_csv?M.post(`${Ns}/user/generate`,s,{responseType:"blob"}):M.post(`${Ns}/user/generate`,s),getStats:s=>M.post(`${Ns}/stat/getStatUser`,s),destroy:s=>M.post(`${Ns}/user/destroy`,{id:s}),sendMail:s=>M.post(`${Ns}/user/sendMail`,s),dumpCSV:s=>M.post(`${Ns}/user/dumpCSV`,s,{responseType:"blob"}),batchBan:s=>M.post(`${Ns}/user/ban`,s)},Xt=window?.settings?.secure_path,ft={getList:s=>M.post(Xt+"/ticket/fetch",s),getInfo:s=>M.get(Xt+"/ticket/fetch?id= "+s),reply:s=>M.post(Xt+"/ticket/reply",s),close:s=>M.post(Xt+"/ticket/close",{id:s})},os=window?.settings?.secure_path,me={getSettings:(s="")=>M.get(os+"/config/fetch?key="+s),saveSettings:s=>M.post(os+"/config/save",s),getEmailTemplate:()=>M.get(os+"/config/getEmailTemplate"),sendTestMail:()=>M.post(os+"/config/testSendMail"),setTelegramWebhook:()=>M.post(os+"/config/setTelegramWebhook"),updateSystemConfig:s=>M.post(os+"/config/save",s),getSystemStatus:()=>M.get(`${os}/system/getSystemStatus`),getQueueStats:()=>M.get(`${os}/system/getQueueStats`),getQueueWorkload:()=>M.get(`${os}/system/getQueueWorkload`),getQueueMasters:()=>M.get(`${os}/system/getQueueMasters`),getHorizonFailedJobs:s=>M.get(`${os}/system/getHorizonFailedJobs`,{params:s}),getSystemLog:s=>M.get(`${os}/system/getSystemLog`,{params:s})},Ds=window?.settings?.secure_path,Ps={getPluginList:()=>M.get(`${Ds}/plugin/getPlugins`),uploadPlugin:s=>{const a=new FormData;return a.append("file",s),M.post(`${Ds}/plugin/upload`,a,{headers:{"Content-Type":"multipart/form-data"}})},deletePlugin:s=>M.post(`${Ds}/plugin/delete`,{code:s}),installPlugin:s=>M.post(`${Ds}/plugin/install`,{code:s}),uninstallPlugin:s=>M.post(`${Ds}/plugin/uninstall`,{code:s}),enablePlugin:s=>M.post(`${Ds}/plugin/enable`,{code:s}),disablePlugin:s=>M.post(`${Ds}/plugin/disable`,{code:s}),getPluginConfig:s=>M.get(`${Ds}/plugin/config`,{params:{code:s}}),updatePluginConfig:(s,a)=>M.post(`${Ds}/plugin/config`,{code:s,config:a})};window?.settings?.secure_path;const td=x.object({subscribe_template_singbox:x.string().nullable(),subscribe_template_clash:x.string().nullable(),subscribe_template_clashmeta:x.string().nullable(),subscribe_template_stash:x.string().nullable(),subscribe_template_surge:x.string().nullable(),subscribe_template_surfboard:x.string().nullable()}),ad={subscribe_template_singbox:"",subscribe_template_clash:"",subscribe_template_clashmeta:"",subscribe_template_stash:"",subscribe_template_surge:"",subscribe_template_surfboard:""};function nd(){const{t:s}=V("settings"),[a,t]=m.useState(!1),l=m.useRef(null),[n,o]=m.useState("singbox"),r=ye({resolver:_e(td),defaultValues:ad,mode:"onBlur"}),{data:c}=ne({queryKey:["settings","client"],queryFn:()=>me.getSettings("subscribe_template")}),{mutateAsync:u}=gs({mutationFn:me.saveSettings,onSuccess:h=>{h.data&&A.success(s("common.autoSaved"))}});m.useEffect(()=>{if(c?.data.subscribe_template){const h=c.data.subscribe_template;Object.entries(h).forEach(([_,T])=>{r.setValue(_,T)}),l.current=h}},[c]),console.log(r.getValues());const i=m.useCallback(Se.debounce(async h=>{if(!Se.isEqual(h,l.current)){t(!0);try{await u(h),l.current=h}finally{t(!1)}}},1e3),[u]),d=m.useCallback(h=>{i(h)},[i]);return e.jsx(we,{...r,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs(Bt,{value:n,onValueChange:o,children:[e.jsxs(Ct,{children:[e.jsx(Ee,{value:"singbox",children:"Sing-box"}),e.jsx(Ee,{value:"clash",children:"Clash"}),e.jsx(Ee,{value:"clashmeta",children:"Clash Meta"}),e.jsx(Ee,{value:"stash",children:"Stash"}),e.jsx(Ee,{value:"surge",children:"Surge"}),e.jsx(Ee,{value:"surfboard",children:"Surfboard"})]}),e.jsx(We,{value:"singbox",children:e.jsx(v,{control:r.control,name:"subscribe_template_singbox",render:({field:h})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe_template.singbox.title")}),e.jsx(b,{children:e.jsx(ot,{height:"500px",defaultLanguage:"json",value:h.value||"",onChange:_=>{typeof _=="string"&&(h.onChange(_),d(r.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(F,{children:s("subscribe_template.singbox.description")}),e.jsx(P,{})]})})}),e.jsx(We,{value:"clash",children:e.jsx(v,{control:r.control,name:"subscribe_template_clash",render:({field:h})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe_template.clash.title")}),e.jsx(b,{children:e.jsx(ot,{height:"500px",defaultLanguage:"yaml",value:h.value||"",onChange:_=>{typeof _=="string"&&(h.onChange(_),d(r.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(F,{children:s("subscribe_template.clash.description")}),e.jsx(P,{})]})})}),e.jsx(We,{value:"clashmeta",children:e.jsx(v,{control:r.control,name:"subscribe_template_clashmeta",render:({field:h})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe_template.clashmeta.title")}),e.jsx(b,{children:e.jsx(ot,{height:"500px",defaultLanguage:"yaml",value:h.value||"",onChange:_=>{typeof _=="string"&&(h.onChange(_),d(r.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(F,{children:s("subscribe_template.clashmeta.description")}),e.jsx(P,{})]})})}),e.jsx(We,{value:"stash",children:e.jsx(v,{control:r.control,name:"subscribe_template_stash",render:({field:h})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe_template.stash.title")}),e.jsx(b,{children:e.jsx(ot,{height:"500px",defaultLanguage:"yaml",value:h.value||"",onChange:_=>{typeof _=="string"&&(h.onChange(_),d(r.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(F,{children:s("subscribe_template.stash.description")}),e.jsx(P,{})]})})}),e.jsx(We,{value:"surge",children:e.jsx(v,{control:r.control,name:"subscribe_template_surge",render:({field:h})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe_template.surge.title")}),e.jsx(b,{children:e.jsx(ot,{height:"500px",defaultLanguage:"ini",value:h.value||"",onChange:_=>{typeof _=="string"&&(h.onChange(_),d(r.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(F,{children:s("subscribe_template.surge.description")}),e.jsx(P,{})]})})}),e.jsx(We,{value:"surfboard",children:e.jsx(v,{control:r.control,name:"subscribe_template_surfboard",render:({field:h})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe_template.surfboard.title")}),e.jsx(b,{children:e.jsx(ot,{height:"500px",defaultLanguage:"ini",value:h.value||"",onChange:_=>{typeof _=="string"&&(h.onChange(_),d(r.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(F,{children:s("subscribe_template.surfboard.description")}),e.jsx(P,{})]})})})]}),a&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("common.saving")})]})})}function ld(){const{t:s}=V("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("subscribe_template.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("subscribe_template.description")})]}),e.jsx(ke,{}),e.jsx(nd,{})]})}const rd=()=>e.jsx(Gc,{children:e.jsx(dn,{})}),id=vi([{path:"/sign-in",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>wd);return{default:s}},void 0,import.meta.url)).default})},{element:e.jsx(rd,{}),children:[{path:"/",lazy:async()=>({Component:(await fe(()=>Promise.resolve().then(()=>Id),void 0,import.meta.url)).default}),errorElement:e.jsx(ct,{}),children:[{index:!0,lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>Zd);return{default:s}},void 0,import.meta.url)).default})},{path:"config",errorElement:e.jsx(ct,{}),children:[{path:"system",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>am);return{default:s}},void 0,import.meta.url)).default}),children:[{index:!0,lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>im);return{default:s}},void 0,import.meta.url)).default})},{path:"safe",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>um);return{default:s}},void 0,import.meta.url)).default})},{path:"subscribe",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>fm);return{default:s}},void 0,import.meta.url)).default})},{path:"invite",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>Nm);return{default:s}},void 0,import.meta.url)).default})},{path:"frontend",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>km);return{default:s}},void 0,import.meta.url)).default})},{path:"server",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>Rm);return{default:s}},void 0,import.meta.url)).default})},{path:"email",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>Mm);return{default:s}},void 0,import.meta.url)).default})},{path:"telegram",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>qm);return{default:s}},void 0,import.meta.url)).default})},{path:"APP",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>Gm);return{default:s}},void 0,import.meta.url)).default})},{path:"subscribe-template",element:e.jsx(ld,{})}]},{path:"payment",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>eu);return{default:s}},void 0,import.meta.url)).default})},{path:"plugin",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>au);return{default:s}},void 0,import.meta.url)).default})},{path:"theme",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>iu);return{default:s}},void 0,import.meta.url)).default})},{path:"notice",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>hu);return{default:s}},void 0,import.meta.url)).default})},{path:"knowledge",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>Nu);return{default:s}},void 0,import.meta.url)).default})}]},{path:"server",errorElement:e.jsx(ct,{}),children:[{path:"manage",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>Xu);return{default:s}},void 0,import.meta.url)).default})},{path:"group",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>ax);return{default:s}},void 0,import.meta.url)).default})},{path:"route",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>cx);return{default:s}},void 0,import.meta.url)).default})}]},{path:"finance",errorElement:e.jsx(ct,{}),children:[{path:"plan",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>jx);return{default:s}},void 0,import.meta.url)).default})},{path:"order",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>Rx);return{default:s}},void 0,import.meta.url)).default})},{path:"coupon",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>Ax);return{default:s}},void 0,import.meta.url)).default})}]},{path:"user",errorElement:e.jsx(ct,{}),children:[{path:"manage",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>fh);return{default:s}},void 0,import.meta.url)).default})},{path:"ticket",lazy:async()=>({Component:(await fe(async()=>{const{default:s}=await Promise.resolve().then(()=>Mh);return{default:s}},void 0,import.meta.url)).default})}]}]}]},{path:"/500",Component:ct},{path:"/404",Component:Kn},{path:"/503",Component:$c},{path:"*",Component:Kn}]);function od(){return M.get("/user/info")}const qa={token:qt()?.value||"",userInfo:null,isLoggedIn:!!qt()?.value,loading:!1,error:null},Ot=bi("user/fetchUserInfo",async()=>(await od()).data,{condition:(s,{getState:a})=>{const{user:t}=a();return!!t.token&&!t.loading}}),mr=yi({name:"user",initialState:qa,reducers:{setToken(s,a){s.token=a.payload,s.isLoggedIn=!!a.payload},resetUserState:()=>qa},extraReducers:s=>{s.addCase(Ot.pending,a=>{a.loading=!0,a.error=null}).addCase(Ot.fulfilled,(a,t)=>{a.loading=!1,a.userInfo=t.payload,a.error=null}).addCase(Ot.rejected,(a,t)=>{if(a.loading=!1,a.error=t.error.message||"Failed to fetch user info",!a.token)return qa})}}),{setToken:cd,resetUserState:dd}=mr.actions,md=s=>s.user.userInfo,ud=mr.reducer,ur=Ni({reducer:{user:ud}});qt()?.value&&ur.dispatch(Ot());_i.use(wi).use(Ci).init({resources:{"en-US":window.XBOARD_TRANSLATIONS?.["en-US"]||{},"zh-CN":window.XBOARD_TRANSLATIONS?.["zh-CN"]||{},"ko-KR":window.XBOARD_TRANSLATIONS?.["ko-KR"]||{}},fallbackLng:"zh-CN",supportedLngs:["en-US","zh-CN","ko-KR"],detection:{order:["querystring","localStorage","navigator"],lookupQuerystring:"lang",lookupLocalStorage:"i18nextLng",caches:["localStorage"]},interpolation:{escapeValue:!1}});const xd=new Si;ki.createRoot(document.getElementById("root")).render(e.jsx(Ti.StrictMode,{children:e.jsx(Di,{client:xd,children:e.jsx(Pi,{store:ur,children:e.jsxs(Fc,{defaultTheme:"light",storageKey:"vite-ui-theme",children:[e.jsx(Ei,{router:id}),e.jsx(Ri,{richColors:!0,position:"top-right"})]})})})}));const Ye=m.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,className:y("rounded-xl border bg-card text-card-foreground shadow",s),...a}));Ye.displayName="Card";const ss=m.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,className:y("flex flex-col space-y-1.5 p-6",s),...a}));ss.displayName="CardHeader";const _s=m.forwardRef(({className:s,...a},t)=>e.jsx("h3",{ref:t,className:y("font-semibold leading-none tracking-tight",s),...a}));_s.displayName="CardTitle";const Xs=m.forwardRef(({className:s,...a},t)=>e.jsx("p",{ref:t,className:y("text-sm text-muted-foreground",s),...a}));Xs.displayName="CardDescription";const ts=m.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,className:y("p-6 pt-0",s),...a}));ts.displayName="CardContent";const hd=m.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,className:y("flex items-center p-6 pt-0",s),...a}));hd.displayName="CardFooter";const D=m.forwardRef(({className:s,type:a,...t},l)=>e.jsx("input",{type:a,className:y("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",s),ref:l,...t}));D.displayName="Input";const xr=m.forwardRef(({className:s,...a},t)=>{const[l,n]=m.useState(!1);return e.jsxs("div",{className:"relative rounded-md",children:[e.jsx("input",{type:l?"text":"password",className:y("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...a}),e.jsx(E,{type:"button",size:"icon",variant:"ghost",className:"absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2 rounded-md text-muted-foreground",onClick:()=>n(o=>!o),children:l?e.jsx(Ii,{size:18}):e.jsx(Li,{size:18})})]})});xr.displayName="PasswordInput";const pd=s=>M.post("/passport/auth/login",s);function gd({className:s,onForgotPassword:a,...t}){const l=Is(),n=il(),{t:o}=V("auth"),r=x.object({email:x.string().min(1,{message:o("signIn.validation.emailRequired")}),password:x.string().min(1,{message:o("signIn.validation.passwordRequired")}).min(7,{message:o("signIn.validation.passwordLength")})}),c=ye({resolver:_e(r),defaultValues:{email:"",password:""}});async function u(i){try{const{data:d}=await pd(i);sd(d.auth_data),n(cd(d.auth_data)),await n(Ot()).unwrap(),l("/")}catch(d){console.error("Login failed:",d),d.response?.data?.message&&c.setError("root",{message:d.response.data.message})}}return e.jsx("div",{className:y("grid gap-6",s),...t,children:e.jsx(we,{...c,children:e.jsx("form",{onSubmit:c.handleSubmit(u),className:"space-y-4",children:e.jsxs("div",{className:"space-y-4",children:[c.formState.errors.root&&e.jsx("div",{className:"text-sm text-destructive",children:c.formState.errors.root.message}),e.jsx(v,{control:c.control,name:"email",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:o("signIn.email")}),e.jsx(b,{children:e.jsx(D,{placeholder:o("signIn.emailPlaceholder"),autoComplete:"email",...i})}),e.jsx(P,{})]})}),e.jsx(v,{control:c.control,name:"password",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:o("signIn.password")}),e.jsx(b,{children:e.jsx(xr,{placeholder:o("signIn.passwordPlaceholder"),autoComplete:"current-password",...i})}),e.jsx(P,{})]})}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(E,{variant:"link",type:"button",className:"px-0 text-sm font-normal text-muted-foreground hover:text-primary",onClick:a,children:o("signIn.forgotPassword")})}),e.jsx(E,{className:"w-full",size:"lg",loading:c.formState.isSubmitting,children:o("signIn.submit")})]})})})})}const pe=ol,rs=cl,fd=dl,qs=mn,hr=m.forwardRef(({className:s,...a},t)=>e.jsx(ja,{ref:t,className:y("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...a}));hr.displayName=ja.displayName;const ue=m.forwardRef(({className:s,children:a,...t},l)=>e.jsxs(fd,{children:[e.jsx(hr,{}),e.jsxs(va,{ref:l,className:y("max-h-[95%] overflow-auto fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...t,children:[a,e.jsxs(mn,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[e.jsx(ds,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));ue.displayName=va.displayName;const ve=({className:s,...a})=>e.jsx("div",{className:y("flex flex-col space-y-1.5 text-center sm:text-left",s),...a});ve.displayName="DialogHeader";const Re=({className:s,...a})=>e.jsx("div",{className:y("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a});Re.displayName="DialogFooter";const ge=m.forwardRef(({className:s,...a},t)=>e.jsx(ba,{ref:t,className:y("text-lg font-semibold leading-none tracking-tight",s),...a}));ge.displayName=ba.displayName;const Le=m.forwardRef(({className:s,...a},t)=>e.jsx(ya,{ref:t,className:y("text-sm text-muted-foreground",s),...a}));Le.displayName=ya.displayName;const bt=Zs("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),G=m.forwardRef(({className:s,variant:a,size:t,asChild:l=!1,...n},o)=>{const r=l?on:"button";return e.jsx(r,{className:y(bt({variant:a,size:t,className:s})),ref:o,...n})});G.displayName="Button";const Es=Mi,Rs=Oi,jd=zi,vd=m.forwardRef(({className:s,inset:a,children:t,...l},n)=>e.jsxs(ml,{ref:n,className:y("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a&&"pl-8",s),...l,children:[t,e.jsx(un,{className:"ml-auto h-4 w-4"})]}));vd.displayName=ml.displayName;const bd=m.forwardRef(({className:s,...a},t)=>e.jsx(ul,{ref:t,className:y("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...a}));bd.displayName=ul.displayName;const Cs=m.forwardRef(({className:s,sideOffset:a=4,...t},l)=>e.jsx(Vi,{children:e.jsx(xl,{ref:l,sideOffset:a,className:y("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...t})}));Cs.displayName=xl.displayName;const Ne=m.forwardRef(({className:s,inset:a,...t},l)=>e.jsx(hl,{ref:l,className:y("relative flex cursor-default cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a&&"pl-8",s),...t}));Ne.displayName=hl.displayName;const yd=m.forwardRef(({className:s,children:a,checked:t,...l},n)=>e.jsxs(pl,{ref:n,className:y("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:t,...l,children:[e.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(gl,{children:e.jsx(et,{className:"h-4 w-4"})})}),a]}));yd.displayName=pl.displayName;const Nd=m.forwardRef(({className:s,children:a,...t},l)=>e.jsxs(fl,{ref:l,className:y("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...t,children:[e.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(gl,{children:e.jsx(Fi,{className:"h-4 w-4 fill-current"})})}),a]}));Nd.displayName=fl.displayName;const vn=m.forwardRef(({className:s,inset:a,...t},l)=>e.jsx(jl,{ref:l,className:y("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",s),...t}));vn.displayName=jl.displayName;const yt=m.forwardRef(({className:s,...a},t)=>e.jsx(vl,{ref:t,className:y("-mx-1 my-1 h-px bg-muted",s),...a}));yt.displayName=vl.displayName;const ln=({className:s,...a})=>e.jsx("span",{className:y("ml-auto text-xs tracking-widest opacity-60",s),...a});ln.displayName="DropdownMenuShortcut";const Ha=[{code:"en-US",name:"English",flag:$i,shortName:"EN"},{code:"zh-CN",name:"中文",flag:Ai,shortName:"CN"},{code:"ko-KR",name:"한국어",flag:qi,shortName:"KR"}];function pr(){const{i18n:s}=V(),a=n=>{s.changeLanguage(n)},t=Ha.find(n=>n.code===s.language)||Ha[1],l=t.flag;return e.jsxs(Es,{children:[e.jsx(Rs,{asChild:!0,children:e.jsxs(G,{variant:"ghost",size:"sm",className:"h-8 px-2 gap-1",children:[e.jsx(l,{className:"h-4 w-5 rounded-sm shadow-sm"}),e.jsx("span",{className:"text-sm font-medium",children:t.shortName})]})}),e.jsx(Cs,{align:"end",className:"w-[120px]",children:Ha.map(n=>{const o=n.flag,r=n.code===s.language;return e.jsxs(Ne,{onClick:()=>a(n.code),className:y("flex items-center gap-2 px-2 py-1.5 cursor-pointer",r&&"bg-accent"),children:[e.jsx(o,{className:"h-4 w-5 rounded-sm shadow-sm"}),e.jsx("span",{className:y("text-sm",r&&"font-medium"),children:n.name})]},n.code)})})]})}function _d(){const[s,a]=m.useState(!1),{t}=V("auth"),l=t("signIn.resetPassword.command");return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"container relative flex min-h-svh flex-col items-center justify-center bg-primary-foreground px-4 py-8 lg:max-w-none lg:px-0",children:[e.jsx("div",{className:"absolute right-4 top-4 md:right-8 md:top-8",children:e.jsx(pr,{})}),e.jsxs("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px] md:w-[420px] lg:p-8",children:[e.jsxs("div",{className:"flex flex-col space-y-2 text-center",children:[e.jsx("h1",{className:"text-2xl font-bold sm:text-3xl",children:window?.settings?.title}),e.jsx("p",{className:"text-sm text-muted-foreground",children:window?.settings?.description})]}),e.jsxs(Ye,{className:"p-4 sm:p-6",children:[e.jsxs("div",{className:"flex flex-col space-y-2 text-left",children:[e.jsx("h1",{className:"text-xl font-semibold tracking-tight sm:text-2xl",children:t("signIn.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:t("signIn.description")})]}),e.jsx(gd,{onForgotPassword:()=>a(!0)})]})]})]}),e.jsx(pe,{open:s,onOpenChange:a,children:e.jsx(ue,{className:"max-w-[90vw] sm:max-w-lg",children:e.jsxs(ve,{children:[e.jsx(ge,{children:t("signIn.resetPassword.title")}),e.jsx(Le,{children:t("signIn.resetPassword.description")}),e.jsx("div",{className:"mt-4",children:e.jsxs("div",{className:"relative",children:[e.jsx("pre",{className:"max-w-full overflow-x-auto rounded-md bg-secondary p-4 pr-12 text-sm",children:l}),e.jsx(G,{variant:"ghost",size:"icon",className:"absolute right-2 top-2 h-8 w-8 hover:bg-secondary-foreground/10",onClick:()=>ha(l).then(()=>{A.success(t("common:copy.success"))}),children:e.jsx(Hi,{className:"h-4 w-4"})})]})})]})})})]})}const wd=Object.freeze(Object.defineProperty({__proto__:null,default:_d},Symbol.toStringTag,{value:"Module"})),Ve=m.forwardRef(({className:s,fadedBelow:a=!1,fixedHeight:t=!1,...l},n)=>e.jsx("div",{ref:n,className:y("relative flex h-full w-full flex-col",a&&"after:pointer-events-none after:absolute after:bottom-0 after:left-0 after:hidden after:h-32 after:w-full after:bg-[linear-gradient(180deg,_transparent_10%,_hsl(var(--background))_70%)] after:md:block",t&&"md:h-svh",s),...l}));Ve.displayName="Layout";const Fe=m.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,className:y("flex h-[var(--header-height)] flex-none items-center gap-4 bg-background p-4 md:px-8",s),...a}));Fe.displayName="LayoutHeader";const Ae=m.forwardRef(({className:s,fixedHeight:a,...t},l)=>e.jsx("div",{ref:l,className:y("flex-1 overflow-hidden px-4 py-6 md:px-8",a&&"h-[calc(100%-var(--header-height))]",s),...t}));Ae.displayName="LayoutBody";const gr=Ui,fr=Ki,jr=Bi,be=Gi,xe=Wi,he=Yi,de=m.forwardRef(({className:s,sideOffset:a=4,...t},l)=>e.jsx(bl,{ref:l,sideOffset:a,className:y("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...t}));de.displayName=bl.displayName;function ka(){const{pathname:s}=cn();return{checkActiveNav:t=>{if(t==="/"&&s==="/")return!0;const l=t.replace(/^\//,""),n=s.replace(/^\//,"");return l?n.startsWith(l):!1}}}function vr({key:s,defaultValue:a}){const[t,l]=m.useState(()=>{const n=localStorage.getItem(s);return n!==null?JSON.parse(n):a});return m.useEffect(()=>{localStorage.setItem(s,JSON.stringify(t))},[t,s]),[t,l]}function Cd(){const[s,a]=vr({key:"collapsed-sidebar-items",defaultValue:[]}),t=n=>!s.includes(n);return{isExpanded:t,toggleItem:n=>{t(n)?a([...s,n]):a(s.filter(o=>o!==n))}}}function Sd({links:s,isCollapsed:a,className:t,closeNav:l}){const{t:n}=V(),o=({sub:r,...c})=>{const u=`${n(c.title)}-${c.href}`;return a&&r?m.createElement(Dd,{...c,sub:r,key:u,closeNav:l}):a?m.createElement(Td,{...c,key:u,closeNav:l}):r?m.createElement(kd,{...c,sub:r,key:u,closeNav:l}):m.createElement(br,{...c,key:u,closeNav:l})};return e.jsx("div",{"data-collapsed":a,className:y("group border-b bg-background py-2 transition-[max-height,padding] duration-500 data-[collapsed=true]:py-2 md:border-none",t),children:e.jsx(be,{delayDuration:0,children:e.jsx("nav",{className:"grid gap-1 group-[[data-collapsed=true]]:justify-center group-[[data-collapsed=true]]:px-2",children:s.map(o)})})})}function br({title:s,icon:a,label:t,href:l,closeNav:n,subLink:o=!1}){const{checkActiveNav:r}=ka(),{t:c}=V();return e.jsxs(st,{to:l,onClick:n,className:y(wt({variant:r(l)?"secondary":"ghost",size:"sm"}),"h-12 justify-start text-wrap rounded-none px-6",o&&"h-10 w-full border-l border-l-slate-500 px-2"),"aria-current":r(l)?"page":void 0,children:[e.jsx("div",{className:"mr-2",children:a}),c(s),t&&e.jsx("div",{className:"ml-2 rounded-lg bg-primary px-1 text-[0.625rem] text-primary-foreground",children:c(t)})]})}function kd({title:s,icon:a,label:t,sub:l,closeNav:n}){const{checkActiveNav:o}=ka(),{isExpanded:r,toggleItem:c}=Cd(),{t:u}=V(),i=!!l?.find(_=>o(_.href)),d=u(s),h=r(d)||i;return e.jsxs(gr,{open:h,onOpenChange:()=>c(d),children:[e.jsxs(fr,{className:y(wt({variant:i?"secondary":"ghost",size:"sm"}),"group h-12 w-full justify-start rounded-none px-6"),children:[e.jsx("div",{className:"mr-2",children:a}),u(s),t&&e.jsx("div",{className:"ml-2 rounded-lg bg-primary px-1 text-[0.625rem] text-primary-foreground",children:u(t)}),e.jsx("span",{className:y('ml-auto transition-all group-data-[state="open"]:-rotate-180'),children:e.jsx(yl,{stroke:1})})]}),e.jsx(jr,{className:"collapsibleDropdown",asChild:!0,children:e.jsx("ul",{children:l.map(_=>e.jsx("li",{className:"my-1 ml-8",children:e.jsx(br,{..._,subLink:!0,closeNav:n})},u(_.title)))})})]})}function Td({title:s,icon:a,label:t,href:l,closeNav:n}){const{checkActiveNav:o}=ka(),{t:r}=V();return e.jsxs(xe,{delayDuration:0,children:[e.jsx(he,{asChild:!0,children:e.jsxs(st,{to:l,onClick:n,className:y(wt({variant:o(l)?"secondary":"ghost",size:"icon"}),"h-12 w-12"),children:[a,e.jsx("span",{className:"sr-only",children:r(s)})]})}),e.jsxs(de,{side:"right",className:"flex items-center gap-4",children:[r(s),t&&e.jsx("span",{className:"ml-auto text-muted-foreground",children:r(t)})]})]})}function Dd({title:s,icon:a,label:t,sub:l,closeNav:n}){const{checkActiveNav:o}=ka(),{t:r}=V(),c=!!l?.find(u=>o(u.href));return e.jsxs(Es,{children:[e.jsxs(xe,{delayDuration:0,children:[e.jsx(he,{asChild:!0,children:e.jsx(Rs,{asChild:!0,children:e.jsx(E,{variant:c?"secondary":"ghost",size:"icon",className:"h-12 w-12",children:a})})}),e.jsxs(de,{side:"right",className:"flex items-center gap-4",children:[r(s)," ",t&&e.jsx("span",{className:"ml-auto text-muted-foreground",children:r(t)}),e.jsx(yl,{size:18,className:"-rotate-90 text-muted-foreground"})]})]}),e.jsxs(Cs,{side:"right",align:"start",sideOffset:4,children:[e.jsxs(vn,{children:[r(s)," ",t?`(${r(t)})`:""]}),e.jsx(yt,{}),l.map(({title:u,icon:i,label:d,href:h})=>e.jsx(Ne,{asChild:!0,children:e.jsxs(st,{to:h,onClick:n,className:`${o(h)?"bg-secondary":""}`,children:[i," ",e.jsx("span",{className:"ml-2 max-w-52 text-wrap",children:r(u)}),d&&e.jsx("span",{className:"ml-auto text-xs",children:r(d)})]})},`${r(u)}-${h}`))]})]})}const yr=[{title:"nav:dashboard",label:"",href:"/",icon:e.jsx(Ji,{size:18})},{title:"nav:systemManagement",label:"",href:"",icon:e.jsx(Qi,{size:18}),sub:[{title:"nav:systemConfig",label:"",href:"/config/system",icon:e.jsx(Nl,{size:18})},{title:"nav:pluginManagement",label:"",href:"/config/plugin",icon:e.jsx(xn,{size:18})},{title:"nav:themeConfig",label:"",href:"/config/theme",icon:e.jsx(Xi,{size:18})},{title:"nav:noticeManagement",label:"",href:"/config/notice",icon:e.jsx(Zi,{size:18})},{title:"nav:paymentConfig",label:"",href:"/config/payment",icon:e.jsx(Fn,{size:18})},{title:"nav:knowledgeManagement",label:"",href:"/config/knowledge",icon:e.jsx(eo,{size:18})}]},{title:"nav:nodeManagement",label:"",href:"",icon:e.jsx(_l,{size:18}),sub:[{title:"nav:nodeManagement",label:"",href:"/server/manage",icon:e.jsx(so,{size:18})},{title:"nav:permissionGroupManagement",label:"",href:"/server/group",icon:e.jsx(wl,{size:18})},{title:"nav:routeManagement",label:"",href:"/server/route",icon:e.jsx(to,{size:18})}]},{title:"nav:subscriptionManagement",label:"",href:"",icon:e.jsx(ao,{size:18}),sub:[{title:"nav:planManagement",label:"",href:"/finance/plan",icon:e.jsx(no,{size:18})},{title:"nav:orderManagement",label:"",href:"/finance/order",icon:e.jsx(Fn,{size:18})},{title:"nav:couponManagement",label:"",href:"/finance/coupon",icon:e.jsx(lo,{size:18})}]},{title:"nav:userManagement",label:"",href:"",icon:e.jsx(ro,{size:18}),sub:[{title:"nav:userManagement",label:"",href:"/user/manage",icon:e.jsx(io,{size:18})},{title:"nav:ticketManagement",label:"",href:"/user/ticket",icon:e.jsx(Cl,{size:18})}]}];function Pd({className:s,isCollapsed:a,setIsCollapsed:t}){const[l,n]=m.useState(!1),{t:o}=V();return m.useEffect(()=>{l?document.body.classList.add("overflow-hidden"):document.body.classList.remove("overflow-hidden")},[l]),e.jsxs("aside",{className:y(`fixed left-0 right-0 top-0 z-50 flex h-auto flex-col border-r-2 border-r-muted transition-[width] md:bottom-0 md:right-auto md:h-svh ${a?"md:w-14":"md:w-64"}`,s),children:[e.jsx("div",{onClick:()=>n(!1),className:`absolute inset-0 transition-[opacity] delay-100 duration-700 ${l?"h-svh opacity-50":"h-0 opacity-0"} w-full bg-black md:hidden`}),e.jsxs(Ve,{className:`flex h-full flex-col  ${l?"h-[100vh] md:h-full":""}`,children:[e.jsxs(Fe,{className:"sticky top-0 justify-between px-4 py-3 shadow md:px-4",children:[e.jsxs("div",{className:`flex items-center ${a?"":"gap-2"}`,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",className:`transition-all ${a?"h-6 w-6":"h-8 w-8"}`,children:[e.jsx("rect",{width:"256",height:"256",fill:"none"}),e.jsx("line",{x1:"208",y1:"128",x2:"128",y2:"208",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),e.jsx("line",{x1:"192",y1:"40",x2:"40",y2:"192",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),e.jsx("span",{className:"sr-only",children:"Website Name"})]}),e.jsx("div",{className:`flex flex-col justify-end truncate ${a?"invisible w-0":"visible w-auto"}`,children:e.jsx("span",{className:"font-medium",children:window?.settings?.title})})]}),e.jsx(E,{variant:"ghost",size:"icon",className:"md:hidden","aria-label":o("common:toggleNavigation"),"aria-controls":"sidebar-menu","aria-expanded":l,onClick:()=>n(r=>!r),children:l?e.jsx(oo,{}):e.jsx(co,{})})]}),e.jsx(Sd,{id:"sidebar-menu",className:y("flex-1 overflow-auto overscroll-contain",l?"block":"hidden md:block","md:py-2"),closeNav:()=>n(!1),isCollapsed:a,links:yr}),e.jsx("div",{className:y("border-t border-border/50 bg-background","px-4 py-2.5 text-xs text-muted-foreground",l?"block":"hidden md:block",a?"text-center":"text-left"),children:e.jsxs("div",{className:y("flex items-center gap-1.5",a?"justify-center":"justify-start"),children:[e.jsx("div",{className:"h-1.5 w-1.5 rounded-full bg-green-500"}),e.jsxs("span",{className:y("whitespace-nowrap tracking-wide","transition-opacity duration-200",a&&"md:opacity-0"),children:["v",window?.settings?.version]})]})}),e.jsx(E,{onClick:()=>t(r=>!r),size:"icon",variant:"outline",className:"absolute -right-5 top-1/2 hidden rounded-full md:inline-flex","aria-label":o("common:toggleSidebar"),children:e.jsx(mo,{stroke:1.5,className:`h-5 w-5 ${a?"rotate-180":""}`})})]})]})}function Ed(){const[s,a]=vr({key:"collapsed-sidebar",defaultValue:!1});return m.useEffect(()=>{const t=()=>{a(window.innerWidth<768?!1:s)};return t(),window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}},[s,a]),[s,a]}function Rd(){const[s,a]=Ed();return e.jsxs("div",{className:"relative h-full overflow-hidden bg-background",children:[e.jsx(Pd,{isCollapsed:s,setIsCollapsed:a}),e.jsx("main",{id:"content",className:`overflow-x-hidden pt-16 transition-[margin] md:overflow-y-hidden md:pt-0 ${s?"md:ml-14":"md:ml-64"} h-full`,children:e.jsx(dn,{})})]})}const Id=Object.freeze(Object.defineProperty({__proto__:null,default:Rd},Symbol.toStringTag,{value:"Module"})),Us=m.forwardRef(({className:s,...a},t)=>e.jsx(He,{ref:t,className:y("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",s),...a}));Us.displayName=He.displayName;const Ld=({children:s,...a})=>e.jsx(pe,{...a,children:e.jsx(ue,{className:"overflow-hidden p-0",children:e.jsx(Us,{className:"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:s})})}),nt=m.forwardRef(({className:s,...a},t)=>e.jsxs("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[e.jsx(uo,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),e.jsx(He.Input,{ref:t,className:y("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",s),...a})]}));nt.displayName=He.Input.displayName;const Ks=m.forwardRef(({className:s,...a},t)=>e.jsx(He.List,{ref:t,className:y("max-h-[300px] overflow-y-auto overflow-x-hidden",s),...a}));Ks.displayName=He.List.displayName;const lt=m.forwardRef((s,a)=>e.jsx(He.Empty,{ref:a,className:"py-6 text-center text-sm",...s}));lt.displayName=He.Empty.displayName;const as=m.forwardRef(({className:s,...a},t)=>e.jsx(He.Group,{ref:t,className:y("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",s),...a}));as.displayName=He.Group.displayName;const St=m.forwardRef(({className:s,...a},t)=>e.jsx(He.Separator,{ref:t,className:y("-mx-1 h-px bg-border",s),...a}));St.displayName=He.Separator.displayName;const $e=m.forwardRef(({className:s,...a},t)=>e.jsx(He.Item,{ref:t,className:y("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...a}));$e.displayName=He.Item.displayName;function Vd(){const s=[];for(const a of yr)if(a.href&&s.push(a),a.sub)for(const t of a.sub)s.push({...t,parent:a.title});return s}function Xe(){const[s,a]=m.useState(!1),t=Is(),l=Vd(),{t:n}=V("search"),{t:o}=V("nav");m.useEffect(()=>{const c=u=>{u.key==="k"&&(u.metaKey||u.ctrlKey)&&(u.preventDefault(),a(i=>!i))};return document.addEventListener("keydown",c),()=>document.removeEventListener("keydown",c)},[]);const r=m.useCallback(c=>{a(!1),t(c)},[t]);return e.jsxs(e.Fragment,{children:[e.jsxs(G,{variant:"outline",className:"relative h-9 w-9 p-0 xl:h-10 xl:w-60 xl:justify-start xl:px-3 xl:py-2",onClick:()=>a(!0),children:[e.jsx(hn,{className:"h-4 w-4 xl:mr-2"}),e.jsx("span",{className:"hidden xl:inline-flex",children:n("placeholder")}),e.jsx("span",{className:"sr-only",children:n("shortcut.label")}),e.jsx("kbd",{className:"pointer-events-none absolute right-1.5 top-2 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 xl:flex",children:n("shortcut.key")})]}),e.jsxs(Ld,{open:s,onOpenChange:a,children:[e.jsx(nt,{placeholder:n("placeholder")}),e.jsxs(Ks,{children:[e.jsx(lt,{children:n("noResults")}),e.jsx(as,{heading:n("title"),children:l.map(c=>e.jsxs($e,{value:`${c.parent?c.parent+" ":""}${c.title}`,onSelect:()=>r(c.href),children:[e.jsx("div",{className:"mr-2",children:c.icon}),e.jsx("span",{children:o(c.title)}),c.parent&&e.jsx("span",{className:"ml-2 text-xs text-muted-foreground",children:o(c.parent)})]},c.href))})]})]})]})}function Ue(){const{theme:s,setTheme:a}=Mc();return m.useEffect(()=>{const t=s==="dark"?"#020817":"#fff",l=document.querySelector("meta[name='theme-color']");l&&l.setAttribute("content",t)},[s]),e.jsxs(e.Fragment,{children:[e.jsx(E,{size:"icon",variant:"ghost",className:"rounded-full",onClick:()=>a(s==="light"?"dark":"light"),children:s==="light"?e.jsx(xo,{size:20}):e.jsx(ho,{size:20})}),e.jsx(pr,{})]})}const Nr=m.forwardRef(({className:s,...a},t)=>e.jsx(Sl,{ref:t,className:y("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...a}));Nr.displayName=Sl.displayName;const _r=m.forwardRef(({className:s,...a},t)=>e.jsx(kl,{ref:t,className:y("aspect-square h-full w-full",s),...a}));_r.displayName=kl.displayName;const wr=m.forwardRef(({className:s,...a},t)=>e.jsx(Tl,{ref:t,className:y("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...a}));wr.displayName=Tl.displayName;function Ke(){const s=Is(),a=il(),t=po(md),{t:l}=V(["common"]),n=()=>{ir(),a(dd()),s("/sign-in")},o=t?.email?.split("@")[0]||l("common:user"),r=o.substring(0,2).toUpperCase();return e.jsxs(Es,{children:[e.jsx(Rs,{asChild:!0,children:e.jsx(E,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:e.jsxs(Nr,{className:"h-8 w-8",children:[e.jsx(_r,{src:t?.avatar_url,alt:o}),e.jsx(wr,{children:r})]})})}),e.jsxs(Cs,{className:"w-56",align:"end",forceMount:!0,children:[e.jsx(vn,{className:"font-normal",children:e.jsxs("div",{className:"flex flex-col space-y-1",children:[e.jsx("p",{className:"text-sm font-medium leading-none",children:o}),e.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:t?.email||l("common:defaultEmail")})]})}),e.jsx(yt,{}),e.jsx(Ne,{asChild:!0,children:e.jsxs(st,{to:"/config/system",children:[l("common:settings"),e.jsx(ln,{children:"⌘S"})]})}),e.jsx(yt,{}),e.jsxs(Ne,{onClick:n,children:[l("common:logout"),e.jsx(ln,{children:"⇧⌘Q"})]})]})]})}const X=go,Be=wo,Z=fo,Y=m.forwardRef(({className:s,children:a,...t},l)=>e.jsxs(Dl,{ref:l,className:y("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...t,children:[a,e.jsx(jo,{asChild:!0,children:e.jsx(pn,{className:"h-4 w-4 opacity-50"})})]}));Y.displayName=Dl.displayName;const Cr=m.forwardRef(({className:s,...a},t)=>e.jsx(Pl,{ref:t,className:y("flex cursor-default items-center justify-center py-1",s),...a,children:e.jsx(vo,{className:"h-4 w-4"})}));Cr.displayName=Pl.displayName;const Sr=m.forwardRef(({className:s,...a},t)=>e.jsx(El,{ref:t,className:y("flex cursor-default items-center justify-center py-1",s),...a,children:e.jsx(pn,{className:"h-4 w-4"})}));Sr.displayName=El.displayName;const J=m.forwardRef(({className:s,children:a,position:t="popper",...l},n)=>e.jsx(bo,{children:e.jsxs(Rl,{ref:n,className:y("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:t,...l,children:[e.jsx(Cr,{}),e.jsx(yo,{className:y("p-1",t==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),e.jsx(Sr,{})]})}));J.displayName=Rl.displayName;const Fd=m.forwardRef(({className:s,...a},t)=>e.jsx(Il,{ref:t,className:y("px-2 py-1.5 text-sm font-semibold",s),...a}));Fd.displayName=Il.displayName;const $=m.forwardRef(({className:s,children:a,...t},l)=>e.jsxs(Ll,{ref:l,className:y("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...t,children:[e.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(No,{children:e.jsx(et,{className:"h-4 w-4"})})}),e.jsx(_o,{children:a})]}));$.displayName=Ll.displayName;const Md=m.forwardRef(({className:s,...a},t)=>e.jsx(Vl,{ref:t,className:y("-mx-1 my-1 h-px bg-muted",s),...a}));Md.displayName=Vl.displayName;function rt({className:s,classNames:a,showOutsideDays:t=!0,...l}){return e.jsx(Co,{showOutsideDays:t,className:y("p-3",s),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:y(bt({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:y("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md",l.mode==="range"?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:y(bt({variant:"ghost"}),"h-8 w-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start",day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:({className:n,...o})=>e.jsx(Fl,{className:y("h-4 w-4",n),...o}),IconRight:({className:n,...o})=>e.jsx(un,{className:y("h-4 w-4",n),...o})},...l})}rt.displayName="Calendar";const Ss=ko,ks=To,bs=m.forwardRef(({className:s,align:a="center",sideOffset:t=4,...l},n)=>e.jsx(So,{children:e.jsx(Ml,{ref:n,align:a,sideOffset:t,className:y("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...l})}));bs.displayName=Ml.displayName;const zs={income:{main:"hsl(var(--primary))",gradient:{start:"hsl(var(--primary))",end:"transparent"}},commission:{main:"hsl(var(--secondary))",gradient:{start:"hsl(var(--secondary))",end:"transparent"}}},Lt=s=>(s/100).toFixed(2),Od=({active:s,payload:a,label:t})=>{const{t:l}=V();return s&&a&&a.length?e.jsxs("div",{className:"rounded-lg border bg-background p-3 shadow-sm",children:[e.jsx("div",{className:"mb-2 text-sm font-medium",children:t}),a.map((n,o)=>e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx("div",{className:"h-2 w-2 rounded-full",style:{backgroundColor:n.color}}),e.jsxs("span",{className:"text-muted-foreground",children:[l(n.name),":"]}),e.jsx("span",{className:"font-medium",children:n.name.includes(l("dashboard:overview.amount"))?`¥${Lt(n.value)}`:l("dashboard:overview.transactions",{count:n.value})})]},o))]}):null},zd=[{value:"7d",label:"dashboard:overview.last7Days"},{value:"30d",label:"dashboard:overview.last30Days"},{value:"90d",label:"dashboard:overview.last90Days"},{value:"180d",label:"dashboard:overview.last180Days"},{value:"365d",label:"dashboard:overview.lastYear"},{value:"custom",label:"dashboard:overview.customRange"}],$d=(s,a)=>{const t=new Date;if(s==="custom"&&a)return{startDate:a.from,endDate:a.to};let l;switch(s){case"7d":l=hs(t,7);break;case"30d":l=hs(t,30);break;case"90d":l=hs(t,90);break;case"180d":l=hs(t,180);break;case"365d":l=hs(t,365);break;default:l=hs(t,30)}return{startDate:l,endDate:t}};function Ad(){const[s,a]=m.useState("amount"),[t,l]=m.useState("30d"),[n,o]=m.useState({from:hs(new Date,7),to:new Date}),{t:r}=V(),{startDate:c,endDate:u}=$d(t,n),{data:i}=ne({queryKey:["orderStat",{start_date:xs(c,"yyyy-MM-dd"),end_date:xs(u,"yyyy-MM-dd")}],queryFn:async()=>{const{data:d}=await pa.getOrderStat({start_date:xs(c,"yyyy-MM-dd"),end_date:xs(u,"yyyy-MM-dd")});return d},refetchInterval:3e4});return e.jsxs(Ye,{children:[e.jsx(ss,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(_s,{children:r("dashboard:overview.title")}),e.jsxs(Xs,{children:[i?.summary.start_date," ",r("dashboard:overview.to")," ",i?.summary.end_date]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex min-w-0 items-center gap-1",children:[e.jsxs(X,{value:t,onValueChange:d=>l(d),children:[e.jsx(Y,{className:"w-[120px]",children:e.jsx(Z,{placeholder:r("dashboard:overview.selectTimeRange")})}),e.jsx(J,{children:zd.map(d=>e.jsx($,{value:d.value,children:r(d.label)},d.value))})]}),t==="custom"&&e.jsxs(Ss,{children:[e.jsx(ks,{asChild:!0,children:e.jsxs(G,{variant:"outline",className:y("min-w-0 justify-start text-left font-normal",!n&&"text-muted-foreground"),children:[e.jsx(Kt,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:n?.from?n.to?e.jsxs(e.Fragment,{children:[xs(n.from,"yyyy-MM-dd")," -"," ",xs(n.to,"yyyy-MM-dd")]}):xs(n.from,"yyyy-MM-dd"):r("dashboard:overview.selectDate")})]})}),e.jsx(bs,{className:"w-auto p-0",align:"end",children:e.jsx(rt,{mode:"range",defaultMonth:n?.from,selected:{from:n?.from,to:n?.to},onSelect:d=>{d?.from&&d?.to&&o({from:d.from,to:d.to})},numberOfMonths:2})})]})]}),e.jsx(Bt,{value:s,onValueChange:d=>a(d),children:e.jsxs(Ct,{children:[e.jsx(Ee,{value:"amount",children:r("dashboard:overview.amount")}),e.jsx(Ee,{value:"count",children:r("dashboard:overview.count")})]})})]})]})}),e.jsxs(ts,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:r("dashboard:overview.totalIncome")}),e.jsxs("div",{className:"text-2xl font-bold",children:["¥",Lt(i?.summary?.paid_total||0)]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:r("dashboard:overview.totalTransactions",{count:i?.summary?.paid_count||0})}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[r("dashboard:overview.avgOrderAmount")," ¥",Lt(i?.summary?.avg_paid_amount||0)]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:r("dashboard:overview.totalCommission")}),e.jsxs("div",{className:"text-2xl font-bold",children:["¥",Lt(i?.summary?.commission_total||0)]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:r("dashboard:overview.totalTransactions",{count:i?.summary?.commission_count||0})}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[r("dashboard:overview.commissionRate")," ",i?.summary?.commission_rate.toFixed(2)||0,"%"]})]})]}),e.jsx("div",{className:"h-[400px] w-full",children:e.jsx(Do,{width:"100%",height:"100%",children:e.jsxs(Po,{data:i?.list||[],margin:{top:20,right:20,left:0,bottom:0},children:[e.jsxs("defs",{children:[e.jsxs("linearGradient",{id:"incomeGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"0%",stopColor:zs.income.gradient.start,stopOpacity:.2}),e.jsx("stop",{offset:"100%",stopColor:zs.income.gradient.end,stopOpacity:.1})]}),e.jsxs("linearGradient",{id:"commissionGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"0%",stopColor:zs.commission.gradient.start,stopOpacity:.2}),e.jsx("stop",{offset:"100%",stopColor:zs.commission.gradient.end,stopOpacity:.1})]})]}),e.jsx(Eo,{dataKey:"date",axisLine:!1,tickLine:!1,tick:{fill:"hsl(var(--muted-foreground))",fontSize:12},tickFormatter:d=>xs(new Date(d),"MM-dd",{locale:Vo})}),e.jsx(Ro,{axisLine:!1,tickLine:!1,tick:{fill:"hsl(var(--muted-foreground))",fontSize:12},tickFormatter:d=>s==="amount"?`¥${Lt(d)}`:r("dashboard:overview.transactions",{count:d})}),e.jsx(Io,{strokeDasharray:"3 3",vertical:!1,stroke:"hsl(var(--border))",opacity:.3}),e.jsx(Lo,{content:e.jsx(Od,{})}),s==="amount"?e.jsxs(e.Fragment,{children:[e.jsx(Mn,{type:"monotone",dataKey:"paid_total",name:r("dashboard:overview.orderAmount"),stroke:zs.income.main,fill:"url(#incomeGradient)",strokeWidth:2}),e.jsx(Mn,{type:"monotone",dataKey:"commission_total",name:r("dashboard:overview.commissionAmount"),stroke:zs.commission.main,fill:"url(#commissionGradient)",strokeWidth:2})]}):e.jsxs(e.Fragment,{children:[e.jsx(On,{dataKey:"paid_count",name:r("dashboard:overview.orderCount"),fill:zs.income.main,radius:[4,4,0,0],maxBarSize:40}),e.jsx(On,{dataKey:"commission_count",name:r("dashboard:overview.commissionCount"),fill:zs.commission.main,radius:[4,4,0,0],maxBarSize:40})]})]})})})]})]})}function ce({className:s,...a}){return e.jsx("div",{className:y("animate-pulse rounded-md bg-primary/10",s),...a})}function qd(){return e.jsxs(Ye,{children:[e.jsxs(ss,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(ce,{className:"h-4 w-[120px]"}),e.jsx(ce,{className:"h-4 w-4"})]}),e.jsxs(ts,{children:[e.jsx(ce,{className:"h-8 w-[140px] mb-2"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ce,{className:"h-4 w-4"}),e.jsx(ce,{className:"h-4 w-[100px]"})]})]})]})}function Hd(){return e.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:Array.from({length:8}).map((s,a)=>e.jsx(qd,{},a))})}var ae=(s=>(s[s.PENDING=0]="PENDING",s[s.PROCESSING=1]="PROCESSING",s[s.CANCELLED=2]="CANCELLED",s[s.COMPLETED=3]="COMPLETED",s[s.DISCOUNTED=4]="DISCOUNTED",s))(ae||{});const Pt={0:"待支付",1:"开通中",2:"已取消",3:"已完成",4:"已折抵"},Et={0:"yellow-500",1:"blue-500",2:"red-500",3:"green-500",4:"green-500"};var ps=(s=>(s[s.NEW=1]="NEW",s[s.RENEWAL=2]="RENEWAL",s[s.UPGRADE=3]="UPGRADE",s[s.RESET_FLOW=4]="RESET_FLOW",s))(ps||{}),je=(s=>(s[s.PENDING=0]="PENDING",s[s.PROCESSING=1]="PROCESSING",s[s.VALID=2]="VALID",s[s.INVALID=3]="INVALID",s))(je||{});const Zt={0:"待确认",1:"发放中",2:"有效",3:"无效"},ea={0:"yellow-500",1:"blue-500",2:"green-500",3:"red-500"};var Ie=(s=>(s.MONTH_PRICE="month_price",s.QUARTER_PRICE="quarter_price",s.HALF_YEAR_PRICE="half_year_price",s.YEAR_PRICE="year_price",s.TWO_YEAR_PRICE="two_year_price",s.THREE_YEAR_PRICE="three_year_price",s.ONETIME_PRICE="onetime_price",s.RESET_PRICE="reset_price",s))(Ie||{});const Ud={month_price:"月付",quarter_price:"季付",half_year_price:"半年付",year_price:"年付",two_year_price:"两年付",three_year_price:"三年付",onetime_price:"一次性",reset_price:"流量重置包"};var re=(s=>(s.Shadowsocks="shadowsocks",s.Vmess="vmess",s.Trojan="trojan",s.Hysteria="hysteria",s.Vless="vless",s.Tuic="tuic",s.Socks="socks",s.Naive="naive",s.Http="http",s.Mieru="mieru",s.AnyTLS="anytls",s))(re||{});const cs=[{type:"shadowsocks",label:"Shadowsocks"},{type:"vmess",label:"VMess"},{type:"trojan",label:"Trojan"},{type:"hysteria",label:"Hysteria"},{type:"vless",label:"VLess"},{type:"tuic",label:"TUIC"},{type:"socks",label:"SOCKS"},{type:"naive",label:"Naive"},{type:"http",label:"HTTP"},{type:"mieru",label:"Mieru"},{type:"anytls",label:"AnyTLS"}],Ge={shadowsocks:"#489851",vmess:"#CB3180",trojan:"#EBB749",hysteria:"#5684e6",vless:"#1a1a1a",tuic:"#00C853",socks:"#2196F3",naive:"#9C27B0",http:"#FF5722",mieru:"#4CAF50",anytls:"#7E57C2"};var Ze=(s=>(s[s.AMOUNT=1]="AMOUNT",s[s.PERCENTAGE=2]="PERCENTAGE",s))(Ze||{});const Kd={1:"按金额优惠",2:"按比例优惠"};var Hs=(s=>(s[s.OPENING=0]="OPENING",s[s.CLOSED=1]="CLOSED",s))(Hs||{}),qe=(s=>(s[s.LOW=0]="LOW",s[s.MIDDLE=1]="MIDDLE",s[s.HIGH=2]="HIGH",s))(qe||{}),zt=(s=>(s.MONTH="monthly",s.QUARTER="quarterly",s.HALF_YEAR="half_yearly",s.YEAR="yearly",s.TWO_YEAR="two_yearly",s.THREE_YEAR="three_yearly",s.ONETIME="onetime",s.RESET="reset_traffic",s))(zt||{});function $s({title:s,value:a,icon:t,trend:l,description:n,onClick:o,highlight:r,className:c}){return e.jsxs(Ye,{className:y("transition-colors",o&&"cursor-pointer hover:bg-muted/50",r&&"border-primary/50",c),onClick:o,children:[e.jsxs(ss,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(_s,{className:"text-sm font-medium",children:s}),t]}),e.jsxs(ts,{children:[e.jsx("div",{className:"text-2xl font-bold",children:a}),l?e.jsxs("div",{className:"flex items-center pt-1",children:[e.jsx(Ao,{className:y("h-4 w-4",l.isPositive?"text-emerald-500":"text-red-500")}),e.jsxs("span",{className:y("ml-1 text-xs",l.isPositive?"text-emerald-500":"text-red-500"),children:[l.isPositive?"+":"-",Math.abs(l.value),"%"]}),e.jsx("span",{className:"ml-1 text-xs text-muted-foreground",children:l.label})]}):e.jsx("p",{className:"text-xs text-muted-foreground",children:n})]})]})}function Bd({className:s}){const a=Is(),{t}=V(),{data:l,isLoading:n}=ne({queryKey:["dashboardStats"],queryFn:async()=>(await pa.getStatsData()).data,refetchInterval:1e3*60*5});if(n||!l)return e.jsx(Hd,{});const o=()=>{const r=new URLSearchParams;r.set("commission_status",je.PENDING.toString()),r.set("status",ae.COMPLETED.toString()),r.set("commission_balance","gt:0"),a(`/finance/order?${r.toString()}`)};return e.jsxs("div",{className:y("grid gap-4 md:grid-cols-2 lg:grid-cols-4",s),children:[e.jsx($s,{title:t("dashboard:stats.todayIncome"),value:Ws(l.todayIncome),icon:e.jsx(Fo,{className:"h-4 w-4 text-emerald-500"}),trend:{value:l.dayIncomeGrowth,label:t("dashboard:stats.vsYesterday"),isPositive:l.dayIncomeGrowth>0}}),e.jsx($s,{title:t("dashboard:stats.monthlyIncome"),value:Ws(l.currentMonthIncome),icon:e.jsx(Mo,{className:"h-4 w-4 text-blue-500"}),trend:{value:l.monthIncomeGrowth,label:t("dashboard:stats.vsLastMonth"),isPositive:l.monthIncomeGrowth>0}}),e.jsx($s,{title:t("dashboard:stats.pendingTickets"),value:l.ticketPendingTotal,icon:e.jsx(Oo,{className:y("h-4 w-4",l.ticketPendingTotal>0?"text-orange-500":"text-muted-foreground")}),description:l.ticketPendingTotal>0?t("dashboard:stats.hasPendingTickets"):t("dashboard:stats.noPendingTickets"),onClick:()=>a("/user/ticket"),highlight:l.ticketPendingTotal>0}),e.jsx($s,{title:t("dashboard:stats.pendingCommission"),value:l.commissionPendingTotal,icon:e.jsx(zo,{className:y("h-4 w-4",l.commissionPendingTotal>0?"text-blue-500":"text-muted-foreground")}),description:l.commissionPendingTotal>0?t("dashboard:stats.hasPendingCommission"):t("dashboard:stats.noPendingCommission"),onClick:o,highlight:l.commissionPendingTotal>0}),e.jsx($s,{title:t("dashboard:stats.monthlyNewUsers"),value:l.currentMonthNewUsers,icon:e.jsx(Qa,{className:"h-4 w-4 text-blue-500"}),trend:{value:l.userGrowth,label:t("dashboard:stats.vsLastMonth"),isPositive:l.userGrowth>0}}),e.jsx($s,{title:t("dashboard:stats.totalUsers"),value:l.totalUsers,icon:e.jsx(Qa,{className:"h-4 w-4 text-muted-foreground"}),description:t("dashboard:stats.activeUsers",{count:l.activeUsers})}),e.jsx($s,{title:t("dashboard:stats.monthlyUpload"),value:Oe(l.monthTraffic.upload),icon:e.jsx(At,{className:"h-4 w-4 text-emerald-500"}),description:t("dashboard:stats.todayTraffic",{value:Oe(l.todayTraffic.upload)})}),e.jsx($s,{title:t("dashboard:stats.monthlyDownload"),value:Oe(l.monthTraffic.download),icon:e.jsx($o,{className:"h-4 w-4 text-blue-500"}),description:t("dashboard:stats.todayTraffic",{value:Oe(l.todayTraffic.download)})})]})}const Nt=m.forwardRef(({className:s,children:a,...t},l)=>e.jsxs(Ol,{ref:l,className:y("relative overflow-hidden",s),...t,children:[e.jsx(qo,{className:"h-full w-full rounded-[inherit]",children:a}),e.jsx(fa,{}),e.jsx(Ho,{})]}));Nt.displayName=Ol.displayName;const fa=m.forwardRef(({className:s,orientation:a="vertical",...t},l)=>e.jsx(zl,{ref:l,orientation:a,className:y("flex touch-none select-none transition-colors",a==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",a==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",s),...t,children:e.jsx(Uo,{className:"relative flex-1 rounded-full bg-border"})}));fa.displayName=zl.displayName;const rn={today:{getValue:()=>{const s=Bo();return{start:s,end:Go(s,1)}}},last7days:{getValue:()=>{const s=new Date;return{start:hs(s,7),end:s}}},last30days:{getValue:()=>{const s=new Date;return{start:hs(s,30),end:s}}},custom:{getValue:()=>null}};function Gn({selectedRange:s,customDateRange:a,onRangeChange:t,onCustomRangeChange:l}){const{t:n}=V(),o={today:n("dashboard:trafficRank.today"),last7days:n("dashboard:trafficRank.last7days"),last30days:n("dashboard:trafficRank.last30days"),custom:n("dashboard:trafficRank.customRange")};return e.jsxs("div",{className:"flex min-w-0 flex-wrap items-center gap-1",children:[e.jsxs(X,{value:s,onValueChange:t,children:[e.jsx(Y,{className:"w-[120px]",children:e.jsx(Z,{placeholder:n("dashboard:trafficRank.selectTimeRange")})}),e.jsx(J,{position:"popper",className:"z-50",children:Object.entries(rn).map(([r])=>e.jsx($,{value:r,children:o[r]},r))})]}),s==="custom"&&e.jsxs(Ss,{children:[e.jsx(ks,{asChild:!0,children:e.jsxs(G,{variant:"outline",className:y("min-w-0 justify-start text-left font-normal",!a&&"text-muted-foreground"),children:[e.jsx(Kt,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:a?.from?a.to?e.jsxs(e.Fragment,{children:[xs(a.from,"yyyy-MM-dd")," -"," ",xs(a.to,"yyyy-MM-dd")]}):xs(a.from,"yyyy-MM-dd"):e.jsx("span",{children:n("dashboard:trafficRank.selectDateRange")})})]})}),e.jsx(bs,{className:"w-auto p-0",align:"end",children:e.jsx(rt,{mode:"range",defaultMonth:a?.from,selected:{from:a?.from,to:a?.to},onSelect:r=>{r?.from&&r?.to&&l({from:r.from,to:r.to})},numberOfMonths:2})})]})]})}const xt=s=>`${(s/1024/1024/1024).toFixed(2)} GB`;function Gd({className:s}){const{t:a}=V(),[t,l]=m.useState("today"),[n,o]=m.useState({from:hs(new Date,7),to:new Date}),[r,c]=m.useState("today"),[u,i]=m.useState({from:hs(new Date,7),to:new Date}),d=m.useMemo(()=>t==="custom"?{start:n.from,end:n.to}:rn[t].getValue(),[t,n]),h=m.useMemo(()=>r==="custom"?{start:u.from,end:u.to}:rn[r].getValue(),[r,u]),{data:_}=ne({queryKey:["nodeTrafficRank",d.start,d.end],queryFn:()=>pa.getNodeTrafficData({type:"node",start_time:Se.round(d.start.getTime()/1e3),end_time:Se.round(d.end.getTime()/1e3)}),refetchInterval:3e4}),{data:T}=ne({queryKey:["userTrafficRank",h.start,h.end],queryFn:()=>pa.getNodeTrafficData({type:"user",start_time:Se.round(h.start.getTime()/1e3),end_time:Se.round(h.end.getTime()/1e3)}),refetchInterval:3e4});return e.jsxs("div",{className:y("grid gap-4 md:grid-cols-2",s),children:[e.jsxs(Ye,{children:[e.jsx(ss,{className:"flex-none pb-2",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[e.jsxs(_s,{className:"flex items-center text-base font-medium",children:[e.jsx(Ko,{className:"mr-2 h-4 w-4"}),a("dashboard:trafficRank.nodeTrafficRank")]}),e.jsxs("div",{className:"flex min-w-0 items-center gap-1",children:[e.jsx(Gn,{selectedRange:t,customDateRange:n,onRangeChange:l,onCustomRangeChange:o}),e.jsx(zn,{className:"h-4 w-4 flex-shrink-0 text-muted-foreground"})]})]})}),e.jsx(ts,{className:"flex-1",children:_?.data?e.jsxs(Nt,{className:"h-[400px] pr-4",children:[e.jsx("div",{className:"space-y-3",children:_.data.map(S=>e.jsx(be,{delayDuration:200,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx("div",{className:"flex cursor-pointer items-center justify-between space-x-2 rounded-lg bg-muted/50 p-2 transition-colors hover:bg-muted/70",children:e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"truncate text-sm font-medium",children:S.name}),e.jsxs("span",{className:y("ml-2 flex items-center text-xs font-medium",S.change>=0?"text-green-600":"text-red-600"),children:[S.change>=0?e.jsx(Xa,{className:"mr-1 h-3 w-3"}):e.jsx(Za,{className:"mr-1 h-3 w-3"}),Math.abs(S.change),"%"]})]}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx("div",{className:"h-2 flex-1 overflow-hidden rounded-full bg-muted",children:e.jsx("div",{className:"h-full bg-primary transition-all",style:{width:`${S.value/_.data[0].value*100}%`}})}),e.jsx("span",{className:"text-xs text-muted-foreground",children:xt(S.value)})]})]})})}),e.jsx(de,{side:"right",className:"space-y-2 p-4",children:e.jsxs("div",{className:"grid grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[e.jsxs("span",{className:"text-muted-foreground",children:[a("dashboard:trafficRank.currentTraffic"),"："]}),e.jsx("span",{className:"font-medium",children:xt(S.value)}),e.jsxs("span",{className:"text-muted-foreground",children:[a("dashboard:trafficRank.previousTraffic"),"："]}),e.jsx("span",{className:"font-medium",children:xt(S.previousValue)}),e.jsxs("span",{className:"text-muted-foreground",children:[a("dashboard:trafficRank.changeRate"),"："]}),e.jsxs("span",{className:y("font-medium",S.change>=0?"text-green-600":"text-red-600"),children:[S.change>=0?"+":"",S.change,"%"]})]})})]})},S.id))}),e.jsx(fa,{orientation:"vertical"})]}):e.jsx("div",{className:"flex h-[400px] items-center justify-center",children:e.jsx("div",{className:"animate-pulse",children:a("common:loading")})})})]}),e.jsxs(Ye,{children:[e.jsx(ss,{className:"flex-none pb-2",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[e.jsxs(_s,{className:"flex items-center text-base font-medium",children:[e.jsx(Qa,{className:"mr-2 h-4 w-4"}),a("dashboard:trafficRank.userTrafficRank")]}),e.jsxs("div",{className:"flex min-w-0 items-center gap-1",children:[e.jsx(Gn,{selectedRange:r,customDateRange:u,onRangeChange:c,onCustomRangeChange:i}),e.jsx(zn,{className:"h-4 w-4 flex-shrink-0 text-muted-foreground"})]})]})}),e.jsx(ts,{className:"flex-1",children:T?.data?e.jsxs(Nt,{className:"h-[400px] pr-4",children:[e.jsx("div",{className:"space-y-3",children:T.data.map(S=>e.jsx(be,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx("div",{className:"flex cursor-pointer items-center justify-between space-x-2 rounded-lg bg-muted/50 p-2 transition-colors hover:bg-muted/70",children:e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"truncate text-sm font-medium",children:S.name}),e.jsxs("span",{className:y("ml-2 flex items-center text-xs font-medium",S.change>=0?"text-green-600":"text-red-600"),children:[S.change>=0?e.jsx(Xa,{className:"mr-1 h-3 w-3"}):e.jsx(Za,{className:"mr-1 h-3 w-3"}),Math.abs(S.change),"%"]})]}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx("div",{className:"h-2 flex-1 overflow-hidden rounded-full bg-muted",children:e.jsx("div",{className:"h-full bg-primary transition-all",style:{width:`${S.value/T.data[0].value*100}%`}})}),e.jsx("span",{className:"text-xs text-muted-foreground",children:xt(S.value)})]})]})})}),e.jsx(de,{side:"right",className:"space-y-2 p-4",children:e.jsxs("div",{className:"grid grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[e.jsxs("span",{className:"text-muted-foreground",children:[a("dashboard:trafficRank.currentTraffic"),"："]}),e.jsx("span",{className:"font-medium",children:xt(S.value)}),e.jsxs("span",{className:"text-muted-foreground",children:[a("dashboard:trafficRank.previousTraffic"),"："]}),e.jsx("span",{className:"font-medium",children:xt(S.previousValue)}),e.jsxs("span",{className:"text-muted-foreground",children:[a("dashboard:trafficRank.changeRate"),"："]}),e.jsxs("span",{className:y("font-medium",S.change>=0?"text-green-600":"text-red-600"),children:[S.change>=0?"+":"",S.change,"%"]})]})})]})},S.id))}),e.jsx(fa,{orientation:"vertical"})]}):e.jsx("div",{className:"flex h-[400px] items-center justify-center",children:e.jsx("div",{className:"animate-pulse",children:a("common:loading")})})})]})]})}const Wd=Zs("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/10",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function B({className:s,variant:a,...t}){return e.jsx("div",{className:y(Wd({variant:a}),s),...t})}const ra=m.forwardRef(({className:s,value:a,...t},l)=>e.jsx($l,{ref:l,className:y("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",s),...t,children:e.jsx(Wo,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(a||0)}%)`}})}));ra.displayName=$l.displayName;const bn=m.forwardRef(({className:s,...a},t)=>e.jsx("div",{className:"relative w-full overflow-auto",children:e.jsx("table",{ref:t,className:y("w-full caption-bottom text-sm",s),...a})}));bn.displayName="Table";const yn=m.forwardRef(({className:s,...a},t)=>e.jsx("thead",{ref:t,className:y("[&_tr]:border-b",s),...a}));yn.displayName="TableHeader";const Nn=m.forwardRef(({className:s,...a},t)=>e.jsx("tbody",{ref:t,className:y("[&_tr:last-child]:border-0",s),...a}));Nn.displayName="TableBody";const Yd=m.forwardRef(({className:s,...a},t)=>e.jsx("tfoot",{ref:t,className:y("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...a}));Yd.displayName="TableFooter";const As=m.forwardRef(({className:s,...a},t)=>e.jsx("tr",{ref:t,className:y("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...a}));As.displayName="TableRow";const _n=m.forwardRef(({className:s,...a},t)=>e.jsx("th",{ref:t,className:y("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a}));_n.displayName="TableHead";const jt=m.forwardRef(({className:s,...a},t)=>e.jsx("td",{ref:t,className:y("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a}));jt.displayName="TableCell";const Jd=m.forwardRef(({className:s,...a},t)=>e.jsx("caption",{ref:t,className:y("mt-4 text-sm text-muted-foreground",s),...a}));Jd.displayName="TableCaption";function wn({table:s}){const[a,t]=m.useState(""),{t:l}=V("common");m.useEffect(()=>{t((s.getState().pagination.pageIndex+1).toString())},[s.getState().pagination.pageIndex]);const n=o=>{const r=parseInt(o);!isNaN(r)&&r>=1&&r<=s.getPageCount()?s.setPageIndex(r-1):t((s.getState().pagination.pageIndex+1).toString())};return e.jsxs("div",{className:"flex flex-col-reverse gap-4 px-2 py-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("div",{className:"flex-1 text-sm text-muted-foreground",children:l("table.pagination.selected",{selected:s.getFilteredSelectedRowModel().rows.length,total:s.getFilteredRowModel().rows.length})}),e.jsxs("div",{className:"flex flex-col-reverse items-center gap-4 sm:flex-row sm:gap-6 lg:gap-8",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("p",{className:"text-sm font-medium",children:l("table.pagination.itemsPerPage")}),e.jsxs(X,{value:`${s.getState().pagination.pageSize}`,onValueChange:o=>{s.setPageSize(Number(o))},children:[e.jsx(Y,{className:"h-8 w-[70px]",children:e.jsx(Z,{placeholder:s.getState().pagination.pageSize})}),e.jsx(J,{side:"top",children:[10,20,30,40,50,100,500].map(o=>e.jsx($,{value:`${o}`,children:o},o))})]})]}),e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-sm font-medium",children:[e.jsx("span",{children:l("table.pagination.page")}),e.jsx(D,{type:"text",value:a,onChange:o=>t(o.target.value),onBlur:o=>n(o.target.value),onKeyDown:o=>{o.key==="Enter"&&n(o.currentTarget.value)},className:"h-8 w-[50px] text-center"}),e.jsx("span",{children:l("table.pagination.pageOf",{total:s.getPageCount()})})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(E,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>s.setPageIndex(0),disabled:!s.getCanPreviousPage(),children:[e.jsx("span",{className:"sr-only",children:l("table.pagination.firstPage")}),e.jsx(Yo,{className:"h-4 w-4"})]}),e.jsxs(E,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>s.previousPage(),disabled:!s.getCanPreviousPage(),children:[e.jsx("span",{className:"sr-only",children:l("table.pagination.previousPage")}),e.jsx(Fl,{className:"h-4 w-4"})]}),e.jsxs(E,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>s.nextPage(),disabled:!s.getCanNextPage(),children:[e.jsx("span",{className:"sr-only",children:l("table.pagination.nextPage")}),e.jsx(un,{className:"h-4 w-4"})]}),e.jsxs(E,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>s.setPageIndex(s.getPageCount()-1),disabled:!s.getCanNextPage(),children:[e.jsx("span",{className:"sr-only",children:l("table.pagination.lastPage")}),e.jsx(Jo,{className:"h-4 w-4"})]})]})]})]})}function is({table:s,toolbar:a,draggable:t=!1,onDragStart:l,onDragEnd:n,onDragOver:o,onDragLeave:r,onDrop:c,showPagination:u=!0,isLoading:i=!1}){const{t:d}=V("common"),h=m.useRef(null),_=s.getAllColumns().filter(N=>N.getIsPinned()==="left"),T=s.getAllColumns().filter(N=>N.getIsPinned()==="right"),S=N=>_.slice(0,N).reduce((g,k)=>g+(k.getSize()??0),0),C=N=>T.slice(N+1).reduce((g,k)=>g+(k.getSize()??0),0);return e.jsxs("div",{className:"space-y-4",children:[typeof a=="function"?a(s):a,e.jsx("div",{ref:h,className:"relative overflow-auto rounded-md border bg-card",children:e.jsx("div",{className:"overflow-auto",children:e.jsxs(bn,{children:[e.jsx(yn,{children:s.getHeaderGroups().map(N=>e.jsx(As,{className:"hover:bg-transparent",children:N.headers.map((g,k)=>{const R=g.column.getIsPinned()==="left",p=g.column.getIsPinned()==="right",w=R?S(_.indexOf(g.column)):void 0,I=p?C(T.indexOf(g.column)):void 0;return e.jsx(_n,{colSpan:g.colSpan,style:{width:g.getSize(),...R&&{left:w},...p&&{right:I}},className:y("h-11 bg-card px-4 text-muted-foreground",(R||p)&&["sticky z-20","before:absolute before:bottom-0 before:top-0 before:w-[1px] before:bg-border",R&&"before:right-0",p&&"before:left-0"]),children:g.isPlaceholder?null:da(g.column.columnDef.header,g.getContext())},g.id)})},N.id))}),e.jsx(Nn,{children:s.getRowModel().rows?.length?s.getRowModel().rows.map((N,g)=>e.jsx(As,{"data-state":N.getIsSelected()&&"selected",className:"hover:bg-muted/50",draggable:t,onDragStart:k=>l?.(k,g),onDragEnd:n,onDragOver:o,onDragLeave:r,onDrop:k=>c?.(k,g),children:N.getVisibleCells().map((k,R)=>{const p=k.column.getIsPinned()==="left",w=k.column.getIsPinned()==="right",I=p?S(_.indexOf(k.column)):void 0,H=w?C(T.indexOf(k.column)):void 0;return e.jsx(jt,{style:{width:k.column.getSize(),...p&&{left:I},...w&&{right:H}},className:y("bg-card",(p||w)&&["sticky z-20","before:absolute before:bottom-0 before:top-0 before:w-[1px] before:bg-border",p&&"before:right-0",w&&"before:left-0"]),children:da(k.column.columnDef.cell,k.getContext())},k.id)})},N.id)):e.jsx(As,{children:e.jsx(jt,{colSpan:s.getAllColumns().length,className:"h-24 text-center",children:d("table.noData")})})})]})})}),u&&e.jsx(wn,{table:s})]})}const ia=s=>{if(!s)return"";let a;if(typeof s=="string"){if(a=parseInt(s),isNaN(a))return s}else a=s;return(a.toString().length===10?new Date(a*1e3):new Date(a)).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},Rt=Al(),It=Al();function sa({data:s,isLoading:a,searchKeyword:t,selectedLevel:l,total:n,currentPage:o,pageSize:r,onViewDetail:c,onPageChange:u}){const{t:i}=V(),d=T=>{switch(T.toLowerCase()){case"info":return e.jsx(Ft,{className:"h-4 w-4 text-blue-500"});case"warning":return e.jsx(sn,{className:"h-4 w-4 text-yellow-500"});case"error":return e.jsx(tn,{className:"h-4 w-4 text-red-500"});default:return e.jsx(Ft,{className:"h-4 w-4 text-slate-500"})}},h=m.useMemo(()=>[Rt.accessor("level",{id:"level",header:()=>i("dashboard:systemLog.level","级别"),size:80,cell:({getValue:T,row:S})=>{const C=T();return e.jsxs("div",{className:"flex items-center gap-1",children:[d(C),e.jsx("span",{className:y(C.toLowerCase()==="error"&&"text-red-600",C.toLowerCase()==="warning"&&"text-yellow-600",C.toLowerCase()==="info"&&"text-blue-600"),children:C})]})}}),Rt.accessor("created_at",{id:"created_at",header:()=>i("dashboard:systemLog.time","时间"),size:160,cell:({getValue:T})=>ia(T())}),Rt.accessor(T=>T.title||T.message||"",{id:"title",header:()=>i("dashboard:systemLog.logTitle","标题"),cell:({getValue:T})=>e.jsx("span",{className:"inline-block max-w-[300px] truncate",children:T()})}),Rt.accessor("method",{id:"method",header:()=>i("dashboard:systemLog.method","请求方法"),size:100,cell:({getValue:T})=>{const S=T();return S?e.jsx(B,{variant:"outline",className:y(S==="GET"&&"border-blue-200 bg-blue-50 text-blue-700",S==="POST"&&"border-green-200 bg-green-50 text-green-700",S==="PUT"&&"border-amber-200 bg-amber-50 text-amber-700",S==="DELETE"&&"border-red-200 bg-red-50 text-red-700"),children:S}):null}}),Rt.display({id:"actions",header:()=>i("dashboard:systemLog.action","操作"),size:80,cell:({row:T})=>e.jsx(G,{variant:"ghost",size:"sm",onClick:()=>c(T.original),"aria-label":i("dashboard:systemLog.viewDetail","查看详情"),children:e.jsx(en,{className:"h-4 w-4"})})})],[i,c]),_=Je({data:s,columns:h,getCoreRowModel:Qe(),getPaginationRowModel:ls(),pageCount:Math.ceil(n/r),manualPagination:!0,state:{pagination:{pageIndex:o-1,pageSize:r}},onPaginationChange:T=>{if(typeof T=="function"){const S=T({pageIndex:o-1,pageSize:r});u(S.pageIndex+1)}else u(T.pageIndex+1)}});return e.jsxs("div",{className:"overflow-x-auto",children:[e.jsx(is,{table:_,showPagination:!1,isLoading:a}),e.jsx(wn,{table:_}),(t||l&&l!=="all")&&e.jsx("div",{className:"text-center text-sm text-muted-foreground",children:t&&l&&l!=="all"?i("dashboard:systemLog.filter.searchAndLevel",{keyword:t,level:l,count:n},`筛选结果: 包含"${t}"且级别为"${l}"的日志共 ${n} 条`):t?i("dashboard:systemLog.filter.searchOnly",{keyword:t,count:n},`搜索结果: 包含"${t}"的日志共 ${n} 条`):i("dashboard:systemLog.filter.levelOnly",{level:l,count:n},`筛选结果: 级别为"${l}"的日志共 ${n} 条`)})]})}function Qd(){const{t:s}=V(),[a,t]=m.useState(0),[l,n]=m.useState(!1),[o,r]=m.useState(1),[c]=m.useState(10),[u,i]=m.useState(null),[d,h]=m.useState(!1),[_,T]=m.useState(!1),[S,C]=m.useState(1),[N]=m.useState(10),[g,k]=m.useState(null),[R,p]=m.useState(!1),[w,I]=m.useState(""),[H,O]=m.useState(""),[K,oe]=m.useState("all");m.useEffect(()=>{const Q=setTimeout(()=>{O(w),w!==H&&C(1)},500);return()=>clearTimeout(Q)},[w]);const{data:W,isLoading:te,refetch:q,isRefetching:L}=ne({queryKey:["systemStatus",a],queryFn:async()=>(await me.getSystemStatus()).data,refetchInterval:3e4}),{data:U,isLoading:ms,refetch:De,isRefetching:le}=ne({queryKey:["queueStats",a],queryFn:async()=>(await me.getQueueStats()).data,refetchInterval:3e4}),{data:ys,isLoading:Fs,refetch:Fa}=ne({queryKey:["failedJobs",o,c],queryFn:async()=>{const Q=await me.getHorizonFailedJobs({current:o,page_size:c});return{data:Q.data,total:Q.total||0}},enabled:l}),{data:Gt,isLoading:it,refetch:Ma}=ne({queryKey:["systemLogs",S,N,K,H],queryFn:async()=>{const Q={current:S,page_size:N};K&&K!=="all"&&(Q.level=K),H.trim()&&(Q.keyword=H.trim());const Os=await me.getSystemLog(Q);return{data:Os.data,total:Os.total||0}},enabled:_}),Wt=ys?.data||[],Oa=ys?.total||0,se=Gt?.data||[],ie=Gt?.total||0,Me=m.useMemo(()=>[It.display({id:"failed_at",header:()=>s("dashboard:queue.details.time","时间"),cell:({row:Q})=>ia(Q.original.failed_at)}),It.display({id:"queue",header:()=>s("dashboard:queue.details.queue","队列"),cell:({row:Q})=>Q.original.queue}),It.display({id:"name",header:()=>s("dashboard:queue.details.name","任务名称"),cell:({row:Q})=>e.jsx(be,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx("span",{className:"inline-block max-w-[150px] truncate",children:Q.original.name})}),e.jsx(de,{children:e.jsx("span",{children:Q.original.name})})]})})}),It.display({id:"exception",header:()=>s("dashboard:queue.details.exception","异常信息"),cell:({row:Q})=>e.jsx(be,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx("span",{className:"inline-block max-w-[200px] truncate",children:Q.original.exception.split(`
`)[0]})}),e.jsx(de,{className:"max-w-[300px] whitespace-pre-wrap",children:e.jsx("span",{children:Q.original.exception})})]})})}),It.display({id:"actions",header:()=>s("dashboard:queue.details.action","操作"),size:80,cell:({row:Q})=>e.jsx(G,{variant:"ghost",size:"sm",onClick:()=>li(Q.original),"aria-label":s("dashboard:queue.details.viewDetail","查看详情"),children:e.jsx(en,{className:"h-4 w-4"})})})],[s]),Ms=Je({data:Wt,columns:Me,getCoreRowModel:Qe(),getPaginationRowModel:ls(),pageCount:Math.ceil(Oa/c),manualPagination:!0,state:{pagination:{pageIndex:o-1,pageSize:c}},onPaginationChange:Q=>{if(typeof Q=="function"){const Os=Q({pageIndex:o-1,pageSize:c});Ln(Os.pageIndex+1)}else Ln(Q.pageIndex+1)}}),ti=()=>{t(Q=>Q+1)},Ln=Q=>{r(Q)},Yt=Q=>{C(Q)},ai=Q=>{oe(Q),C(1)},ni=()=>{I(""),O(""),oe("all"),C(1)},Jt=Q=>{k(Q),p(!0)},li=Q=>{i(Q),h(!0)};if(te||ms)return e.jsx("div",{className:"flex items-center justify-center p-6",children:e.jsx(gn,{className:"h-6 w-6 animate-spin"})});const ri=Q=>Q?e.jsx(ql,{className:"h-5 w-5 text-green-500"}):e.jsx(Hl,{className:"h-5 w-5 text-red-500"});return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs(Ye,{children:[e.jsxs(ss,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs(_s,{className:"flex items-center gap-2",children:[e.jsx(Qo,{className:"h-5 w-5"}),s("dashboard:queue.title")]}),e.jsx(Xs,{children:s("dashboard:queue.status.description")})]}),e.jsx(G,{variant:"outline",size:"icon",onClick:ti,disabled:L||le,children:e.jsx(za,{className:y("h-4 w-4",(L||le)&&"animate-spin")})})]}),e.jsx(ts,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2 rounded-lg bg-muted/50 p-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[ri(U?.status||!1),e.jsx("span",{className:"font-medium",children:s("dashboard:queue.status.running")})]}),e.jsx(B,{variant:U?.status?"secondary":"destructive",children:U?.status?s("dashboard:queue.status.normal"):s("dashboard:queue.status.abnormal")})]}),e.jsx("div",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.status.waitTime",{seconds:U?.wait?.default||0})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(be,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsxs("div",{className:"space-y-2 rounded-lg bg-muted/50 p-3",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.details.recentJobs")}),e.jsx("p",{className:"text-2xl font-bold",children:U?.recentJobs||0}),e.jsx(ra,{value:(U?.recentJobs||0)/(U?.periods?.recentJobs||1)*100,className:"h-1"})]})}),e.jsx(de,{children:e.jsx("p",{children:s("dashboard:queue.details.statisticsPeriod",{hours:U?.periods?.recentJobs||0})})})]})}),e.jsx(be,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsxs("div",{className:"space-y-2 rounded-lg bg-muted/50 p-3",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.details.jobsPerMinute")}),e.jsx("p",{className:"text-2xl font-bold",children:U?.jobsPerMinute||0}),e.jsx(ra,{value:(U?.jobsPerMinute||0)/(U?.queueWithMaxThroughput?.throughput||1)*100,className:"h-1"})]})}),e.jsx(de,{children:e.jsx("p",{children:s("dashboard:queue.details.maxThroughput",{value:U?.queueWithMaxThroughput?.throughput||0})})})]})})]})]})})]}),e.jsxs(Ye,{children:[e.jsxs(ss,{children:[e.jsxs(_s,{className:"flex items-center gap-2",children:[e.jsx(Xo,{className:"h-5 w-5"}),s("dashboard:queue.jobDetails")]}),e.jsx(Xs,{children:s("dashboard:queue.details.description")})]}),e.jsx(ts,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2 rounded-lg bg-muted/50 p-3",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.details.failedJobs7Days")}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"cursor-pointer text-2xl font-bold text-destructive hover:underline",title:s("dashboard:queue.details.viewFailedJobs"),onClick:()=>n(!0),style:{userSelect:"none"},children:U?.failedJobs||0}),e.jsx(en,{className:"h-4 w-4 cursor-pointer text-muted-foreground hover:text-destructive",onClick:()=>n(!0),"aria-label":s("dashboard:queue.details.viewFailedJobs")})]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s("dashboard:queue.details.retentionPeriod",{hours:U?.periods?.failedJobs||0})})]}),e.jsxs("div",{className:"space-y-2 rounded-lg bg-muted/50 p-3",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.details.longestRunningQueue")}),e.jsxs("p",{className:"text-2xl font-bold",children:[U?.queueWithMaxRuntime?.runtime||0,"s"]}),e.jsx("div",{className:"truncate text-xs text-muted-foreground",children:U?.queueWithMaxRuntime?.name||"N/A"})]})]}),e.jsxs("div",{className:"rounded-lg bg-muted/50 p-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.details.activeProcesses")}),e.jsxs("span",{className:"font-medium",children:[U?.processes||0," /"," ",(U?.processes||0)+(U?.pausedMasters||0)]})]}),e.jsx(ra,{value:(U?.processes||0)/((U?.processes||0)+(U?.pausedMasters||0))*100,className:"mt-2 h-1"})]})]})})]})]}),e.jsxs(Ye,{children:[e.jsxs(ss,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs(_s,{className:"flex items-center gap-2",children:[e.jsx($n,{className:"h-5 w-5"}),s("dashboard:systemLog.title","系统日志")]}),e.jsx(Xs,{children:s("dashboard:systemLog.description","查看系统运行日志记录")})]}),e.jsx(G,{variant:"outline",onClick:()=>T(!0),children:s("dashboard:systemLog.viewAll","查看全部")})]}),e.jsx(ts,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2 rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-900 dark:bg-blue-950/30",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ft,{className:"h-5 w-5 text-blue-500"}),e.jsx("p",{className:"font-medium text-blue-700 dark:text-blue-300",children:s("dashboard:systemLog.tabs.info","信息")})]}),e.jsx("p",{className:"text-2xl font-bold text-blue-700 dark:text-blue-300",children:W?.logs?.info||0})]}),e.jsxs("div",{className:"space-y-2 rounded-lg border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-900 dark:bg-yellow-950/30",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(sn,{className:"h-5 w-5 text-yellow-500"}),e.jsx("p",{className:"font-medium text-yellow-700 dark:text-yellow-300",children:s("dashboard:systemLog.tabs.warning","警告")})]}),e.jsx("p",{className:"text-2xl font-bold text-yellow-700 dark:text-yellow-300",children:W?.logs?.warning||0})]}),e.jsxs("div",{className:"space-y-2 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950/30",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(tn,{className:"h-5 w-5 text-red-500"}),e.jsx("p",{className:"font-medium text-red-700 dark:text-red-300",children:s("dashboard:systemLog.tabs.error","错误")})]}),e.jsx("p",{className:"text-2xl font-bold text-red-700 dark:text-red-300",children:W?.logs?.error||0})]})]}),W?.logs&&W.logs.total>0&&e.jsx("div",{className:"mt-3 text-center text-sm text-muted-foreground",children:s("dashboard:systemLog.totalLogs","总日志数：{{count}}",{count:W.logs.total})})]})})]}),e.jsx(pe,{open:l,onOpenChange:n,children:e.jsxs(ue,{className:"max-h-[90vh] max-w-4xl overflow-y-auto",children:[e.jsx(ve,{children:e.jsx(ge,{children:s("dashboard:queue.details.failedJobsDetailTitle","失败任务详情")})}),e.jsxs("div",{className:"overflow-x-auto",children:[e.jsx(is,{table:Ms,showPagination:!1,isLoading:Fs}),e.jsx(wn,{table:Ms}),Wt.length===0&&e.jsx("div",{className:"py-8 text-center text-muted-foreground",children:s("dashboard:queue.details.noFailedJobs","暂无失败任务")})]}),e.jsxs(Re,{children:[e.jsxs(G,{variant:"outline",onClick:()=>Fa(),children:[e.jsx(za,{className:"mr-2 h-4 w-4"}),s("dashboard:common.refresh","刷新")]}),e.jsx(qs,{asChild:!0,children:e.jsx(G,{variant:"outline",children:s("common.close","关闭")})})]})]})}),e.jsx(pe,{open:d,onOpenChange:h,children:e.jsxs(ue,{className:"max-h-[90vh] max-w-4xl overflow-y-auto",children:[e.jsx(ve,{children:e.jsx(ge,{children:s("dashboard:queue.details.jobDetailTitle","任务详情")})}),u&&e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:queue.details.id","任务ID")}),e.jsx("p",{className:"break-all rounded-md bg-muted/50 p-2 text-sm",children:u.id})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:queue.details.time","时间")}),e.jsx("p",{className:"rounded-md bg-muted/50 p-2 text-sm",children:u.failed_at})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:queue.details.queue","队列")}),e.jsx("p",{className:"rounded-md bg-muted/50 p-2 text-sm",children:u.queue})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:queue.details.connection","连接")}),e.jsx("p",{className:"rounded-md bg-muted/50 p-2 text-sm",children:u.connection})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:queue.details.name","任务名称")}),e.jsx("p",{className:"break-all rounded-md bg-muted/50 p-2 text-sm",children:u.name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:queue.details.exception","异常信息")}),e.jsx("div",{className:"max-h-[200px] overflow-y-auto rounded-md bg-red-50 p-3 dark:bg-red-950/30",children:e.jsx("pre",{className:"whitespace-pre-wrap text-xs text-red-700 dark:text-red-300",children:u.exception})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:queue.details.payload","任务数据")}),e.jsx("div",{className:"max-h-[200px] overflow-y-auto rounded-md bg-muted/50 p-3",children:e.jsx("pre",{className:"whitespace-pre-wrap break-all text-xs",children:(()=>{try{return JSON.stringify(JSON.parse(u.payload),null,2)}catch{return u.payload}})()})})]})]}),e.jsx(Re,{children:e.jsx(G,{variant:"outline",onClick:()=>h(!1),children:s("common.close","关闭")})})]})}),e.jsx(pe,{open:_,onOpenChange:T,children:e.jsxs(ue,{className:"max-h-[90vh] max-w-4xl overflow-y-auto",children:[e.jsx(ve,{children:e.jsx(ge,{children:s("dashboard:systemLog.title","系统日志")})}),e.jsxs(Bt,{value:K,onValueChange:ai,className:"w-full overflow-x-auto",children:[e.jsxs("div",{className:"mb-4 flex flex-col gap-2 p-1 md:flex-row md:items-center md:justify-between",children:[e.jsxs(Ct,{className:"grid w-auto grid-cols-4",children:[e.jsxs(Ee,{value:"all",className:"flex items-center gap-2",children:[e.jsx($n,{className:"h-4 w-4"}),s("dashboard:systemLog.tabs.all","全部")]}),e.jsxs(Ee,{value:"info",className:"flex items-center gap-2",children:[e.jsx(Ft,{className:"h-4 w-4 text-blue-500"}),s("dashboard:systemLog.tabs.info","信息")]}),e.jsxs(Ee,{value:"warning",className:"flex items-center gap-2",children:[e.jsx(sn,{className:"h-4 w-4 text-yellow-500"}),s("dashboard:systemLog.tabs.warning","警告")]}),e.jsxs(Ee,{value:"error",className:"flex items-center gap-2",children:[e.jsx(tn,{className:"h-4 w-4 text-red-500"}),s("dashboard:systemLog.tabs.error","错误")]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(hn,{className:"h-4 w-4 text-muted-foreground"}),e.jsx(D,{placeholder:s("dashboard:systemLog.search","搜索日志内容..."),value:w,onChange:Q=>I(Q.target.value),className:"w-full md:w-64"})]})]}),e.jsx(We,{value:"all",className:"mt-0",children:e.jsx(sa,{data:se,isLoading:it,searchKeyword:H,selectedLevel:K,total:ie,currentPage:S,pageSize:N,onViewDetail:Jt,onPageChange:Yt})}),e.jsx(We,{value:"info",className:"mt-0 overflow-x-auto",children:e.jsx(sa,{data:se,isLoading:it,searchKeyword:H,selectedLevel:K,total:ie,currentPage:S,pageSize:N,onViewDetail:Jt,onPageChange:Yt})}),e.jsx(We,{value:"warning",className:"mt-0",children:e.jsx(sa,{data:se,isLoading:it,searchKeyword:H,selectedLevel:K,total:ie,currentPage:S,pageSize:N,onViewDetail:Jt,onPageChange:Yt})}),e.jsx(We,{value:"error",className:"mt-0",children:e.jsx(sa,{data:se,isLoading:it,searchKeyword:H,selectedLevel:K,total:ie,currentPage:S,pageSize:N,onViewDetail:Jt,onPageChange:Yt})})]}),e.jsxs(Re,{children:[e.jsxs(G,{variant:"outline",onClick:()=>Ma(),children:[e.jsx(za,{className:"mr-2 h-4 w-4"}),s("dashboard:common.refresh","刷新")]}),e.jsx(G,{variant:"outline",onClick:ni,children:s("dashboard:systemLog.filter.reset","重置筛选")}),e.jsx(qs,{asChild:!0,children:e.jsx(G,{variant:"outline",children:s("common.close","关闭")})})]})]})}),e.jsx(pe,{open:R,onOpenChange:p,children:e.jsxs(ue,{className:"max-h-[90vh] max-w-4xl overflow-y-auto",children:[e.jsx(ve,{children:e.jsx(ge,{children:s("dashboard:systemLog.detailTitle","日志详情")})}),g&&e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:systemLog.level","级别")}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(Ft,{className:"h-4 w-4"}),e.jsx("p",{className:"font-medium",children:g.level})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:systemLog.time","时间")}),e.jsx("p",{children:ia(g.created_at)||ia(g.updated_at)})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:systemLog.logTitle","标题")}),e.jsx("div",{className:"whitespace-pre-wrap rounded-md bg-muted/50 p-3",children:g.title||g.message||""})]}),(g.host||g.ip)&&e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[g.host&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:systemLog.host","主机")}),e.jsx("p",{className:"break-all rounded-md bg-muted/50 p-2 text-sm",children:g.host})]}),g.ip&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:systemLog.ip","IP地址")}),e.jsx("p",{className:"rounded-md bg-muted/50 p-2 text-sm",children:g.ip})]})]}),g.uri&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:systemLog.uri","URI")}),e.jsx("div",{className:"overflow-x-auto rounded-md bg-muted/50 p-3",children:e.jsx("code",{className:"text-sm",children:g.uri})})]}),g.method&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:systemLog.method","请求方法")}),e.jsx("div",{children:e.jsx(B,{variant:"outline",className:"text-base font-medium",children:g.method})})]}),g.data&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:systemLog.requestData","请求数据")}),e.jsx("div",{className:"max-h-[150px] overflow-y-auto rounded-md bg-muted/50 p-3",children:e.jsx("pre",{className:"whitespace-pre-wrap break-all text-xs",children:(()=>{try{return JSON.stringify(JSON.parse(g.data),null,2)}catch{return g.data}})()})})]}),g.context&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:s("dashboard:systemLog.exception","异常信息")}),e.jsx("div",{className:"max-h-[250px] overflow-y-auto rounded-md bg-red-50 p-3 dark:bg-red-950/30",children:e.jsx("pre",{className:"whitespace-pre-wrap break-all text-xs text-red-700 dark:text-red-300",children:(()=>{try{const Q=JSON.parse(g.context);if(Q.exception){const Os=Q.exception,ii=Os["\0*\0message"]||"",oi=Os["\0*\0file"]||"",ci=Os["\0*\0line"]||"";return`${ii}

File: ${oi}
Line: ${ci}`}return JSON.stringify(Q,null,2)}catch{return g.context}})()})})]})]}),e.jsx(Re,{children:e.jsx(qs,{asChild:!0,children:e.jsx(G,{variant:"outline",children:s("common.close","关闭")})})})]})})]})}function Xd(){const{t:s}=V();return e.jsxs(Ve,{children:[e.jsxs(Fe,{children:[e.jsx("div",{className:"flex items-center",children:e.jsx("h1",{className:"text-2xl font-bold tracking-tight md:text-3xl",children:s("dashboard:title")})}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Xe,{}),e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsx(Ae,{children:e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"grid gap-6",children:[e.jsx(Bd,{}),e.jsx(Ad,{}),e.jsx(Gd,{}),e.jsx(Qd,{})]})})})]})}const Zd=Object.freeze(Object.defineProperty({__proto__:null,default:Xd},Symbol.toStringTag,{value:"Module"}));function em({className:s,items:a,...t}){const{pathname:l}=cn(),n=Is(),[o,r]=m.useState(l??"/settings"),c=i=>{r(i),n(i)},{t:u}=V("settings");return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"p-1 md:hidden",children:e.jsxs(X,{value:o,onValueChange:c,children:[e.jsx(Y,{className:"h-12 sm:w-48",children:e.jsx(Z,{placeholder:"Theme"})}),e.jsx(J,{children:a.map(i=>e.jsx($,{value:i.href,children:e.jsxs("div",{className:"flex gap-x-4 px-2 py-1",children:[e.jsx("span",{className:"scale-125",children:i.icon}),e.jsx("span",{className:"text-md",children:u(i.title)})]})},i.href))})]})}),e.jsx("div",{className:"hidden w-full overflow-x-auto bg-background px-1 py-2 md:block",children:e.jsx("nav",{className:y("flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1",s),...t,children:a.map(i=>e.jsxs(st,{to:i.href,className:y(wt({variant:"ghost"}),l===i.href?"bg-muted hover:bg-muted":"hover:bg-transparent hover:underline","justify-start"),children:[e.jsx("span",{className:"mr-2",children:i.icon}),u(i.title)]},i.href))})})]})}const sm=[{title:"site.title",key:"site",icon:e.jsx(Zo,{size:18}),href:"/config/system",description:"site.description"},{title:"safe.title",key:"safe",icon:e.jsx(wl,{size:18}),href:"/config/system/safe",description:"safe.description"},{title:"subscribe.title",key:"subscribe",icon:e.jsx(Cl,{size:18}),href:"/config/system/subscribe",description:"subscribe.description"},{title:"invite.title",key:"invite",icon:e.jsx(ec,{size:18}),href:"/config/system/invite",description:"invite.description"},{title:"server.title",key:"server",icon:e.jsx(_l,{size:18}),href:"/config/system/server",description:"server.description"},{title:"email.title",key:"email",icon:e.jsx(sc,{size:18}),href:"/config/system/email",description:"email.description"},{title:"telegram.title",key:"telegram",icon:e.jsx(tc,{size:18}),href:"/config/system/telegram",description:"telegram.description"},{title:"app.title",key:"app",icon:e.jsx(Nl,{size:18}),href:"/config/system/app",description:"app.description"},{title:"subscribe_template.title",key:"subscribe_template",icon:e.jsx(ac,{size:18}),href:"/config/system/subscribe-template",description:"subscribe_template.description"}];function tm(){const{t:s}=V("settings");return e.jsxs(Ve,{fadedBelow:!0,fixedHeight:!0,children:[e.jsxs(Fe,{children:[e.jsx(Xe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h1",{className:"text-2xl font-bold tracking-tight md:text-3xl",children:s("title")}),e.jsx("div",{className:"text-muted-foreground",children:s("description")})]}),e.jsx(ke,{className:"my-6"}),e.jsxs("div",{className:"flex flex-1 flex-col space-y-8 overflow-auto lg:flex-row lg:space-x-12 lg:space-y-0",children:[e.jsx("aside",{className:"sticky top-0 lg:w-1/5",children:e.jsx(em,{items:sm})}),e.jsx("div",{className:"flex-1 w-full p-1 pr-4",children:e.jsx("div",{className:"pb-16",children:e.jsx(dn,{})})})]})]})]})}const am=Object.freeze(Object.defineProperty({__proto__:null,default:tm},Symbol.toStringTag,{value:"Module"})),ee=m.forwardRef(({className:s,...a},t)=>e.jsx(Ul,{className:y("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...a,ref:t,children:e.jsx(nc,{className:y("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})}));ee.displayName=Ul.displayName;const Ts=m.forwardRef(({className:s,...a},t)=>e.jsx("textarea",{className:y("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...a}));Ts.displayName="Textarea";const nm=x.object({logo:x.string().nullable().default(""),force_https:x.number().nullable().default(0),stop_register:x.number().nullable().default(0),app_name:x.string().nullable().default(""),app_description:x.string().nullable().default(""),app_url:x.string().nullable().default(""),subscribe_url:x.string().nullable().default(""),try_out_plan_id:x.number().nullable().default(0),try_out_hour:x.coerce.number().nullable().default(0),tos_url:x.string().nullable().default(""),currency:x.string().nullable().default(""),currency_symbol:x.string().nullable().default("")});function lm(){const{t:s}=V("settings"),[a,t]=m.useState(!1),l=m.useRef(null),{data:n}=ne({queryKey:["settings","site"],queryFn:()=>me.getSettings("site")}),{data:o}=ne({queryKey:["plans"],queryFn:()=>es.getList()}),r=ye({resolver:_e(nm),defaultValues:{},mode:"onBlur"}),{mutateAsync:c}=gs({mutationFn:me.saveSettings,onSuccess:d=>{d.data&&A.success(s("common.autoSaved"))}});m.useEffect(()=>{if(n?.data?.site){const d=n?.data?.site;Object.entries(d).forEach(([h,_])=>{r.setValue(h,_)}),l.current=d}},[n]);const u=m.useCallback(Se.debounce(async d=>{if(!Se.isEqual(d,l.current)){t(!0);try{const h=Object.entries(d).reduce((_,[T,S])=>(_[T]=S===null?"":S,_),{});await c(h),l.current=d}finally{t(!1)}}},1e3),[c]),i=m.useCallback(d=>{u(d)},[u]);return m.useEffect(()=>{const d=r.watch(h=>{i(h)});return()=>d.unsubscribe()},[r.watch,i]),e.jsx(we,{...r,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:r.control,name:"app_name",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("site.form.siteName.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("site.form.siteName.placeholder"),...d,value:d.value||"",onChange:h=>{d.onChange(h),i(r.getValues())}})}),e.jsx(F,{children:s("site.form.siteName.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:r.control,name:"app_description",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("site.form.siteDescription.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("site.form.siteDescription.placeholder"),...d,value:d.value||"",onChange:h=>{d.onChange(h),i(r.getValues())}})}),e.jsx(F,{children:s("site.form.siteDescription.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:r.control,name:"app_url",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("site.form.siteUrl.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("site.form.siteUrl.placeholder"),...d,value:d.value||"",onChange:h=>{d.onChange(h),i(r.getValues())}})}),e.jsx(F,{children:s("site.form.siteUrl.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:r.control,name:"force_https",render:({field:d})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("site.form.forceHttps.label")}),e.jsx(F,{children:s("site.form.forceHttps.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:!!d.value,onCheckedChange:h=>{d.onChange(Number(h)),i(r.getValues())}})})]})}),e.jsx(v,{control:r.control,name:"logo",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("site.form.logo.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("site.form.logo.placeholder"),...d,value:d.value||"",onChange:h=>{d.onChange(h),i(r.getValues())}})}),e.jsx(F,{children:s("site.form.logo.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:r.control,name:"subscribe_url",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("site.form.subscribeUrl.label")}),e.jsx(b,{children:e.jsx(Ts,{placeholder:s("site.form.subscribeUrl.placeholder"),...d,value:d.value||"",onChange:h=>{d.onChange(h),i(r.getValues())}})}),e.jsx(F,{children:s("site.form.subscribeUrl.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:r.control,name:"tos_url",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("site.form.tosUrl.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("site.form.tosUrl.placeholder"),...d,value:d.value||"",onChange:h=>{d.onChange(h),i(r.getValues())}})}),e.jsx(F,{children:s("site.form.tosUrl.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:r.control,name:"stop_register",render:({field:d})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("site.form.stopRegister.label")}),e.jsx(F,{children:s("site.form.stopRegister.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:!!d.value,onCheckedChange:h=>{d.onChange(Number(h)),i(r.getValues())}})})]})}),e.jsx(v,{control:r.control,name:"try_out_plan_id",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("site.form.tryOut.label")}),e.jsx(b,{children:e.jsxs(X,{value:d.value?.toString(),onValueChange:h=>{d.onChange(Number(h)),i(r.getValues())},children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:s("site.form.tryOut.placeholder")})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:s("site.form.tryOut.placeholder")}),o?.data?.map(h=>e.jsx($,{value:h.id.toString(),children:h.name},h.id.toString()))]})]})}),e.jsx(F,{children:s("site.form.tryOut.description")}),e.jsx(P,{})]})}),!!r.watch("try_out_plan_id")&&e.jsx(v,{control:r.control,name:"try_out_hour",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"",children:s("site.form.tryOut.duration.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("site.form.tryOut.duration.placeholder"),...d,value:d.value||"",onChange:h=>{d.onChange(h),i(r.getValues())}})}),e.jsx(F,{children:s("site.form.tryOut.duration.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:r.control,name:"currency",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("site.form.currency.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("site.form.currency.placeholder"),...d,value:d.value||"",onChange:h=>{d.onChange(h),i(r.getValues())}})}),e.jsx(F,{children:s("site.form.currency.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:r.control,name:"currency_symbol",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("site.form.currencySymbol.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("site.form.currencySymbol.placeholder"),...d,value:d.value||"",onChange:h=>{d.onChange(h),i(r.getValues())}})}),e.jsx(F,{children:s("site.form.currencySymbol.description")}),e.jsx(P,{})]})}),a&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("site.form.saving")})]})})}function rm(){const{t:s}=V("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("site.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("site.description")})]}),e.jsx(ke,{}),e.jsx(lm,{})]})}const im=Object.freeze(Object.defineProperty({__proto__:null,default:rm},Symbol.toStringTag,{value:"Module"})),om=x.object({email_verify:x.boolean().nullable(),safe_mode_enable:x.boolean().nullable(),secure_path:x.string().nullable(),email_whitelist_enable:x.boolean().nullable(),email_whitelist_suffix:x.array(x.string().nullable()).nullable(),email_gmail_limit_enable:x.boolean().nullable(),recaptcha_enable:x.boolean().nullable(),recaptcha_key:x.string().nullable(),recaptcha_site_key:x.string().nullable(),register_limit_by_ip_enable:x.boolean().nullable(),register_limit_count:x.coerce.string().transform(s=>s===""?null:s).nullable(),register_limit_expire:x.coerce.string().transform(s=>s===""?null:s).nullable(),password_limit_enable:x.boolean().nullable(),password_limit_count:x.coerce.string().transform(s=>s===""?null:s).nullable(),password_limit_expire:x.coerce.string().transform(s=>s===""?null:s).nullable()}),cm={email_verify:!1,safe_mode_enable:!1,secure_path:"",email_whitelist_enable:!1,email_whitelist_suffix:[],email_gmail_limit_enable:!1,recaptcha_enable:!1,recaptcha_key:"",recaptcha_site_key:"",register_limit_by_ip_enable:!1,register_limit_count:"",register_limit_expire:"",password_limit_enable:!1,password_limit_count:"",password_limit_expire:""};function dm(){const{t:s}=V("settings"),[a,t]=m.useState(!1),l=m.useRef(null),n=ye({resolver:_e(om),defaultValues:cm,mode:"onBlur"}),{data:o}=ne({queryKey:["settings","safe"],queryFn:()=>me.getSettings("safe")}),{mutateAsync:r}=gs({mutationFn:me.saveSettings,onSuccess:i=>{i.data&&A.success(s("common.autoSaved"))}});m.useEffect(()=>{if(o?.data.safe){const i=o.data.safe;Object.entries(i).forEach(([d,h])=>{typeof h=="number"?n.setValue(d,String(h)):n.setValue(d,h)}),l.current=i}},[o]);const c=m.useCallback(Se.debounce(async i=>{if(!Se.isEqual(i,l.current)){t(!0);try{await r(i),l.current=i}finally{t(!1)}}},1e3),[r]),u=m.useCallback(i=>{c(i)},[c]);return m.useEffect(()=>{const i=n.watch(d=>{u(d)});return()=>i.unsubscribe()},[n.watch,u]),e.jsx(we,{...n,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:n.control,name:"email_verify",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("safe.form.emailVerify.label")}),e.jsx(F,{children:s("safe.form.emailVerify.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),e.jsx(v,{control:n.control,name:"email_gmail_limit_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("safe.form.gmailLimit.label")}),e.jsx(F,{children:s("safe.form.gmailLimit.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),e.jsx(v,{control:n.control,name:"safe_mode_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("safe.form.safeMode.label")}),e.jsx(F,{children:s("safe.form.safeMode.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),e.jsx(v,{control:n.control,name:"secure_path",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("safe.form.securePath.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("safe.form.securePath.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),u(n.getValues())}})}),e.jsx(F,{children:s("safe.form.securePath.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"email_whitelist_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("safe.form.emailWhitelist.label")}),e.jsx(F,{children:s("safe.form.emailWhitelist.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),n.watch("email_whitelist_enable")&&e.jsx(v,{control:n.control,name:"email_whitelist_suffix",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("safe.form.emailWhitelist.suffixes.label")}),e.jsx(b,{children:e.jsx(Ts,{placeholder:s("safe.form.emailWhitelist.suffixes.placeholder"),...i,value:(i.value||[]).join(`
`),onChange:d=>{const h=d.target.value.split(`
`).filter(Boolean);i.onChange(h),u(n.getValues())}})}),e.jsx(F,{children:s("safe.form.emailWhitelist.suffixes.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"recaptcha_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("safe.form.recaptcha.enable.label")}),e.jsx(F,{children:s("safe.form.recaptcha.enable.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),n.watch("recaptcha_enable")&&e.jsxs(e.Fragment,{children:[e.jsx(v,{control:n.control,name:"recaptcha_site_key",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("safe.form.recaptcha.siteKey.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("safe.form.recaptcha.siteKey.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),u(n.getValues())}})}),e.jsx(F,{children:s("safe.form.recaptcha.siteKey.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"recaptcha_key",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("safe.form.recaptcha.key.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("safe.form.recaptcha.key.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),u(n.getValues())}})}),e.jsx(F,{children:s("safe.form.recaptcha.key.description")}),e.jsx(P,{})]})})]}),e.jsx(v,{control:n.control,name:"register_limit_by_ip_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("safe.form.registerLimit.enable.label")}),e.jsx(F,{children:s("safe.form.registerLimit.enable.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),n.watch("register_limit_by_ip_enable")&&e.jsxs(e.Fragment,{children:[e.jsx(v,{control:n.control,name:"register_limit_count",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("safe.form.registerLimit.count.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("safe.form.registerLimit.count.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),u(n.getValues())}})}),e.jsx(F,{children:s("safe.form.registerLimit.count.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"register_limit_expire",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("safe.form.registerLimit.expire.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("safe.form.registerLimit.expire.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),u(n.getValues())}})}),e.jsx(F,{children:s("safe.form.registerLimit.expire.description")}),e.jsx(P,{})]})})]}),e.jsx(v,{control:n.control,name:"password_limit_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("safe.form.passwordLimit.enable.label")}),e.jsx(F,{children:s("safe.form.passwordLimit.enable.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),n.watch("password_limit_enable")&&e.jsxs(e.Fragment,{children:[e.jsx(v,{control:n.control,name:"password_limit_count",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("safe.form.passwordLimit.count.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("safe.form.passwordLimit.count.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),u(n.getValues())}})}),e.jsx(F,{children:s("safe.form.passwordLimit.count.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"password_limit_expire",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("safe.form.passwordLimit.expire.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("safe.form.passwordLimit.expire.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),u(n.getValues())}})}),e.jsx(F,{children:s("safe.form.passwordLimit.expire.description")}),e.jsx(P,{})]})})]}),a&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("safe.form.saving")})]})})}function mm(){const{t:s}=V("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("safe.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("safe.description")})]}),e.jsx(ke,{}),e.jsx(dm,{})]})}const um=Object.freeze(Object.defineProperty({__proto__:null,default:mm},Symbol.toStringTag,{value:"Module"})),xm=x.object({plan_change_enable:x.boolean().nullable().default(!1),reset_traffic_method:x.coerce.number().nullable().default(0),surplus_enable:x.boolean().nullable().default(!1),new_order_event_id:x.coerce.number().nullable().default(0),renew_order_event_id:x.coerce.number().nullable().default(0),change_order_event_id:x.coerce.number().nullable().default(0),show_info_to_server_enable:x.boolean().nullable().default(!1),show_protocol_to_server_enable:x.boolean().nullable().default(!1),default_remind_expire:x.boolean().nullable().default(!1),default_remind_traffic:x.boolean().nullable().default(!1),subscribe_path:x.string().nullable().default("s")}),hm={plan_change_enable:!1,reset_traffic_method:0,surplus_enable:!1,new_order_event_id:0,renew_order_event_id:0,change_order_event_id:0,show_info_to_server_enable:!1,show_protocol_to_server_enable:!1,default_remind_expire:!1,default_remind_traffic:!1,subscribe_path:"s"};function pm(){const{t:s}=V("settings"),[a,t]=m.useState(!1),l=m.useRef(null),n=ye({resolver:_e(xm),defaultValues:hm,mode:"onBlur"}),{data:o}=ne({queryKey:["settings","subscribe"],queryFn:()=>me.getSettings("subscribe")}),{mutateAsync:r}=gs({mutationFn:me.saveSettings,onSuccess:i=>{i.data&&A.success(s("common.autoSaved"))}});m.useEffect(()=>{if(o?.data?.subscribe){const i=o?.data?.subscribe;Object.entries(i).forEach(([d,h])=>{n.setValue(d,h)}),l.current=i}},[o]);const c=m.useCallback(Se.debounce(async i=>{if(!Se.isEqual(i,l.current)){t(!0);try{await r(i),l.current=i}finally{t(!1)}}},1e3),[r]),u=m.useCallback(i=>{c(i)},[c]);return m.useEffect(()=>{const i=n.watch(d=>{u(d)});return()=>i.unsubscribe()},[n.watch,u]),e.jsx(we,{...n,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:n.control,name:"plan_change_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe.plan_change_enable.title")}),e.jsx(F,{children:s("subscribe.plan_change_enable.description")}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"reset_traffic_method",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe.reset_traffic_method.title")}),e.jsxs(X,{onValueChange:i.onChange,value:i.value?.toString()||"0",children:[e.jsx(b,{children:e.jsx(Y,{children:e.jsx(Z,{placeholder:"请选择重置方式"})})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:s("subscribe.reset_traffic_method.options.monthly_first")}),e.jsx($,{value:"1",children:s("subscribe.reset_traffic_method.options.monthly_reset")}),e.jsx($,{value:"2",children:s("subscribe.reset_traffic_method.options.no_reset")}),e.jsx($,{value:"3",children:s("subscribe.reset_traffic_method.options.yearly_first")}),e.jsx($,{value:"4",children:s("subscribe.reset_traffic_method.options.yearly_reset")})]})]}),e.jsx(F,{children:s("subscribe.reset_traffic_method.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"surplus_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe.surplus_enable.title")}),e.jsx(F,{children:s("subscribe.surplus_enable.description")}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"new_order_event_id",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe.new_order_event.title")}),e.jsx("div",{className:"relative w-max",children:e.jsx(b,{children:e.jsxs(X,{onValueChange:i.onChange,value:i.value?.toString(),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:"请选择"})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:s("subscribe.new_order_event.options.no_action")}),e.jsx($,{value:"1",children:s("subscribe.new_order_event.options.reset_traffic")})]})]})})}),e.jsx(F,{children:s("subscribe.new_order_event.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"renew_order_event_id",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe.renew_order_event.title")}),e.jsx("div",{className:"relative w-max",children:e.jsx(b,{children:e.jsxs(X,{onValueChange:i.onChange,value:i.value?.toString(),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:"请选择"})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:s("subscribe.renew_order_event.options.no_action")}),e.jsx($,{value:"1",children:s("subscribe.renew_order_event.options.reset_traffic")})]})]})})}),e.jsx(F,{children:s("subscribe.renew_order_event.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"change_order_event_id",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe.change_order_event.title")}),e.jsx("div",{className:"relative w-max",children:e.jsx(b,{children:e.jsxs(X,{onValueChange:i.onChange,value:i.value?.toString(),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:"请选择"})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:s("subscribe.change_order_event.options.no_action")}),e.jsx($,{value:"1",children:s("subscribe.change_order_event.options.reset_traffic")})]})]})})}),e.jsx(F,{children:s("subscribe.change_order_event.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"subscribe_path",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("subscribe.subscribe_path.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:"subscribe",...i,value:i.value||"",onChange:d=>{i.onChange(d),u(n.getValues())}})}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[s("subscribe.subscribe_path.description"),e.jsx("br",{}),s("subscribe.subscribe_path.current_format",{path:i.value||"s"})]}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"show_info_to_server_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("subscribe.show_info_to_server.title")}),e.jsx(F,{children:s("subscribe.show_info_to_server.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),e.jsx(v,{control:n.control,name:"show_protocol_to_server_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("subscribe.show_protocol_to_server.title")}),e.jsx(F,{children:s("subscribe.show_protocol_to_server.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),a&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("common.saving")})]})})}function gm(){const{t:s}=V("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("subscribe.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("subscribe.description")})]}),e.jsx(ke,{}),e.jsx(pm,{})]})}const fm=Object.freeze(Object.defineProperty({__proto__:null,default:gm},Symbol.toStringTag,{value:"Module"})),jm=x.object({invite_force:x.boolean().default(!1),invite_commission:x.coerce.string().default("0"),invite_gen_limit:x.coerce.string().default("0"),invite_never_expire:x.boolean().default(!1),commission_first_time_enable:x.boolean().default(!1),commission_auto_check_enable:x.boolean().default(!1),commission_withdraw_limit:x.coerce.string().default("0"),commission_withdraw_method:x.array(x.string()).default(["支付宝","USDT","Paypal"]),withdraw_close_enable:x.boolean().default(!1),commission_distribution_enable:x.boolean().default(!1),commission_distribution_l1:x.coerce.number().default(0),commission_distribution_l2:x.coerce.number().default(0),commission_distribution_l3:x.coerce.number().default(0)}),vm={invite_force:!1,invite_commission:"0",invite_gen_limit:"0",invite_never_expire:!1,commission_first_time_enable:!1,commission_auto_check_enable:!1,commission_withdraw_limit:"0",commission_withdraw_method:["支付宝","USDT","Paypal"],withdraw_close_enable:!1,commission_distribution_enable:!1,commission_distribution_l1:0,commission_distribution_l2:0,commission_distribution_l3:0};function bm(){const{t:s}=V("settings"),[a,t]=m.useState(!1),l=m.useRef(null),n=ye({resolver:_e(jm),defaultValues:vm,mode:"onBlur"}),{data:o}=ne({queryKey:["settings","invite"],queryFn:()=>me.getSettings("invite")}),{mutateAsync:r}=gs({mutationFn:me.saveSettings,onSuccess:i=>{i.data&&A.success(s("common.autoSaved"))}});m.useEffect(()=>{if(o?.data?.invite){const i=o?.data?.invite;Object.entries(i).forEach(([d,h])=>{typeof h=="number"?n.setValue(d,String(h)):n.setValue(d,h)}),l.current=i}},[o]);const c=m.useCallback(Se.debounce(async i=>{if(!Se.isEqual(i,l.current)){t(!0);try{await r(i),l.current=i}finally{t(!1)}}},1e3),[r]),u=m.useCallback(i=>{c(i)},[c]);return m.useEffect(()=>{const i=n.watch(d=>{u(d)});return()=>i.unsubscribe()},[n.watch,u]),e.jsx(we,{...n,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:n.control,name:"invite_force",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("invite.invite_force.title")}),e.jsx(F,{children:s("invite.invite_force.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),e.jsx(v,{control:n.control,name:"invite_commission",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("invite.invite_commission.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("invite.invite_commission.placeholder"),...i,value:i.value||""})}),e.jsx(F,{children:s("invite.invite_commission.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"invite_gen_limit",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("invite.invite_gen_limit.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("invite.invite_gen_limit.placeholder"),...i,value:i.value||""})}),e.jsx(F,{children:s("invite.invite_gen_limit.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"invite_never_expire",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("invite.invite_never_expire.title")}),e.jsx(F,{children:s("invite.invite_never_expire.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),e.jsx(v,{control:n.control,name:"commission_first_time_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("invite.commission_first_time.title")}),e.jsx(F,{children:s("invite.commission_first_time.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),e.jsx(v,{control:n.control,name:"commission_auto_check_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("invite.commission_auto_check.title")}),e.jsx(F,{children:s("invite.commission_auto_check.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),e.jsx(v,{control:n.control,name:"commission_withdraw_limit",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("invite.commission_withdraw_limit.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("invite.commission_withdraw_limit.placeholder"),...i,value:i.value||""})}),e.jsx(F,{children:s("invite.commission_withdraw_limit.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"commission_withdraw_method",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("invite.commission_withdraw_method.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("invite.commission_withdraw_method.placeholder"),...i,value:Array.isArray(i.value)?i.value.join(","):"",onChange:d=>{const h=d.target.value.split(",").filter(Boolean);i.onChange(h),u(n.getValues())}})}),e.jsx(F,{children:s("invite.commission_withdraw_method.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"withdraw_close_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("invite.withdraw_close.title")}),e.jsx(F,{children:s("invite.withdraw_close.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),e.jsx(v,{control:n.control,name:"commission_distribution_enable",render:({field:i})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("invite.commission_distribution.title")}),e.jsx(F,{children:s("invite.commission_distribution.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:i.value,onCheckedChange:d=>{i.onChange(d),u(n.getValues())}})})]})}),n.watch("commission_distribution_enable")&&e.jsxs(e.Fragment,{children:[e.jsx(v,{control:n.control,name:"commission_distribution_l1",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:s("invite.commission_distribution.l1")}),e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:s("invite.commission_distribution.placeholder"),...i,value:i.value||"",onChange:d=>{const h=d.target.value?Number(d.target.value):0;i.onChange(h),u(n.getValues())}})}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"commission_distribution_l2",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:s("invite.commission_distribution.l2")}),e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:s("invite.commission_distribution.placeholder"),...i,value:i.value||"",onChange:d=>{const h=d.target.value?Number(d.target.value):0;i.onChange(h),u(n.getValues())}})}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"commission_distribution_l3",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:s("invite.commission_distribution.l3")}),e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:s("invite.commission_distribution.placeholder"),...i,value:i.value||"",onChange:d=>{const h=d.target.value?Number(d.target.value):0;i.onChange(h),u(n.getValues())}})}),e.jsx(P,{})]})})]}),a&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("invite.saving")})]})})}function ym(){const{t:s}=V("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("invite.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("invite.description")})]}),e.jsx(ke,{}),e.jsx(bm,{})]})}const Nm=Object.freeze(Object.defineProperty({__proto__:null,default:ym},Symbol.toStringTag,{value:"Module"})),_m=x.object({frontend_theme:x.string().nullable(),frontend_theme_sidebar:x.string().nullable(),frontend_theme_header:x.string().nullable(),frontend_theme_color:x.string().nullable(),frontend_background_url:x.string().url().nullable()}),wm={frontend_theme:"",frontend_theme_sidebar:"",frontend_theme_header:"",frontend_theme_color:"",frontend_background_url:""};function Cm(){const{data:s}=ne({queryKey:["settings","frontend"],queryFn:()=>me.getSettings("frontend")}),a=ye({resolver:_e(_m),defaultValues:wm,mode:"onChange"});m.useEffect(()=>{if(s?.data?.frontend){const l=s?.data?.frontend;Object.entries(l).forEach(([n,o])=>{a.setValue(n,o)})}},[s]);function t(l){me.saveSettings(l).then(({data:n})=>{n&&A.success("更新成功")})}return e.jsx(we,{...a,children:e.jsxs("form",{onSubmit:a.handleSubmit(t),className:"space-y-8",children:[e.jsx(v,{control:a.control,name:"frontend_theme_sidebar",render:({field:l})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:"边栏风格"}),e.jsx(F,{children:"边栏风格"})]}),e.jsx(b,{children:e.jsx(ee,{checked:l.value,onCheckedChange:l.onChange})})]})}),e.jsx(v,{control:a.control,name:"frontend_theme_header",render:({field:l})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:"头部风格"}),e.jsx(F,{children:"边栏风格"})]}),e.jsx(b,{children:e.jsx(ee,{checked:l.value,onCheckedChange:l.onChange})})]})}),e.jsx(v,{control:a.control,name:"frontend_theme_color",render:({field:l})=>e.jsxs(f,{children:[e.jsx(j,{children:"主题色"}),e.jsxs("div",{className:"relative w-max",children:[e.jsx(b,{children:e.jsxs("select",{className:y(wt({variant:"outline"}),"w-[200px] appearance-none font-normal"),...l,children:[e.jsx("option",{value:"default",children:"默认"}),e.jsx("option",{value:"black",children:"黑色"}),e.jsx("option",{value:"blackblue",children:"暗蓝色"}),e.jsx("option",{value:"green",children:"奶绿色"})]})}),e.jsx(pn,{className:"absolute right-3 top-2.5 h-4 w-4 opacity-50"})]}),e.jsx(F,{children:"主题色"}),e.jsx(P,{})]})}),e.jsx(v,{control:a.control,name:"frontend_background_url",render:({field:l})=>e.jsxs(f,{children:[e.jsx(j,{children:"背景"}),e.jsx(b,{children:e.jsx(D,{placeholder:"请输入图片地址",...l})}),e.jsx(F,{children:"将会在后台登录页面进行展示。"}),e.jsx(P,{})]})}),e.jsx(E,{type:"submit",children:"保存设置"})]})})}function Sm(){return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"个性化设置"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"自定义系统界面外观，包括主题风格、布局、颜色方案、背景图等个性化选项。"})]}),e.jsx(ke,{}),e.jsx(Cm,{})]})}const km=Object.freeze(Object.defineProperty({__proto__:null,default:Sm},Symbol.toStringTag,{value:"Module"})),Tm=x.object({server_pull_interval:x.coerce.number().nullable(),server_push_interval:x.coerce.number().nullable(),server_token:x.string().nullable(),device_limit_mode:x.coerce.number().nullable()}),Dm={server_pull_interval:0,server_push_interval:0,server_token:"",device_limit_mode:0};function Pm(){const{t:s}=V("settings"),[a,t]=m.useState(!1),l=m.useRef(null),n=ye({resolver:_e(Tm),defaultValues:Dm,mode:"onBlur"}),{data:o}=ne({queryKey:["settings","server"],queryFn:()=>me.getSettings("server")}),{mutateAsync:r}=gs({mutationFn:me.saveSettings,onSuccess:d=>{d.data&&A.success(s("common.AutoSaved"))}});m.useEffect(()=>{if(o?.data.server){const d=o.data.server;Object.entries(d).forEach(([h,_])=>{n.setValue(h,_)}),l.current=d}},[o]);const c=m.useCallback(Se.debounce(async d=>{if(!Se.isEqual(d,l.current)){t(!0);try{await r(d),l.current=d}finally{t(!1)}}},1e3),[r]),u=m.useCallback(d=>{c(d)},[c]);m.useEffect(()=>{const d=n.watch(h=>{u(h)});return()=>d.unsubscribe()},[n.watch,u]);const i=()=>{const d=Math.floor(Math.random()*17)+16,h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";let _="";for(let T=0;T<d;T++)_+=h.charAt(Math.floor(Math.random()*h.length));n.setValue("server_token",_)};return e.jsx(we,{...n,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:n.control,name:"server_token",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("server.server_token.title")}),e.jsx(b,{children:e.jsxs("div",{className:"relative",children:[e.jsx(D,{placeholder:s("server.server_token.placeholder"),...d,value:d.value||"",className:"pr-10"}),e.jsx(be,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(G,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2",onClick:h=>{h.preventDefault(),i()},children:e.jsx(lc,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})})}),e.jsx(de,{children:e.jsx("p",{children:s("server.server_token.generate_tooltip")})})]})})]})}),e.jsx(F,{children:s("server.server_token.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"server_pull_interval",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("server.server_pull_interval.title")}),e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:s("server.server_pull_interval.placeholder"),...d,value:d.value||"",onChange:h=>{const _=h.target.value?Number(h.target.value):null;d.onChange(_)}})}),e.jsx(F,{children:s("server.server_pull_interval.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"server_push_interval",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("server.server_push_interval.title")}),e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:s("server.server_push_interval.placeholder"),...d,value:d.value||"",onChange:h=>{const _=h.target.value?Number(h.target.value):null;d.onChange(_)}})}),e.jsx(F,{children:s("server.server_push_interval.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"device_limit_mode",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("server.device_limit_mode.title")}),e.jsxs(X,{onValueChange:d.onChange,value:d.value?.toString()||"0",children:[e.jsx(b,{children:e.jsx(Y,{children:e.jsx(Z,{placeholder:s("server.device_limit_mode.placeholder")})})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:s("server.device_limit_mode.strict")}),e.jsx($,{value:"1",children:s("server.device_limit_mode.relaxed")})]})]}),e.jsx(F,{children:s("server.device_limit_mode.description")}),e.jsx(P,{})]})}),a&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("server.saving")})]})})}function Em(){const{t:s}=V("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("server.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("server.description")})]}),e.jsx(ke,{}),e.jsx(Pm,{})]})}const Rm=Object.freeze(Object.defineProperty({__proto__:null,default:Em},Symbol.toStringTag,{value:"Module"}));function Im({open:s,onOpenChange:a,result:t}){const l=!t.error;return e.jsx(pe,{open:s,onOpenChange:a,children:e.jsxs(ue,{className:"sm:max-w-[425px]",children:[e.jsxs(ve,{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[l?e.jsx(ql,{className:"h-5 w-5 text-green-500"}):e.jsx(Hl,{className:"h-5 w-5 text-destructive"}),e.jsx(ge,{children:l?"邮件发送成功":"邮件发送失败"})]}),e.jsx(Le,{children:l?"测试邮件已成功发送，请检查收件箱":"发送测试邮件时遇到错误"})]}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx("div",{className:"font-medium",children:"发送详情"}),e.jsxs("div",{className:"grid grid-cols-[100px_1fr] items-center gap-2 text-sm",children:[e.jsx("div",{className:"text-muted-foreground",children:"收件地址"}),e.jsx("div",{children:t.email}),e.jsx("div",{className:"text-muted-foreground",children:"邮件主题"}),e.jsx("div",{children:t.subject}),e.jsx("div",{className:"text-muted-foreground",children:"模板名称"}),e.jsx("div",{children:t.template_name})]})]}),t.error&&e.jsxs("div",{className:"grid gap-2",children:[e.jsx("div",{className:"font-medium text-destructive",children:"错误信息"}),e.jsx("div",{className:"rounded-md bg-destructive/10 p-3 text-sm text-destructive break-all",children:t.error})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx("div",{className:"font-medium",children:"配置信息"}),e.jsx(Nt,{className:"h-[200px] rounded-md border p-4",children:e.jsx("div",{className:"grid gap-2 text-sm",children:e.jsxs("div",{className:"grid grid-cols-[100px_1fr] items-center gap-2",children:[e.jsx("div",{className:"text-muted-foreground",children:"驱动"}),e.jsx("div",{children:t.config.driver}),e.jsx("div",{className:"text-muted-foreground",children:"服务器"}),e.jsx("div",{children:t.config.host}),e.jsx("div",{className:"text-muted-foreground",children:"端口"}),e.jsx("div",{children:t.config.port}),e.jsx("div",{className:"text-muted-foreground",children:"加密方式"}),e.jsx("div",{children:t.config.encryption||"无"}),e.jsx("div",{className:"text-muted-foreground",children:"发件人"}),e.jsx("div",{children:t.config.from.address?`${t.config.from.address}${t.config.from.name?` (${t.config.from.name})`:""}`:"未设置"}),e.jsx("div",{className:"text-muted-foreground",children:"用户名"}),e.jsx("div",{children:t.config.username||"未设置"})]})})})]})]})]})})}const Lm=x.object({email_template:x.string().nullable().default("classic"),email_host:x.string().nullable().default(""),email_port:x.coerce.number().nullable().default(465),email_username:x.string().nullable().default(""),email_password:x.string().nullable().default(""),email_encryption:x.string().nullable().default(""),email_from_address:x.string().email().nullable().default(""),remind_mail_enable:x.boolean().nullable().default(!1)});function Vm(){const{t:s}=V("settings"),[a,t]=m.useState(null),[l,n]=m.useState(!1),o=m.useRef(null),[r,c]=m.useState(!1),u=ye({resolver:_e(Lm),defaultValues:{},mode:"onBlur"}),{data:i}=ne({queryKey:["settings","email"],queryFn:()=>me.getSettings("email")}),{data:d}=ne({queryKey:["emailTemplate"],queryFn:()=>me.getEmailTemplate()}),{mutateAsync:h}=gs({mutationFn:me.saveSettings,onSuccess:N=>{N.data&&A.success(s("common.autoSaved"))}}),{mutate:_,isPending:T}=gs({mutationFn:me.sendTestMail,onMutate:()=>{t(null),n(!1)},onSuccess:N=>{t(N.data),n(!0),N.data.error?A.error(s("email.test.error")):A.success(s("email.test.success"))}});m.useEffect(()=>{if(i?.data.email){const N=i.data.email;Object.entries(N).forEach(([g,k])=>{u.setValue(g,k)}),o.current=N}},[i]);const S=m.useCallback(Se.debounce(async N=>{if(!Se.isEqual(N,o.current)){c(!0);try{await h(N),o.current=N}finally{c(!1)}}},1e3),[h]),C=m.useCallback(N=>{S(N)},[S]);return m.useEffect(()=>{const N=u.watch(g=>{C(g)});return()=>N.unsubscribe()},[u.watch,C]),e.jsxs(e.Fragment,{children:[e.jsx(we,{...u,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:u.control,name:"email_host",render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("email.email_host.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("common.placeholder"),...N,value:N.value||""})}),e.jsx(F,{children:s("email.email_host.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"email_port",render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("email.email_port.title")}),e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:s("common.placeholder"),...N,value:N.value||"",onChange:g=>{const k=g.target.value?Number(g.target.value):null;N.onChange(k)}})}),e.jsx(F,{children:s("email.email_port.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"email_encryption",render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("email.email_encryption.title")}),e.jsxs(X,{onValueChange:g=>{const k=g==="none"?"":g;N.onChange(k)},value:N.value===""||N.value===null||N.value===void 0?"none":N.value,children:[e.jsx(b,{children:e.jsx(Y,{children:e.jsx(Z,{placeholder:"请选择加密方式"})})}),e.jsxs(J,{children:[e.jsx($,{value:"none",children:s("email.email_encryption.none")}),e.jsx($,{value:"ssl",children:s("email.email_encryption.ssl")}),e.jsx($,{value:"tls",children:s("email.email_encryption.tls")})]})]}),e.jsx(F,{children:s("email.email_encryption.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"email_username",render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("email.email_username.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("common.placeholder"),...N,value:N.value||""})}),e.jsx(F,{children:s("email.email_username.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"email_password",render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("email.email_password.title")}),e.jsx(b,{children:e.jsx(D,{type:"password",placeholder:s("common.placeholder"),...N,value:N.value||""})}),e.jsx(F,{children:s("email.email_password.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"email_from_address",render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("email.email_from.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("common.placeholder"),...N,value:N.value||""})}),e.jsx(F,{children:s("email.email_from.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"email_template",render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("email.email_template.title")}),e.jsxs(X,{onValueChange:g=>{N.onChange(g),C(u.getValues())},value:N.value||void 0,children:[e.jsx(b,{children:e.jsx(Y,{className:"w-[200px]",children:e.jsx(Z,{placeholder:s("email.email_template.placeholder")})})}),e.jsx(J,{children:d?.data?.map(g=>e.jsx($,{value:g,children:g},g))})]}),e.jsx(F,{children:s("email.email_template.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"remind_mail_enable",render:({field:N})=>e.jsxs(f,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:s("email.remind_mail.title")}),e.jsx(F,{children:s("email.remind_mail.description")})]}),e.jsx(b,{children:e.jsx(ee,{checked:N.value||!1,onCheckedChange:g=>{N.onChange(g),C(u.getValues())}})})]})}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(E,{onClick:()=>_(),loading:T,disabled:T,children:s(T?"email.test.sending":"email.test.title")})})]})}),r&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("saving")}),a&&e.jsx(Im,{open:l,onOpenChange:n,result:a})]})}function Fm(){const{t:s}=V("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("email.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("email.description")})]}),e.jsx(ke,{}),e.jsx(Vm,{})]})}const Mm=Object.freeze(Object.defineProperty({__proto__:null,default:Fm},Symbol.toStringTag,{value:"Module"})),Om=x.object({telegram_bot_enable:x.boolean().nullable(),telegram_bot_token:x.string().nullable(),telegram_discuss_link:x.string().nullable()}),zm={telegram_bot_enable:!1,telegram_bot_token:"",telegram_discuss_link:""};function $m(){const{t:s}=V("settings"),[a,t]=m.useState(!1),l=m.useRef(null),n=ye({resolver:_e(Om),defaultValues:zm,mode:"onBlur"}),{data:o}=ne({queryKey:["settings","telegram"],queryFn:()=>me.getSettings("telegram")}),{mutateAsync:r}=gs({mutationFn:me.saveSettings,onSuccess:h=>{h.data&&A.success(s("common.autoSaved"))}}),{mutate:c,isPending:u}=gs({mutationFn:me.setTelegramWebhook,onSuccess:h=>{h.data&&A.success(s("telegram.webhook.success"))}});m.useEffect(()=>{if(o?.data.telegram){const h=o.data.telegram;Object.entries(h).forEach(([_,T])=>{n.setValue(_,T)}),l.current=h}},[o]);const i=m.useCallback(Se.debounce(async h=>{if(!Se.isEqual(h,l.current)){t(!0);try{await r(h),l.current=h}finally{t(!1)}}},1e3),[r]),d=m.useCallback(h=>{i(h)},[i]);return m.useEffect(()=>{const h=n.watch(_=>{d(_)});return()=>h.unsubscribe()},[n.watch,d]),e.jsx(we,{...n,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:n.control,name:"telegram_bot_token",render:({field:h})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("telegram.bot_token.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("telegram.bot_token.placeholder"),...h,value:h.value||""})}),e.jsx(F,{children:s("telegram.bot_token.description")}),e.jsx(P,{})]})}),n.watch("telegram_bot_token")&&e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("telegram.webhook.title")}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(E,{loading:u,disabled:u,onClick:()=>c(),children:s(u?"telegram.webhook.setting":"telegram.webhook.button")}),a&&e.jsx("span",{className:"text-sm text-muted-foreground",children:s("common.saving")})]}),e.jsx(F,{children:s("telegram.webhook.description")}),e.jsx(P,{})]}),e.jsx(v,{control:n.control,name:"telegram_bot_enable",render:({field:h})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("telegram.bot_enable.title")}),e.jsx(F,{children:s("telegram.bot_enable.description")}),e.jsx(b,{children:e.jsx(ee,{checked:h.value||!1,onCheckedChange:_=>{h.onChange(_),d(n.getValues())}})}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"telegram_discuss_link",render:({field:h})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("telegram.discuss_link.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("telegram.discuss_link.placeholder"),...h,value:h.value||""})}),e.jsx(F,{children:s("telegram.discuss_link.description")}),e.jsx(P,{})]})}),a&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("common.saving")})]})})}function Am(){const{t:s}=V("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("telegram.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("telegram.description")})]}),e.jsx(ke,{}),e.jsx($m,{})]})}const qm=Object.freeze(Object.defineProperty({__proto__:null,default:Am},Symbol.toStringTag,{value:"Module"})),Hm=x.object({windows_version:x.string().nullable(),windows_download_url:x.string().nullable(),macos_version:x.string().nullable(),macos_download_url:x.string().nullable(),android_version:x.string().nullable(),android_download_url:x.string().nullable()}),Um={windows_version:"",windows_download_url:"",macos_version:"",macos_download_url:"",android_version:"",android_download_url:""};function Km(){const{t:s}=V("settings"),[a,t]=m.useState(!1),l=m.useRef(null),n=ye({resolver:_e(Hm),defaultValues:Um,mode:"onBlur"}),{data:o}=ne({queryKey:["settings","app"],queryFn:()=>me.getSettings("app")}),{mutateAsync:r}=gs({mutationFn:me.saveSettings,onSuccess:i=>{i.data&&A.success(s("app.save_success"))}});m.useEffect(()=>{if(o?.data.app){const i=o.data.app;Object.entries(i).forEach(([d,h])=>{n.setValue(d,h)}),l.current=i}},[o]);const c=m.useCallback(Se.debounce(async i=>{if(!Se.isEqual(i,l.current)){t(!0);try{await r(i),l.current=i}finally{t(!1)}}},1e3),[r]),u=m.useCallback(i=>{c(i)},[c]);return m.useEffect(()=>{const i=n.watch(d=>{u(d)});return()=>i.unsubscribe()},[n.watch,u]),e.jsx(we,{...n,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:n.control,name:"windows_version",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("app.windows.version.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(F,{children:s("app.windows.version.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"windows_download_url",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("app.windows.download.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(F,{children:s("app.windows.download.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"macos_version",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("app.macos.version.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(F,{children:s("app.macos.version.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"macos_download_url",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("app.macos.download.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(F,{children:s("app.macos.download.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"android_version",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("app.android.version.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(F,{children:s("app.android.version.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"android_download_url",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{className:"text-base",children:s("app.android.download.title")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(F,{children:s("app.android.download.description")}),e.jsx(P,{})]})}),a&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("common.saving")})]})})}function Bm(){const{t:s}=V("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("app.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("app.description")})]}),e.jsx(ke,{}),e.jsx(Km,{})]})}const Gm=Object.freeze(Object.defineProperty({__proto__:null,default:Bm},Symbol.toStringTag,{value:"Module"})),Wm=s=>x.object({id:x.number().nullable(),name:x.string().min(2,s("form.validation.name.min")).max(30,s("form.validation.name.max")),icon:x.string().optional().nullable(),notify_domain:x.string().refine(t=>!t||/^https?:\/\/\S+/.test(t),s("form.validation.notify_domain.url")).optional().nullable(),handling_fee_fixed:x.coerce.number().min(0).optional().nullable(),handling_fee_percent:x.coerce.number().min(0).max(100).optional().nullable(),payment:x.string().min(1,s("form.validation.payment.required")),config:x.record(x.string(),x.string())}),Wn={id:null,name:"",icon:"",notify_domain:"",handling_fee_fixed:0,handling_fee_percent:0,payment:"",config:{}};function kr({refetch:s,dialogTrigger:a,type:t="add",defaultFormValues:l=Wn}){const{t:n}=V("payment"),[o,r]=m.useState(!1),[c,u]=m.useState(!1),[i,d]=m.useState([]),[h,_]=m.useState([]),T=Wm(n),S=ye({resolver:_e(T),defaultValues:l,mode:"onChange"}),C=S.watch("payment");m.useEffect(()=>{o&&(async()=>{const{data:k}=await Qs.getMethodList();d(k)})()},[o]),m.useEffect(()=>{if(!C||!o)return;(async()=>{const k={payment:C,...t==="edit"&&{id:Number(S.getValues("id"))}};Qs.getMethodForm(k).then(({data:R})=>{_(R);const p=R.reduce((w,I)=>(I.field_name&&(w[I.field_name]=I.value??""),w),{});S.setValue("config",p)})})()},[C,o,S,t]);const N=async g=>{u(!0);try{(await Qs.save(g)).data&&(A.success(n("form.messages.success")),S.reset(Wn),s(),r(!1))}finally{u(!1)}};return e.jsxs(pe,{open:o,onOpenChange:r,children:[e.jsx(rs,{asChild:!0,children:a||e.jsxs(E,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(ze,{icon:"ion:add"})," ",e.jsx("div",{children:n("form.add.button")})]})}),e.jsxs(ue,{className:"sm:max-w-[425px]",children:[e.jsx(ve,{children:e.jsx(ge,{children:n(t==="add"?"form.add.title":"form.edit.title")})}),e.jsx(we,{...S,children:e.jsxs("form",{onSubmit:S.handleSubmit(N),className:"space-y-4",children:[e.jsx(v,{control:S.control,name:"name",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:n("form.fields.name.placeholder"),...g})}),e.jsx(F,{children:n("form.fields.name.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:S.control,name:"icon",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.icon.label")}),e.jsx(b,{children:e.jsx(D,{...g,value:g.value||"",placeholder:n("form.fields.icon.placeholder")})}),e.jsx(F,{children:n("form.fields.icon.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:S.control,name:"notify_domain",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.notify_domain.label")}),e.jsx(b,{children:e.jsx(D,{...g,value:g.value||"",placeholder:n("form.fields.notify_domain.placeholder")})}),e.jsx(F,{children:n("form.fields.notify_domain.description")}),e.jsx(P,{})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(v,{control:S.control,name:"handling_fee_percent",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.handling_fee_percent.label")}),e.jsx(b,{children:e.jsx(D,{type:"number",...g,value:g.value||"",placeholder:n("form.fields.handling_fee_percent.placeholder")})}),e.jsx(P,{})]})}),e.jsx(v,{control:S.control,name:"handling_fee_fixed",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.handling_fee_fixed.label")}),e.jsx(b,{children:e.jsx(D,{type:"number",...g,value:g.value||"",placeholder:n("form.fields.handling_fee_fixed.placeholder")})}),e.jsx(P,{})]})})]}),e.jsx(v,{control:S.control,name:"payment",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.payment.label")}),e.jsxs(X,{onValueChange:g.onChange,defaultValue:g.value,children:[e.jsx(b,{children:e.jsx(Y,{children:e.jsx(Z,{placeholder:n("form.fields.payment.placeholder")})})}),e.jsx(J,{children:i.map(k=>e.jsx($,{value:k,children:k},k))})]}),e.jsx(F,{children:n("form.fields.payment.description")}),e.jsx(P,{})]})}),h.length>0&&e.jsx("div",{className:"space-y-4",children:h.map(g=>e.jsx(v,{control:S.control,name:`config.${g.field_name}`,render:({field:k})=>e.jsxs(f,{children:[e.jsx(j,{children:g.label}),e.jsx(b,{children:e.jsx(D,{...k,value:k.value||""})}),e.jsx(P,{})]})},g.field_name))}),e.jsxs(Re,{children:[e.jsx(qs,{asChild:!0,children:e.jsx(E,{type:"button",variant:"outline",children:n("form.buttons.cancel")})}),e.jsx(E,{type:"submit",disabled:c,children:n("form.buttons.submit")})]})]})})]})]})}function z({column:s,title:a,tooltip:t,className:l}){return s.getCanSort()?e.jsx("div",{className:"flex items-center gap-1",children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(E,{variant:"ghost",size:"default",className:y("-ml-3 flex h-8 items-center gap-2 text-nowrap font-medium hover:bg-muted/60",l),onClick:()=>s.toggleSorting(s.getIsSorted()==="asc"),children:[e.jsx("span",{children:a}),t&&e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(An,{className:"h-4 w-4 cursor-pointer text-muted-foreground"})}),e.jsx(de,{children:t})]})}),s.getIsSorted()==="asc"?e.jsx(Xa,{className:"h-4 w-4 text-foreground/70"}):s.getIsSorted()==="desc"?e.jsx(Za,{className:"h-4 w-4 text-foreground/70"}):e.jsx(rc,{className:"h-4 w-4 text-muted-foreground/70 transition-colors hover:text-foreground/70"})]})})}):e.jsxs("div",{className:y("flex items-center space-x-1 text-nowrap py-2 font-medium text-muted-foreground",l),children:[e.jsx("span",{children:a}),t&&e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{children:e.jsx(An,{className:"h-4 w-4 text-muted-foreground"})}),e.jsx(de,{children:t})]})})]})}const Cn=ic,Tr=oc,Ym=cc,Dr=m.forwardRef(({className:s,...a},t)=>e.jsx(Kl,{className:y("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...a,ref:t}));Dr.displayName=Kl.displayName;const Ta=m.forwardRef(({className:s,...a},t)=>e.jsxs(Ym,{children:[e.jsx(Dr,{}),e.jsx(Bl,{ref:t,className:y("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...a})]}));Ta.displayName=Bl.displayName;const Da=({className:s,...a})=>e.jsx("div",{className:y("flex flex-col space-y-2 text-center sm:text-left",s),...a});Da.displayName="AlertDialogHeader";const Pa=({className:s,...a})=>e.jsx("div",{className:y("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a});Pa.displayName="AlertDialogFooter";const Ea=m.forwardRef(({className:s,...a},t)=>e.jsx(Gl,{ref:t,className:y("text-lg font-semibold",s),...a}));Ea.displayName=Gl.displayName;const Ra=m.forwardRef(({className:s,...a},t)=>e.jsx(Wl,{ref:t,className:y("text-sm text-muted-foreground",s),...a}));Ra.displayName=Wl.displayName;const Ia=m.forwardRef(({className:s,...a},t)=>e.jsx(Yl,{ref:t,className:y(bt(),s),...a}));Ia.displayName=Yl.displayName;const La=m.forwardRef(({className:s,...a},t)=>e.jsx(Jl,{ref:t,className:y(bt({variant:"outline"}),"mt-2 sm:mt-0",s),...a}));La.displayName=Jl.displayName;function ns({onConfirm:s,children:a,title:t="确认操作",description:l="确定要执行此操作吗？",cancelText:n="取消",confirmText:o="确认",variant:r="default",className:c}){return e.jsxs(Cn,{children:[e.jsx(Tr,{asChild:!0,children:a}),e.jsxs(Ta,{className:y("sm:max-w-[425px]",c),children:[e.jsxs(Da,{children:[e.jsx(Ea,{children:t}),e.jsx(Ra,{children:l})]}),e.jsxs(Pa,{children:[e.jsx(La,{asChild:!0,children:e.jsx(E,{variant:"outline",children:n})}),e.jsx(Ia,{asChild:!0,children:e.jsx(E,{variant:r,onClick:s,children:o})})]})]})]})}const Pr=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M11.29 15.29a2 2 0 0 0-.12.15a.8.8 0 0 0-.09.18a.6.6 0 0 0-.06.18a1.4 1.4 0 0 0 0 .2a.84.84 0 0 0 .08.38a.9.9 0 0 0 .54.54a.94.94 0 0 0 .76 0a.9.9 0 0 0 .54-.54A1 1 0 0 0 13 16a1 1 0 0 0-.29-.71a1 1 0 0 0-1.42 0M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2m0 18a8 8 0 1 1 8-8a8 8 0 0 1-8 8m0-13a3 3 0 0 0-2.6 1.5a1 1 0 1 0 1.73 1A1 1 0 0 1 12 9a1 1 0 0 1 0 2a1 1 0 0 0-1 1v1a1 1 0 0 0 2 0v-.18A3 3 0 0 0 12 7"})}),Jm=({refetch:s,isSortMode:a=!1})=>{const{t}=V("payment");return[{id:"drag-handle",header:()=>null,cell:()=>e.jsx("div",{className:a?"cursor-move":"opacity-0",children:e.jsx(Na,{className:"size-4"})}),size:40,enableSorting:!1},{accessorKey:"id",header:({column:l})=>e.jsx(z,{column:l,title:t("table.columns.id")}),cell:({row:l})=>e.jsx(B,{variant:"outline",children:l.getValue("id")}),enableSorting:!0,size:60},{accessorKey:"enable",header:({column:l})=>e.jsx(z,{column:l,title:t("table.columns.enable")}),cell:({row:l})=>e.jsx(ee,{defaultChecked:l.getValue("enable"),onCheckedChange:async()=>{const{data:n}=await Qs.updateStatus({id:l.original.id});n||s()}}),enableSorting:!1,size:100},{accessorKey:"name",header:({column:l})=>e.jsx(z,{column:l,title:t("table.columns.name")}),cell:({row:l})=>e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"max-w-[200px] truncate font-medium",children:l.getValue("name")})}),enableSorting:!1,size:200},{accessorKey:"payment",header:({column:l})=>e.jsx(z,{column:l,title:t("table.columns.payment")}),cell:({row:l})=>e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"max-w-[200px] truncate font-medium",children:l.getValue("payment")})}),enableSorting:!1,size:200},{accessorKey:"notify_url",header:({column:l})=>e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{column:l,title:t("table.columns.notify_url")}),e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{className:"ml-1",children:e.jsx(Pr,{className:"h-4 w-4"})}),e.jsx(de,{children:t("table.columns.notify_url_tooltip")})]})})]}),cell:({row:l})=>e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"max-w-[300px] truncate font-medium",children:l.getValue("notify_url")})}),enableSorting:!1,size:3e3},{id:"actions",header:({column:l})=>e.jsx(z,{className:"justify-end",column:l,title:t("table.columns.actions")}),cell:({row:l})=>e.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[e.jsx(kr,{refetch:s,dialogTrigger:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:[e.jsx(tt,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:t("table.actions.edit")})]}),type:"edit",defaultFormValues:l.original}),e.jsx(ns,{title:t("table.actions.delete.title"),description:t("table.actions.delete.description"),onConfirm:async()=>{const{data:n}=await Qs.drop({id:l.original.id});n&&s()},children:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-destructive/10",children:[e.jsx(fs,{className:"h-4 w-4 text-muted-foreground hover:text-destructive"}),e.jsx("span",{className:"sr-only",children:t("table.actions.delete.title")})]})})]}),size:100}]};function Qm({table:s,refetch:a,saveOrder:t,isSortMode:l}){const{t:n}=V("payment"),o=s.getState().columnFilters.length>0;return e.jsxs("div",{className:"flex items-center justify-between",children:[l?e.jsx("p",{className:"text-sm text-muted-foreground",children:n("table.toolbar.sort.hint")}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(kr,{refetch:a}),e.jsx(D,{placeholder:n("table.toolbar.search"),value:s.getColumn("name")?.getFilterValue()??"",onChange:r=>s.getColumn("name")?.setFilterValue(r.target.value),className:"h-8 w-[250px]"}),o&&e.jsxs(E,{variant:"ghost",onClick:()=>s.resetColumnFilters(),children:[n("table.toolbar.reset"),e.jsx(ds,{className:"ml-2 h-4 w-4"})]})]}),s.getRowCount()>0&&e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(E,{variant:l?"default":"outline",onClick:t,size:"sm",children:n(l?"table.toolbar.sort.save":"table.toolbar.sort.edit")})})]})}function Xm(){const[s,a]=m.useState([]),[t,l]=m.useState([]),[n,o]=m.useState(!1),[r,c]=m.useState([]),[u,i]=m.useState({"drag-handle":!1}),[d,h]=m.useState({pageSize:20,pageIndex:0}),{refetch:_}=ne({queryKey:["paymentList"],queryFn:async()=>{const{data:g}=await Qs.getList();return c(g?.map(k=>({...k,enable:!!k.enable}))||[]),g}});m.useEffect(()=>{i({"drag-handle":n,actions:!n}),h({pageSize:n?99999:10,pageIndex:0})},[n]);const T=(g,k)=>{n&&(g.dataTransfer.setData("text/plain",k.toString()),g.currentTarget.classList.add("opacity-50"))},S=(g,k)=>{if(!n)return;g.preventDefault(),g.currentTarget.classList.remove("bg-muted");const R=parseInt(g.dataTransfer.getData("text/plain"));if(R===k)return;const p=[...r],[w]=p.splice(R,1);p.splice(k,0,w),c(p)},C=async()=>{n?Qs.sort({ids:r.map(g=>g.id)}).then(()=>{_(),o(!1),A.success("排序保存成功")}):o(!0)},N=Je({data:r,columns:Jm({refetch:_,isSortMode:n}),state:{sorting:t,columnFilters:s,columnVisibility:u,pagination:d},onSortingChange:l,onColumnFiltersChange:a,onColumnVisibilityChange:i,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),getSortedRowModel:vs(),initialState:{columnPinning:{right:["actions"]}},pageCount:n?1:void 0});return e.jsx(is,{table:N,toolbar:g=>e.jsx(Qm,{table:g,refetch:_,saveOrder:C,isSortMode:n}),draggable:n,onDragStart:T,onDragEnd:g=>g.currentTarget.classList.remove("opacity-50"),onDragOver:g=>{g.preventDefault(),g.currentTarget.classList.add("bg-muted")},onDragLeave:g=>g.currentTarget.classList.remove("bg-muted"),onDrop:S,showPagination:!n})}function Zm(){const{t:s}=V("payment");return e.jsxs(Ve,{children:[e.jsxs(Fe,{className:"flex items-center justify-between",children:[e.jsx(Xe,{}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{children:[e.jsx("header",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("div",{className:"mb-2",children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")})}),e.jsx("p",{className:"text-muted-foreground",children:s("description")})]})}),e.jsx("section",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(Xm,{})})]})]})}const eu=Object.freeze(Object.defineProperty({__proto__:null,default:Zm},Symbol.toStringTag,{value:"Module"}));function su({pluginName:s,onClose:a,onSuccess:t}){const{t:l}=V("plugin"),[n,o]=m.useState(!0),[r,c]=m.useState(!1),[u,i]=m.useState(null),d=dc({config:mc(uc())}),h=ye({resolver:_e(d),defaultValues:{config:{}}});m.useEffect(()=>{(async()=>{try{const{data:C}=await Ps.getPluginConfig(s);i(C),h.reset({config:Object.fromEntries(Object.entries(C).map(([N,g])=>[N,g.value]))})}catch{A.error(l("messages.configLoadError"))}finally{o(!1)}})()},[s]);const _=async S=>{c(!0);try{await Ps.updatePluginConfig(s,S.config),A.success(l("messages.configSaveSuccess")),t()}catch{A.error(l("messages.configSaveError"))}finally{c(!1)}},T=(S,C)=>{switch(C.type){case"string":return e.jsx(v,{control:h.control,name:`config.${S}`,render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{children:C.label||C.description}),e.jsx(b,{children:e.jsx(D,{placeholder:C.placeholder,...N})}),C.description&&C.label&&e.jsx("p",{className:"text-sm text-muted-foreground",children:C.description}),e.jsx(P,{})]})},S);case"number":case"percentage":return e.jsx(v,{control:h.control,name:`config.${S}`,render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{children:C.label||C.description}),e.jsx(b,{children:e.jsxs("div",{className:"relative",children:[e.jsx(D,{type:"number",placeholder:C.placeholder,...N,onChange:g=>{const k=Number(g.target.value);C.type==="percentage"?N.onChange(Math.min(100,Math.max(0,k))):N.onChange(k)},className:C.type==="percentage"?"pr-8":"",min:C.type==="percentage"?0:void 0,max:C.type==="percentage"?100:void 0,step:C.type==="percentage"?1:void 0}),C.type==="percentage"&&e.jsx("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3",children:e.jsx(xc,{className:"h-4 w-4 text-muted-foreground"})})]})}),C.description&&C.label&&e.jsx("p",{className:"text-sm text-muted-foreground",children:C.description}),e.jsx(P,{})]})},S);case"select":return e.jsx(v,{control:h.control,name:`config.${S}`,render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{children:C.label||C.description}),e.jsxs(X,{onValueChange:N.onChange,defaultValue:N.value,children:[e.jsx(b,{children:e.jsx(Y,{children:e.jsx(Z,{placeholder:C.placeholder})})}),e.jsx(J,{children:C.options?.map(g=>e.jsx($,{value:g.value,children:g.label},g.value))})]}),C.description&&C.label&&e.jsx("p",{className:"text-sm text-muted-foreground",children:C.description}),e.jsx(P,{})]})},S);case"boolean":return e.jsx(v,{control:h.control,name:`config.${S}`,render:({field:N})=>e.jsxs(f,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(j,{className:"text-base",children:C.label||C.description}),C.description&&C.label&&e.jsx("p",{className:"text-sm text-muted-foreground",children:C.description})]}),e.jsx(b,{children:e.jsx(ee,{checked:N.value,onCheckedChange:N.onChange})})]})},S);case"text":return e.jsx(v,{control:h.control,name:`config.${S}`,render:({field:N})=>e.jsxs(f,{children:[e.jsx(j,{children:C.label||C.description}),e.jsx(b,{children:e.jsx(Ts,{placeholder:C.placeholder,...N})}),C.description&&C.label&&e.jsx("p",{className:"text-sm text-muted-foreground",children:C.description}),e.jsx(P,{})]})},S);default:return null}};return n?e.jsxs("div",{className:"space-y-4",children:[e.jsx(ce,{className:"h-4 w-[200px]"}),e.jsx(ce,{className:"h-10 w-full"}),e.jsx(ce,{className:"h-4 w-[200px]"}),e.jsx(ce,{className:"h-10 w-full"})]}):e.jsx(we,{...h,children:e.jsxs("form",{onSubmit:h.handleSubmit(_),className:"space-y-4",children:[u&&Object.entries(u).map(([S,C])=>T(S,C)),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(E,{type:"button",variant:"outline",onClick:a,disabled:r,children:l("config.cancel")}),e.jsx(E,{type:"submit",loading:r,disabled:r,children:l("config.save")})]})]})})}function tu(){const{t:s}=V("plugin"),[a,t]=m.useState(null),[l,n]=m.useState(!1),[o,r]=m.useState(null),[c,u]=m.useState(""),[i,d]=m.useState("all"),[h,_]=m.useState(!1),[T,S]=m.useState(!1),[C,N]=m.useState(!1),g=m.useRef(null),{data:k,isLoading:R,refetch:p}=ne({queryKey:["pluginList"],queryFn:async()=>{const{data:L}=await Ps.getPluginList();return L}});k&&[...new Set(k.map(L=>L.category||"other"))];const w=k?.filter(L=>{const U=L.name.toLowerCase().includes(c.toLowerCase())||L.description.toLowerCase().includes(c.toLowerCase())||L.code.toLowerCase().includes(c.toLowerCase()),ms=i==="all"||L.category===i;return U&&ms}),I=async L=>{t(L),Ps.installPlugin(L).then(()=>{A.success(s("messages.installSuccess")),p()}).catch(U=>{A.error(U.message||s("messages.installError"))}).finally(()=>{t(null)})},H=async L=>{t(L),Ps.uninstallPlugin(L).then(()=>{A.success(s("messages.uninstallSuccess")),p()}).catch(U=>{A.error(U.message||s("messages.uninstallError"))}).finally(()=>{t(null)})},O=async(L,U)=>{t(L),(U?Ps.disablePlugin:Ps.enablePlugin)(L).then(()=>{A.success(s(U?"messages.disableSuccess":"messages.enableSuccess")),p()}).catch(De=>{A.error(De.message||s(U?"messages.disableError":"messages.enableError"))}).finally(()=>{t(null)})},K=L=>{k?.find(U=>U.code===L),r(L),n(!0)},oe=async L=>{if(!L.name.endsWith(".zip")){A.error(s("upload.error.format"));return}_(!0),Ps.uploadPlugin(L).then(()=>{A.success(s("messages.uploadSuccess")),S(!1),p()}).catch(U=>{A.error(U.message||s("messages.uploadError"))}).finally(()=>{_(!1),g.current&&(g.current.value="")})},W=L=>{L.preventDefault(),L.stopPropagation(),L.type==="dragenter"||L.type==="dragover"?N(!0):L.type==="dragleave"&&N(!1)},te=L=>{L.preventDefault(),L.stopPropagation(),N(!1),L.dataTransfer.files&&L.dataTransfer.files[0]&&oe(L.dataTransfer.files[0])},q=async L=>{t(L),Ps.deletePlugin(L).then(()=>{A.success(s("messages.deleteSuccess")),p()}).catch(U=>{A.error(U.message||s("messages.deleteError"))}).finally(()=>{t(null)})};return e.jsxs(Ve,{children:[e.jsxs(Fe,{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(xn,{className:"h-6 w-6"}),e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:s("title")})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{children:[e.jsxs("div",{className:"mb-8 space-y-4",children:[e.jsxs("div",{className:"flex flex-col gap-4 md:flex-row md:items-center md:justify-between",children:[e.jsxs("div",{className:"relative max-w-sm flex-1",children:[e.jsx(hn,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),e.jsx(D,{placeholder:s("search.placeholder"),value:c,onChange:L=>u(L.target.value),className:"pl-9"})]}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs(E,{onClick:()=>S(!0),variant:"outline",className:"shrink-0",size:"sm",children:[e.jsx(At,{className:"mr-2 h-4 w-4"}),s("upload.button")]})})]}),e.jsxs(Bt,{defaultValue:"all",className:"w-full",children:[e.jsxs(Ct,{children:[e.jsx(Ee,{value:"all",children:s("tabs.all")}),e.jsx(Ee,{value:"installed",children:s("tabs.installed")}),e.jsx(Ee,{value:"available",children:s("tabs.available")})]}),e.jsx(We,{value:"all",className:"mt-6",children:e.jsx("div",{className:"space-y-4",children:R?e.jsxs(e.Fragment,{children:[e.jsx(Ka,{}),e.jsx(Ka,{}),e.jsx(Ka,{})]}):w?.map(L=>e.jsx(Ua,{plugin:L,onInstall:I,onUninstall:H,onToggleEnable:O,onOpenConfig:K,onDelete:q,isLoading:a===L.name},L.name))})}),e.jsx(We,{value:"installed",className:"mt-6",children:e.jsx("div",{className:"space-y-4",children:w?.filter(L=>L.is_installed).map(L=>e.jsx(Ua,{plugin:L,onInstall:I,onUninstall:H,onToggleEnable:O,onOpenConfig:K,onDelete:q,isLoading:a===L.name},L.name))})}),e.jsx(We,{value:"available",className:"mt-6",children:e.jsx("div",{className:"space-y-4",children:w?.filter(L=>!L.is_installed).map(L=>e.jsx(Ua,{plugin:L,onInstall:I,onUninstall:H,onToggleEnable:O,onOpenConfig:K,onDelete:q,isLoading:a===L.name},L.code))})})]})]}),e.jsx(pe,{open:l,onOpenChange:n,children:e.jsxs(ue,{className:"sm:max-w-lg",children:[e.jsxs(ve,{children:[e.jsxs(ge,{children:[k?.find(L=>L.code===o)?.name," ",s("config.title")]}),e.jsx(Le,{children:s("config.description")})]}),o&&e.jsx(su,{pluginName:o,onClose:()=>n(!1),onSuccess:()=>{n(!1),p()}})]})}),e.jsx(pe,{open:T,onOpenChange:S,children:e.jsxs(ue,{className:"sm:max-w-md",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:s("upload.title")}),e.jsx(Le,{children:s("upload.description")})]}),e.jsxs("div",{className:y("relative mt-4 flex h-64 flex-col items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/25 px-5 py-10 text-center transition-colors",C&&"border-primary/50 bg-muted/50"),onDragEnter:W,onDragLeave:W,onDragOver:W,onDrop:te,children:[e.jsx("input",{type:"file",ref:g,className:"hidden",accept:".zip",onChange:L=>{const U=L.target.files?.[0];U&&oe(U)}}),h?e.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[e.jsx("div",{className:"h-10 w-10 animate-spin rounded-full border-b-2 border-primary"}),e.jsx("div",{className:"text-sm text-muted-foreground",children:s("upload.uploading")})]}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsx("div",{className:"rounded-full border-2 border-muted-foreground/25 p-3",children:e.jsx(At,{className:"h-6 w-6 text-muted-foreground/50"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-sm font-medium",children:[s("upload.dragText")," ",e.jsx("button",{type:"button",onClick:()=>g.current?.click(),className:"mx-1 text-primary hover:underline",children:s("upload.clickText")})]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s("upload.supportText")})]})]})})]})]})})]})]})}function Ua({plugin:s,onInstall:a,onUninstall:t,onToggleEnable:l,onOpenConfig:n,onDelete:o,isLoading:r}){const{t:c}=V("plugin");return e.jsxs(Ye,{className:"group relative overflow-hidden transition-all hover:shadow-md",children:[e.jsxs(ss,{children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(_s,{children:s.name}),s.is_installed&&e.jsx(B,{variant:s.is_enabled?"success":"secondary",children:s.is_enabled?c("status.enabled"):c("status.disabled")})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(xn,{className:"h-4 w-4"}),e.jsx("code",{className:"rounded bg-muted px-1 py-0.5",children:s.code})]}),e.jsxs("div",{children:["v",s.version]})]})]})}),e.jsx(Xs,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"mt-2",children:s.description}),e.jsx("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:e.jsxs("div",{className:"flex items-center gap-1",children:[c("author"),": ",s.author]})})]})})]}),e.jsx(ts,{children:e.jsx("div",{className:"flex items-center justify-end space-x-2",children:s.is_installed?e.jsxs(e.Fragment,{children:[e.jsxs(E,{variant:"outline",size:"sm",onClick:()=>n(s.code),disabled:!s.is_enabled||r,children:[e.jsx(hc,{className:"mr-2 h-4 w-4"}),c("button.config")]}),e.jsxs(E,{variant:s.is_enabled?"destructive":"default",size:"sm",onClick:()=>l(s.code,s.is_enabled),disabled:r,children:[e.jsx(pc,{className:"mr-2 h-4 w-4"}),s.is_enabled?c("button.disable"):c("button.enable")]}),e.jsx(ns,{title:c("uninstall.title"),description:c("uninstall.description"),cancelText:c("common:cancel"),confirmText:c("uninstall.button"),variant:"destructive",onConfirm:()=>t(s.code),children:e.jsxs(E,{variant:"outline",size:"sm",className:"text-muted-foreground hover:text-destructive",disabled:r,children:[e.jsx(fs,{className:"mr-2 h-4 w-4"}),c("button.uninstall")]})})]}):e.jsxs(e.Fragment,{children:[e.jsx(E,{onClick:()=>a(s.code),disabled:r,loading:r,children:c("button.install")}),e.jsx(ns,{title:c("delete.title"),description:c("delete.description"),cancelText:c("common:cancel"),confirmText:c("delete.button"),variant:"destructive",onConfirm:()=>o(s.code),children:e.jsx(E,{variant:"ghost",size:"icon",className:"h-8 w-8 text-muted-foreground hover:text-destructive",disabled:r,children:e.jsx(fs,{className:"h-4 w-4"})})})]})})})]})}function Ka(){return e.jsxs(Ye,{children:[e.jsxs(ss,{children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ce,{className:"h-6 w-[200px]"}),e.jsx(ce,{className:"h-6 w-[80px]"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(ce,{className:"h-5 w-[120px]"}),e.jsx(ce,{className:"h-5 w-[60px]"})]})]})}),e.jsxs("div",{className:"space-y-2 pt-2",children:[e.jsx(ce,{className:"h-4 w-[300px]"}),e.jsx(ce,{className:"h-4 w-[150px]"})]})]}),e.jsx(ts,{children:e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(ce,{className:"h-9 w-[100px]"}),e.jsx(ce,{className:"h-9 w-[100px]"}),e.jsx(ce,{className:"h-8 w-8"})]})})]})}const au=Object.freeze(Object.defineProperty({__proto__:null,default:tu},Symbol.toStringTag,{value:"Module"})),nu=(s,a)=>{let t=null;switch(s.field_type){case"input":t=e.jsx(D,{placeholder:s.placeholder,...a});break;case"textarea":t=e.jsx(Ts,{placeholder:s.placeholder,...a});break;case"select":t=e.jsx("select",{className:y(bt({variant:"outline"}),"w-full appearance-none font-normal"),...a,children:s.select_options&&Object.keys(s.select_options).map(l=>e.jsx("option",{value:l,children:s.select_options?.[l]},l))});break;default:t=null;break}return t};function lu({themeKey:s,themeInfo:a}){const{t}=V("theme"),[l,n]=m.useState(!1),[o,r]=m.useState(!1),[c,u]=m.useState(!1),i=ye({defaultValues:a.configs.reduce((_,T)=>(_[T.field_name]="",_),{})}),d=async()=>{r(!0),Mt.getConfig(s).then(({data:_})=>{Object.entries(_).forEach(([T,S])=>{i.setValue(T,S)})}).finally(()=>{r(!1)})},h=async _=>{u(!0),Mt.updateConfig(s,_).then(()=>{A.success(t("config.success")),n(!1)}).finally(()=>{u(!1)})};return e.jsxs(pe,{open:l,onOpenChange:_=>{n(_),_?d():i.reset()},children:[e.jsx(rs,{asChild:!0,children:e.jsx(E,{variant:"outline",children:t("card.configureTheme")})}),e.jsxs(ue,{className:"max-h-[90vh] overflow-auto sm:max-w-[425px]",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:t("config.title",{name:a.name})}),e.jsx(Le,{children:t("config.description")})]}),o?e.jsx("div",{className:"flex h-40 items-center justify-center",children:e.jsx(gn,{className:"h-6 w-6 animate-spin"})}):e.jsx(we,{...i,children:e.jsxs("form",{onSubmit:i.handleSubmit(h),className:"space-y-4",children:[a.configs.map(_=>e.jsx(v,{control:i.control,name:_.field_name,render:({field:T})=>e.jsxs(f,{children:[e.jsx(j,{children:_.label}),e.jsx(b,{children:nu(_,T)}),e.jsx(P,{})]})},_.field_name)),e.jsxs(Re,{className:"mt-6 gap-2",children:[e.jsx(E,{type:"button",variant:"secondary",onClick:()=>n(!1),children:t("config.cancel")}),e.jsx(E,{type:"submit",loading:c,children:t("config.save")})]})]})})]})]})}function ru(){const{t:s}=V("theme"),[a,t]=m.useState(null),[l,n]=m.useState(!1),[o,r]=m.useState(!1),[c,u]=m.useState(!1),[i,d]=m.useState(null),h=m.useRef(null),[_,T]=m.useState(0),{data:S,isLoading:C,refetch:N}=ne({queryKey:["themeList"],queryFn:async()=>{const{data:O}=await Mt.getList();return O}}),g=async O=>{t(O),me.updateSystemConfig({frontend_theme:O}).then(()=>{A.success("主题切换成功"),N()}).finally(()=>{t(null)})},k=async O=>{if(!O.name.endsWith(".zip")){A.error(s("upload.error.format"));return}n(!0),Mt.upload(O).then(()=>{A.success("主题上传成功"),r(!1),N()}).finally(()=>{n(!1),h.current&&(h.current.value="")})},R=O=>{O.preventDefault(),O.stopPropagation(),O.type==="dragenter"||O.type==="dragover"?u(!0):O.type==="dragleave"&&u(!1)},p=O=>{O.preventDefault(),O.stopPropagation(),u(!1),O.dataTransfer.files&&O.dataTransfer.files[0]&&k(O.dataTransfer.files[0])},w=()=>{i&&T(O=>O===0?i.images.length-1:O-1)},I=()=>{i&&T(O=>O===i.images.length-1?0:O+1)},H=(O,K)=>{T(0),d({name:O,images:K})};return e.jsxs(Ve,{children:[e.jsxs(Fe,{className:"flex items-center justify-between",children:[e.jsx(Xe,{}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"",children:[e.jsxs("header",{className:"mb-8",children:[e.jsx("div",{className:"mb-2",children:e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:s("title")})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-muted-foreground",children:s("description")}),e.jsxs(E,{onClick:()=>r(!0),variant:"outline",className:"ml-4 shrink-0",size:"sm",children:[e.jsx(At,{className:"mr-2 h-4 w-4"}),s("upload.button")]})]})]}),e.jsx("section",{className:"grid gap-6 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3",children:C?e.jsxs(e.Fragment,{children:[e.jsx(Yn,{}),e.jsx(Yn,{})]}):S?.themes&&Object.entries(S.themes).map(([O,K])=>e.jsx(Ye,{className:"group relative overflow-hidden transition-all hover:shadow-md",style:{backgroundImage:K.background_url?`url(${K.background_url})`:"none",backgroundSize:"cover",backgroundPosition:"center"},children:e.jsxs("div",{className:y("relative z-10 h-full transition-colors",K.background_url?"group-hover:from-background/98 bg-gradient-to-t from-background/95 via-background/80 to-background/60 backdrop-blur-[1px] group-hover:via-background/90 group-hover:to-background/70":"bg-background"),children:[!!K.can_delete&&e.jsx("div",{className:"absolute right-2 top-2",children:e.jsx(ns,{title:s("card.delete.title"),description:s("card.delete.description"),confirmText:s("card.delete.button"),variant:"destructive",onConfirm:async()=>{if(O===S?.active){A.error(s("card.delete.error.active"));return}t(O),Mt.drop(O).then(()=>{A.success("主题删除成功"),N()}).finally(()=>{t(null)})},children:e.jsx(E,{disabled:a===O,loading:a===O,variant:"ghost",size:"icon",className:"h-8 w-8 text-muted-foreground hover:text-destructive",children:e.jsx(fs,{className:"h-4 w-4"})})})}),e.jsxs(ss,{children:[e.jsx(_s,{children:K.name}),e.jsx(Xs,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{children:K.description}),K.version&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("card.version",{version:K.version})})]})})]}),e.jsxs(ts,{className:"flex items-center justify-end space-x-3",children:[K.images&&Array.isArray(K.images)&&K.images.length>0&&e.jsx(E,{variant:"outline",size:"icon",className:"h-8 w-8",onClick:()=>H(K.name,K.images),children:e.jsx(gc,{className:"h-4 w-4"})}),e.jsx(lu,{themeKey:O,themeInfo:K}),e.jsx(E,{onClick:()=>g(O),disabled:a===O||O===S.active,loading:a===O,variant:O===S.active?"secondary":"default",children:O===S.active?s("card.currentTheme"):s("card.activateTheme")})]})]})},O))}),e.jsx(pe,{open:o,onOpenChange:r,children:e.jsxs(ue,{className:"sm:max-w-md",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:s("upload.title")}),e.jsx(Le,{children:s("upload.description")})]}),e.jsxs("div",{className:y("relative mt-4 flex h-64 flex-col items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/25 px-5 py-10 text-center transition-colors",c&&"border-primary/50 bg-muted/50"),onDragEnter:R,onDragLeave:R,onDragOver:R,onDrop:p,children:[e.jsx("input",{type:"file",ref:h,className:"hidden",accept:".zip",onChange:O=>{const K=O.target.files?.[0];K&&k(K)}}),l?e.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[e.jsx("div",{className:"h-10 w-10 animate-spin rounded-full border-b-2 border-primary"}),e.jsx("div",{className:"text-sm text-muted-foreground",children:s("upload.uploading")})]}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsx("div",{className:"rounded-full border-2 border-muted-foreground/25 p-3",children:e.jsx(At,{className:"h-6 w-6 text-muted-foreground/50"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-sm font-medium",children:[s("upload.dragText")," ",e.jsx("button",{type:"button",onClick:()=>h.current?.click(),className:"mx-1 text-primary hover:underline",children:s("upload.clickText")})]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s("upload.supportText")})]})]})})]})]})}),e.jsx(pe,{open:!!i,onOpenChange:O=>{O||(d(null),T(0))},children:e.jsxs(ue,{className:"max-w-4xl",children:[e.jsxs(ve,{children:[e.jsxs(ge,{children:[i?.name," ",s("preview.title")]}),e.jsx(Le,{className:"text-center",children:i&&s("preview.imageCount",{current:_+1,total:i.images.length})})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"aspect-[16/9] overflow-hidden rounded-lg border bg-muted",children:i?.images[_]&&e.jsx("img",{src:i.images[_],alt:`${i.name} 预览图 ${_+1}`,className:"h-full w-full object-contain"})}),i&&i.images.length>1&&e.jsxs(e.Fragment,{children:[e.jsx(E,{variant:"outline",size:"icon",className:"absolute left-4 top-1/2 h-8 w-8 -translate-y-1/2 rounded-full bg-background/80 hover:bg-background",onClick:w,children:e.jsx(fc,{className:"h-4 w-4"})}),e.jsx(E,{variant:"outline",size:"icon",className:"absolute right-4 top-1/2 h-8 w-8 -translate-y-1/2 rounded-full bg-background/80 hover:bg-background",onClick:I,children:e.jsx(jc,{className:"h-4 w-4"})})]})]}),i&&i.images.length>1&&e.jsx("div",{className:"mt-4 flex gap-2 overflow-x-auto pb-2",children:i.images.map((O,K)=>e.jsx("button",{onClick:()=>T(K),className:y("relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border-2",_===K?"border-primary":"border-transparent"),children:e.jsx("img",{src:O,alt:`缩略图 ${K+1}`,className:"h-full w-full object-cover"})},K))})]})})]})]})}function Yn(){return e.jsxs(Ye,{children:[e.jsxs(ss,{children:[e.jsx(ce,{className:"h-6 w-[200px]"}),e.jsx(ce,{className:"h-4 w-[300px]"})]}),e.jsxs(ts,{className:"flex items-center justify-end space-x-3",children:[e.jsx(ce,{className:"h-10 w-[100px]"}),e.jsx(ce,{className:"h-10 w-[100px]"})]})]})}const iu=Object.freeze(Object.defineProperty({__proto__:null,default:ru},Symbol.toStringTag,{value:"Module"})),Sn=m.forwardRef(({className:s,value:a,onChange:t,...l},n)=>{const[o,r]=m.useState("");m.useEffect(()=>{if(o.includes(",")){const u=new Set([...a,...o.split(",").map(i=>i.trim())]);t(Array.from(u)),r("")}},[o,t,a]);const c=()=>{if(o){const u=new Set([...a,o]);t(Array.from(u)),r("")}};return e.jsxs("div",{className:y(" has-[:focus-visible]:outline-none has-[:focus-visible]:ring-1 has-[:focus-visible]:ring-neutral-950  dark:has-[:focus-visible]:ring-neutral-300  flex w-full flex-wrap gap-2 rounded-md border border-input shadow-sm px-3 py-2 text-sm ring-offset-white  disabled:cursor-not-allowed disabled:opacity-50",s),children:[a.map(u=>e.jsxs(B,{variant:"secondary",children:[u,e.jsx(G,{variant:"ghost",size:"icon",className:"ml-2 h-3 w-3",onClick:()=>{t(a.filter(i=>i!==u))},children:e.jsx(an,{className:"w-3"})})]},u)),e.jsx("input",{className:"flex-1 outline-none placeholder:text-muted-foreground bg-transparent",value:o,onChange:u=>r(u.target.value),onKeyDown:u=>{u.key==="Enter"||u.key===","?(u.preventDefault(),c()):u.key==="Backspace"&&o.length===0&&a.length>0&&(u.preventDefault(),t(a.slice(0,-1)))},...l,ref:n})]})});Sn.displayName="InputTags";const ou=x.object({id:x.number().nullable(),title:x.string().min(1).max(250),content:x.string().min(1),show:x.boolean(),tags:x.array(x.string()),img_url:x.string().nullable()}),cu={id:null,show:!1,tags:[],img_url:"",title:"",content:""};function Er({refetch:s,dialogTrigger:a,type:t="add",defaultFormValues:l=cu}){const{t:n}=V("notice"),[o,r]=m.useState(!1),c=ye({resolver:_e(ou),defaultValues:l,mode:"onChange",shouldFocusError:!0}),u=new fn({html:!0});return e.jsx(we,{...c,children:e.jsxs(pe,{onOpenChange:r,open:o,children:[e.jsx(rs,{asChild:!0,children:a||e.jsxs(E,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(ze,{icon:"ion:add"})," ",e.jsx("div",{children:n("form.add.button")})]})}),e.jsxs(ue,{className:"sm:max-w-[1025px]",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:n(t==="add"?"form.add.title":"form.edit.title")}),e.jsx(Le,{})]}),e.jsx(v,{control:c.control,name:"title",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.title.label")}),e.jsx("div",{className:"relative ",children:e.jsx(b,{children:e.jsx(D,{placeholder:n("form.fields.title.placeholder"),...i})})}),e.jsx(P,{})]})}),e.jsx(v,{control:c.control,name:"content",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.content.label")}),e.jsx(b,{children:e.jsx(jn,{style:{height:"500px"},value:i.value,renderHTML:d=>u.render(d),onChange:({text:d})=>{i.onChange(d)}})}),e.jsx(P,{})]})}),e.jsx(v,{control:c.control,name:"img_url",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.img_url.label")}),e.jsx("div",{className:"relative",children:e.jsx(b,{children:e.jsx(D,{type:"text",placeholder:n("form.fields.img_url.placeholder"),...i,value:i.value||""})})}),e.jsx(P,{})]})}),e.jsx(v,{control:c.control,name:"show",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.show.label")}),e.jsx("div",{className:"relative py-2",children:e.jsx(b,{children:e.jsx(ee,{checked:i.value,onCheckedChange:i.onChange})})}),e.jsx(P,{})]})}),e.jsx(v,{control:c.control,name:"tags",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.fields.tags.label")}),e.jsx(b,{children:e.jsx(Sn,{value:i.value,onChange:i.onChange,placeholder:n("form.fields.tags.placeholder"),className:"w-full"})}),e.jsx(P,{})]})}),e.jsxs(Re,{children:[e.jsx(qs,{asChild:!0,children:e.jsx(E,{type:"button",variant:"outline",children:n("form.buttons.cancel")})}),e.jsx(E,{type:"submit",onClick:i=>{i.preventDefault(),c.handleSubmit(async d=>{Ht.save(d).then(({data:h})=>{h&&(A.success(n("form.buttons.success")),s(),r(!1))})})()},children:n("form.buttons.submit")})]})]})]})})}function du({table:s,refetch:a,saveOrder:t,isSortMode:l}){const{t:n}=V("notice"),o=s.getState().columnFilters.length>0;return e.jsxs("div",{className:"flex items-center justify-between space-x-2 ",children:[e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[!l&&e.jsx(Er,{refetch:a}),!l&&e.jsx(D,{placeholder:n("table.toolbar.search"),value:s.getColumn("title")?.getFilterValue()??"",onChange:r=>s.getColumn("title")?.setFilterValue(r.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),o&&!l&&e.jsxs(E,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-9 px-2 lg:px-3",children:[n("table.toolbar.reset"),e.jsx(ds,{className:"ml-2 h-4 w-4"})]})]}),s.getRowCount()>0&&e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(E,{variant:l?"default":"outline",onClick:t,className:"h-8",size:"sm",children:n(l?"table.toolbar.sort.save":"table.toolbar.sort.edit")})})]})}const mu=s=>{const{t:a}=V("notice");return[{id:"drag-handle",header:"",cell:()=>e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(vc,{className:"h-4 w-4 cursor-move text-muted-foreground"})}),size:40,enableSorting:!1},{accessorKey:"id",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.id")}),cell:({row:t})=>e.jsx(B,{variant:"outline",className:"font-mono",children:t.getValue("id")}),enableSorting:!0,size:60},{accessorKey:"show",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.show")}),cell:({row:t})=>e.jsx("div",{className:"flex items-center",children:e.jsx(ee,{defaultChecked:t.getValue("show"),onCheckedChange:async()=>{const{data:l}=await Ht.updateStatus(t.original.id);l||s()}})}),enableSorting:!1,size:100},{accessorKey:"title",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.title")}),cell:({row:t})=>e.jsx("div",{className:"flex max-w-[500px] items-center",children:e.jsx("span",{className:"truncate font-medium",children:t.getValue("title")})}),enableSorting:!1,size:6e3},{id:"actions",header:({column:t})=>e.jsx(z,{className:"justify-end",column:t,title:a("table.columns.actions")}),cell:({row:t})=>e.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[e.jsx(Er,{refetch:s,dialogTrigger:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:[e.jsx(tt,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:a("table.actions.edit")})]}),type:"edit",defaultFormValues:t.original}),e.jsx(ns,{title:a("table.actions.delete.title"),description:a("table.actions.delete.description"),onConfirm:async()=>{Ht.drop(t.original.id).then(()=>{A.success(a("table.actions.delete.success")),s()})},children:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(fs,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:a("table.actions.delete.title")})]})})]}),size:100}]};function uu(){const[s,a]=m.useState({}),[t,l]=m.useState({}),[n,o]=m.useState([]),[r,c]=m.useState([]),[u,i]=m.useState(!1),[d,h]=m.useState({}),[_,T]=m.useState({pageSize:50,pageIndex:0}),[S,C]=m.useState([]),{refetch:N}=ne({queryKey:["notices"],queryFn:async()=>{const{data:w}=await Ht.getList();return C(w),w}});m.useEffect(()=>{l({"drag-handle":u,content:!u,created_at:!u,actions:!u}),T({pageSize:u?99999:50,pageIndex:0})},[u]);const g=(w,I)=>{u&&(w.dataTransfer.setData("text/plain",I.toString()),w.currentTarget.classList.add("opacity-50"))},k=(w,I)=>{if(!u)return;w.preventDefault(),w.currentTarget.classList.remove("bg-muted");const H=parseInt(w.dataTransfer.getData("text/plain"));if(H===I)return;const O=[...S],[K]=O.splice(H,1);O.splice(I,0,K),C(O)},R=async()=>{if(!u){i(!0);return}Ht.sort(S.map(w=>w.id)).then(()=>{A.success("排序保存成功"),i(!1),N()}).finally(()=>{i(!1)})},p=Je({data:S??[],columns:mu(N),state:{sorting:r,columnVisibility:t,rowSelection:s,columnFilters:n,columnSizing:d,pagination:_},enableRowSelection:!0,onRowSelectionChange:a,onSortingChange:c,onColumnFiltersChange:o,onColumnVisibilityChange:l,onColumnSizingChange:h,onPaginationChange:T,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),getSortedRowModel:vs(),getFacetedRowModel:Ls(),getFacetedUniqueValues:Vs(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx("div",{className:"space-y-4",children:e.jsx(is,{table:p,toolbar:w=>e.jsx(du,{table:w,refetch:N,saveOrder:R,isSortMode:u}),draggable:u,onDragStart:g,onDragEnd:w=>w.currentTarget.classList.remove("opacity-50"),onDragOver:w=>{w.preventDefault(),w.currentTarget.classList.add("bg-muted")},onDragLeave:w=>w.currentTarget.classList.remove("bg-muted"),onDrop:k,showPagination:!u})})}function xu(){const{t:s}=V("notice");return e.jsxs(Ve,{children:[e.jsxs(Fe,{className:"flex items-center justify-between",children:[e.jsx(Xe,{}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("div",{className:"mb-2",children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")})}),e.jsx("p",{className:"text-muted-foreground",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(uu,{})})]})]})}const hu=Object.freeze(Object.defineProperty({__proto__:null,default:xu},Symbol.toStringTag,{value:"Module"})),pu=x.object({id:x.number().nullable(),language:x.string().max(250),category:x.string().max(250),title:x.string().min(1).max(250),body:x.string().min(1),show:x.boolean()}),gu={id:null,language:"zh-CN",category:"",title:"",body:"",show:!1};function Rr({refreshData:s,dialogTrigger:a,type:t="add",defaultFormValues:l=gu}){const{t:n}=V("knowledge"),[o,r]=m.useState(!1),c=ye({resolver:_e(pu),defaultValues:l,mode:"onChange",shouldFocusError:!0}),u=new fn({html:!0});return m.useEffect(()=>{o&&l.id&&vt.getInfo(l.id).then(({data:i})=>{c.reset(i)})},[l.id,c,o]),e.jsxs(pe,{onOpenChange:r,open:o,children:[e.jsx(rs,{asChild:!0,children:a||e.jsxs(E,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(ze,{icon:"ion:add"})," ",e.jsx("div",{children:n("form.add")})]})}),e.jsxs(ue,{className:"sm:max-w-[1025px]",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:n(t==="add"?"form.add":"form.edit")}),e.jsx(Le,{})]}),e.jsxs(we,{...c,children:[e.jsx(v,{control:c.control,name:"title",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.title")}),e.jsx("div",{className:"relative ",children:e.jsx(b,{children:e.jsx(D,{placeholder:n("form.titlePlaceholder"),...i})})}),e.jsx(P,{})]})}),e.jsx(v,{control:c.control,name:"category",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.category")}),e.jsx("div",{className:"relative ",children:e.jsx(b,{children:e.jsx(D,{placeholder:n("form.categoryPlaceholder"),...i})})}),e.jsx(P,{})]})}),e.jsx(v,{control:c.control,name:"language",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.language")}),e.jsx(b,{children:e.jsxs(X,{value:i.value,onValueChange:i.onChange,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:n("form.languagePlaceholder")})}),e.jsx(J,{children:[{value:"en-US"},{value:"ja-JP"},{value:"ko-KR"},{value:"vi-VN"},{value:"zh-CN"},{value:"zh-TW"}].map(d=>e.jsx($,{value:d.value,className:"cursor-pointer",children:n(`languages.${d.value}`)},d.value))})]})})]})}),e.jsx(v,{control:c.control,name:"body",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.content")}),e.jsx(b,{children:e.jsx(jn,{style:{height:"500px"},value:i.value,renderHTML:d=>u.render(d),onChange:({text:d})=>{i.onChange(d)}})}),e.jsx(P,{})]})}),e.jsx(v,{control:c.control,name:"show",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.show")}),e.jsx("div",{className:"relative py-2",children:e.jsx(b,{children:e.jsx(ee,{checked:i.value,onCheckedChange:i.onChange})})}),e.jsx(P,{})]})}),e.jsxs(Re,{children:[e.jsx(qs,{asChild:!0,children:e.jsx(E,{type:"button",variant:"outline",children:n("form.cancel")})}),e.jsx(E,{type:"submit",onClick:()=>{c.handleSubmit(i=>{vt.save(i).then(({data:d})=>{d&&(c.reset(),A.success(n("messages.operationSuccess")),r(!1),s())})})()},children:n("form.submit")})]})]})]})]})}function fu({column:s,title:a,options:t}){const l=s?.getFacetedUniqueValues(),n=new Set(s?.getFilterValue());return e.jsxs(Ss,{children:[e.jsx(ks,{asChild:!0,children:e.jsxs(E,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(_a,{className:"mr-2 h-4 w-4"}),a,n?.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(ke,{orientation:"vertical",className:"mx-2 h-4"}),e.jsx(B,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:n.size}),e.jsx("div",{className:"hidden space-x-1 lg:flex",children:n.size>2?e.jsxs(B,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[n.size," selected"]}):t.filter(o=>n.has(o.value)).map(o=>e.jsx(B,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:o.label},o.value))})]})]})}),e.jsx(bs,{className:"w-[200px] p-0",align:"start",children:e.jsxs(Us,{children:[e.jsx(nt,{placeholder:a}),e.jsxs(Ks,{children:[e.jsx(lt,{children:"No results found."}),e.jsx(as,{children:t.map(o=>{const r=n.has(o.value);return e.jsxs($e,{onSelect:()=>{r?n.delete(o.value):n.add(o.value);const c=Array.from(n);s?.setFilterValue(c.length?c:void 0)},children:[e.jsx("div",{className:y("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",r?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(et,{className:y("h-4 w-4")})}),o.icon&&e.jsx(o.icon,{className:"mr-2 h-4 w-4 text-muted-foreground"}),e.jsx("span",{children:o.label}),l?.get(o.value)&&e.jsx("span",{className:"ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs",children:l.get(o.value)})]},o.value)})}),n.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(St,{}),e.jsx(as,{children:e.jsx($e,{onSelect:()=>s?.setFilterValue(void 0),className:"justify-center text-center",children:"Clear filters"})})]})]})]})})]})}function ju({table:s,refetch:a,saveOrder:t,isSortMode:l}){const n=s.getState().columnFilters.length>0,{t:o}=V("knowledge");return e.jsxs("div",{className:"flex items-center justify-between",children:[l?e.jsx("p",{className:"text-sm text-muted-foreground",children:o("toolbar.sortModeHint")}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Rr,{refreshData:a}),e.jsx(D,{placeholder:o("toolbar.searchPlaceholder"),value:s.getColumn("title")?.getFilterValue()??"",onChange:r=>s.getColumn("title")?.setFilterValue(r.target.value),className:"h-8 w-[250px]"}),s.getColumn("category")&&e.jsx(fu,{column:s.getColumn("category"),title:o("columns.category"),options:Array.from(new Set(s.getCoreRowModel().rows.map(r=>r.getValue("category")))).map(r=>({label:r,value:r}))}),n&&e.jsxs(E,{variant:"ghost",onClick:()=>s.resetColumnFilters(),children:[o("toolbar.reset"),e.jsx(ds,{className:"ml-2 h-4 w-4"})]})]}),s.getRowCount()>0&&e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(E,{variant:l?"default":"outline",onClick:t,size:"sm",children:o(l?"toolbar.saveSort":"toolbar.editSort")})})]})}const vu=({refetch:s,isSortMode:a=!1})=>{const{t}=V("knowledge");return[{id:"drag-handle",header:()=>null,cell:()=>e.jsx("div",{className:a?"cursor-move":"opacity-0",children:e.jsx(Na,{className:"size-4"})}),size:40,enableSorting:!1},{accessorKey:"id",header:({column:l})=>e.jsx(z,{column:l,title:t("columns.id")}),cell:({row:l})=>e.jsx(B,{variant:"outline",className:"justify-center",children:l.getValue("id")}),enableSorting:!0,size:70},{accessorKey:"show",header:({column:l})=>e.jsx(z,{column:l,title:t("columns.status")}),cell:({row:l})=>e.jsx("div",{className:"flex items-center",children:e.jsx(ee,{defaultChecked:l.getValue("show"),onCheckedChange:async()=>{vt.updateStatus({id:l.original.id}).then(({data:n})=>{n||s()})}})}),enableSorting:!1,size:100},{accessorKey:"title",header:({column:l})=>e.jsx(z,{column:l,title:t("columns.title")}),cell:({row:l})=>e.jsx("div",{className:"flex space-x-2",children:e.jsx("span",{className:"line-clamp-2 font-medium",children:l.getValue("title")})}),enableSorting:!0,size:600},{accessorKey:"category",header:({column:l})=>e.jsx(z,{column:l,title:t("columns.category")}),cell:({row:l})=>e.jsx(B,{variant:"secondary",className:"max-w-[180px] truncate",children:l.getValue("category")}),enableSorting:!0,size:1800},{id:"actions",header:({column:l})=>e.jsx(z,{className:"justify-end",column:l,title:t("columns.actions")}),cell:({row:l})=>e.jsxs("div",{className:"flex items-center justify-end space-x-1",children:[e.jsx(Rr,{refreshData:s,dialogTrigger:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:[e.jsx(tt,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:t("form.edit")})]}),type:"edit",defaultFormValues:l.original}),e.jsx(ns,{title:t("messages.deleteConfirm"),description:t("messages.deleteDescription"),confirmText:t("messages.deleteButton"),variant:"destructive",onConfirm:async()=>{vt.drop({id:l.original.id}).then(({data:n})=>{n&&(A.success(t("messages.operationSuccess")),s())})},children:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(fs,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:t("messages.deleteButton")})]})})]}),size:100}]};function bu(){const[s,a]=m.useState([]),[t,l]=m.useState([]),[n,o]=m.useState(!1),[r,c]=m.useState([]),[u,i]=m.useState({"drag-handle":!1}),[d,h]=m.useState({pageSize:20,pageIndex:0}),{refetch:_,isLoading:T,data:S}=ne({queryKey:["knowledge"],queryFn:async()=>{const{data:R}=await vt.getList();return c(R||[]),R}});m.useEffect(()=>{i({"drag-handle":n,actions:!n}),h({pageSize:n?99999:10,pageIndex:0})},[n]);const C=(R,p)=>{n&&(R.dataTransfer.setData("text/plain",p.toString()),R.currentTarget.classList.add("opacity-50"))},N=(R,p)=>{if(!n)return;R.preventDefault(),R.currentTarget.classList.remove("bg-muted");const w=parseInt(R.dataTransfer.getData("text/plain"));if(w===p)return;const I=[...r],[H]=I.splice(w,1);I.splice(p,0,H),c(I)},g=async()=>{n?vt.sort({ids:r.map(R=>R.id)}).then(()=>{_(),o(!1),A.success("排序保存成功")}):o(!0)},k=Je({data:r,columns:vu({refetch:_,isSortMode:n}),state:{sorting:t,columnFilters:s,columnVisibility:u,pagination:d},onSortingChange:l,onColumnFiltersChange:a,onColumnVisibilityChange:i,onPaginationChange:h,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),getSortedRowModel:vs(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(is,{table:k,toolbar:R=>e.jsx(ju,{table:R,refetch:_,saveOrder:g,isSortMode:n}),draggable:n,onDragStart:C,onDragEnd:R=>R.currentTarget.classList.remove("opacity-50"),onDragOver:R=>{R.preventDefault(),R.currentTarget.classList.add("bg-muted")},onDragLeave:R=>R.currentTarget.classList.remove("bg-muted"),onDrop:N,showPagination:!n})}function yu(){const{t:s}=V("knowledge");return e.jsxs(Ve,{children:[e.jsxs(Fe,{children:[e.jsx(Xe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight mb-2",children:s("title")}),e.jsx("p",{className:"text-muted-foreground",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(bu,{})})]})]})}const Nu=Object.freeze(Object.defineProperty({__proto__:null,default:yu},Symbol.toStringTag,{value:"Module"}));function _u(s,a){const[t,l]=m.useState(s);return m.useEffect(()=>{const n=setTimeout(()=>l(s),a);return()=>{clearTimeout(n)}},[s,a]),t}function Ba(s,a){if(s.length===0)return{};if(!a)return{"":s};const t={};return s.forEach(l=>{const n=l[a]||"";t[n]||(t[n]=[]),t[n].push(l)}),t}function wu(s,a){const t=JSON.parse(JSON.stringify(s));for(const[l,n]of Object.entries(t))t[l]=n.filter(o=>!a.find(r=>r.value===o.value));return t}function Cu(s,a){for(const[,t]of Object.entries(s))if(t.some(l=>a.find(n=>n.value===l.value)))return!0;return!1}const Ir=m.forwardRef(({className:s,...a},t)=>bc(n=>n.filtered.count===0)?e.jsx("div",{ref:t,className:y("py-6 text-center text-sm",s),"cmdk-empty":"",role:"presentation",...a}):null);Ir.displayName="CommandEmpty";const _t=m.forwardRef(({value:s,onChange:a,placeholder:t,defaultOptions:l=[],options:n,delay:o,onSearch:r,loadingIndicator:c,emptyIndicator:u,maxSelected:i=Number.MAX_SAFE_INTEGER,onMaxSelected:d,hidePlaceholderWhenSelected:h,disabled:_,groupBy:T,className:S,badgeClassName:C,selectFirstItem:N=!0,creatable:g=!1,triggerSearchOnFocus:k=!1,commandProps:R,inputProps:p,hideClearAllButton:w=!1},I)=>{const H=m.useRef(null),[O,K]=m.useState(!1),oe=m.useRef(!1),[W,te]=m.useState(!1),[q,L]=m.useState(s||[]),[U,ms]=m.useState(Ba(l,T)),[De,le]=m.useState(""),ys=_u(De,o||500);m.useImperativeHandle(I,()=>({selectedValue:[...q],input:H.current,focus:()=>H.current?.focus()}),[q]);const Fs=m.useCallback(se=>{const ie=q.filter(Me=>Me.value!==se.value);L(ie),a?.(ie)},[a,q]),Fa=m.useCallback(se=>{const ie=H.current;ie&&((se.key==="Delete"||se.key==="Backspace")&&ie.value===""&&q.length>0&&(q[q.length-1].fixed||Fs(q[q.length-1])),se.key==="Escape"&&ie.blur())},[Fs,q]);m.useEffect(()=>{s&&L(s)},[s]),m.useEffect(()=>{if(!n||r)return;const se=Ba(n||[],T);JSON.stringify(se)!==JSON.stringify(U)&&ms(se)},[l,n,T,r,U]),m.useEffect(()=>{const se=async()=>{te(!0);const Me=await r?.(ys);ms(Ba(Me||[],T)),te(!1)};(async()=>{!r||!O||(k&&await se(),ys&&await se())})()},[ys,T,O,k]);const Gt=()=>{if(!g||Cu(U,[{value:De,label:De}])||q.find(ie=>ie.value===De))return;const se=e.jsx($e,{value:De,className:"cursor-pointer",onMouseDown:ie=>{ie.preventDefault(),ie.stopPropagation()},onSelect:ie=>{if(q.length>=i){d?.(q.length);return}le("");const Me=[...q,{value:ie,label:ie}];L(Me),a?.(Me)},children:`Create "${De}"`});if(!r&&De.length>0||r&&ys.length>0&&!W)return se},it=m.useCallback(()=>{if(u)return r&&!g&&Object.keys(U).length===0?e.jsx($e,{value:"-",disabled:!0,children:u}):e.jsx(Ir,{children:u})},[g,u,r,U]),Ma=m.useMemo(()=>wu(U,q),[U,q]),Wt=m.useCallback(()=>{if(R?.filter)return R.filter;if(g)return(se,ie)=>se.toLowerCase().includes(ie.toLowerCase())?1:-1},[g,R?.filter]),Oa=m.useCallback(()=>{const se=q.filter(ie=>ie.fixed);L(se),a?.(se)},[a,q]);return e.jsxs(Us,{...R,onKeyDown:se=>{Fa(se),R?.onKeyDown?.(se)},className:y("h-auto overflow-visible bg-transparent",R?.className),shouldFilter:R?.shouldFilter!==void 0?R.shouldFilter:!r,filter:Wt(),children:[e.jsx("div",{className:y("rounded-md border border-input text-sm ring-offset-background focus-within:ring-1 focus-within:ring-ring ",{"px-3 py-2":q.length!==0,"cursor-text":!_&&q.length!==0},S),onClick:()=>{_||H.current?.focus()},children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[q.map(se=>e.jsxs(B,{className:y("data-[disabled]:bg-muted-foreground data-[disabled]:text-muted data-[disabled]:hover:bg-muted-foreground","data-[fixed]:bg-muted-foreground data-[fixed]:text-muted data-[fixed]:hover:bg-muted-foreground",C),"data-fixed":se.fixed,"data-disabled":_||void 0,children:[se.label,e.jsx("button",{className:y("ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2",(_||se.fixed)&&"hidden"),onKeyDown:ie=>{ie.key==="Enter"&&Fs(se)},onMouseDown:ie=>{ie.preventDefault(),ie.stopPropagation()},onClick:()=>Fs(se),children:e.jsx(an,{className:"h-3 w-3 text-muted-foreground hover:text-foreground"})})]},se.value)),e.jsx(He.Input,{...p,ref:H,value:De,disabled:_,onValueChange:se=>{le(se),p?.onValueChange?.(se)},onBlur:se=>{oe.current===!1&&K(!1),p?.onBlur?.(se)},onFocus:se=>{K(!0),k&&r?.(ys),p?.onFocus?.(se)},placeholder:h&&q.length!==0?"":t,className:y("flex-1 bg-transparent outline-none placeholder:text-muted-foreground",{"w-full":h,"px-3 py-2":q.length===0,"ml-1":q.length!==0},p?.className)}),e.jsx("button",{type:"button",onClick:Oa,className:y((w||_||q.length<1||q.filter(se=>se.fixed).length===q.length)&&"hidden"),children:e.jsx(an,{})})]})}),e.jsx("div",{className:"relative",children:O&&e.jsx(Ks,{className:"absolute top-1 z-10 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in",onMouseLeave:()=>{oe.current=!1},onMouseEnter:()=>{oe.current=!0},onMouseUp:()=>{H.current?.focus()},children:W?e.jsx(e.Fragment,{children:c}):e.jsxs(e.Fragment,{children:[it(),Gt(),!N&&e.jsx($e,{value:"-",className:"hidden"}),Object.entries(Ma).map(([se,ie])=>e.jsx(as,{heading:se,className:"h-full overflow-auto",children:e.jsx(e.Fragment,{children:ie.map(Me=>e.jsx($e,{value:Me.value,disabled:Me.disable,onMouseDown:Ms=>{Ms.preventDefault(),Ms.stopPropagation()},onSelect:()=>{if(q.length>=i){d?.(q.length);return}le("");const Ms=[...q,Me];L(Ms),a?.(Ms)},className:y("cursor-pointer",Me.disable&&"cursor-default text-muted-foreground"),children:Me.label},Me.value))})},se))]})})})]})});_t.displayName="MultipleSelector";const Su=s=>x.object({id:x.number().optional(),name:x.string().min(2,s("messages.nameValidation.min")).max(50,s("messages.nameValidation.max")).regex(/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/,s("messages.nameValidation.pattern"))});function Va({refetch:s,dialogTrigger:a,defaultValues:t={name:""},type:l="add"}){const{t:n}=V("group"),o=ye({resolver:_e(Su(n)),defaultValues:t,mode:"onChange"}),[r,c]=m.useState(!1),[u,i]=m.useState(!1),d=async h=>{i(!0),at.save(h).then(()=>{A.success(n(l==="edit"?"messages.updateSuccess":"messages.createSuccess")),s&&s(),o.reset(),c(!1)}).finally(()=>{i(!1)})};return e.jsxs(pe,{open:r,onOpenChange:c,children:[e.jsx(rs,{asChild:!0,children:a||e.jsxs(E,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(ze,{icon:"ion:add"}),e.jsx("span",{children:n("form.add")})]})}),e.jsxs(ue,{className:"sm:max-w-[425px]",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:n(l==="edit"?"form.edit":"form.create")}),e.jsx(Le,{children:n(l==="edit"?"form.editDescription":"form.createDescription")})]}),e.jsx(we,{...o,children:e.jsxs("form",{onSubmit:o.handleSubmit(d),className:"space-y-4",children:[e.jsx(v,{control:o.control,name:"name",render:({field:h})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.name")}),e.jsx(b,{children:e.jsx(D,{placeholder:n("form.namePlaceholder"),...h,className:"w-full"})}),e.jsx(F,{children:n("form.nameDescription")}),e.jsx(P,{})]})}),e.jsxs(Re,{className:"gap-2",children:[e.jsx(qs,{asChild:!0,children:e.jsx(E,{type:"button",variant:"outline",children:n("form.cancel")})}),e.jsxs(E,{type:"submit",disabled:u||!o.formState.isValid,children:[u&&e.jsx(gn,{className:"mr-2 h-4 w-4 animate-spin"}),n(l==="edit"?"form.update":"form.create")]})]})]})})]})]})}const Lr=m.createContext(void 0);function ku({children:s,refetch:a}){const[t,l]=m.useState(!1),[n,o]=m.useState(null),[r,c]=m.useState(re.Shadowsocks);return e.jsx(Lr.Provider,{value:{isOpen:t,setIsOpen:l,editingServer:n,setEditingServer:o,serverType:r,setServerType:c,refetch:a},children:s})}function Vr(){const s=m.useContext(Lr);if(s===void 0)throw new Error("useServerEdit must be used within a ServerEditProvider");return s}function Ga({dialogTrigger:s,value:a,setValue:t,templateType:l}){const{t:n}=V("server");m.useEffect(()=>{console.log(a)},[a]);const[o,r]=m.useState(!1),[c,u]=m.useState(()=>{if(!a||Object.keys(a).length===0)return"";try{return JSON.stringify(a,null,2)}catch{return""}}),[i,d]=m.useState(null),h=g=>{if(!g)return null;try{const k=JSON.parse(g);return typeof k!="object"||k===null?n("network_settings.validation.must_be_object"):null}catch{return n("network_settings.validation.invalid_json")}},_={tcp:{label:"TCP",content:{acceptProxyProtocol:!1,header:{type:"none"}}},"tcp-http":{label:"TCP + HTTP",content:{acceptProxyProtocol:!1,header:{type:"http",request:{version:"1.1",method:"GET",path:["/"],headers:{Host:["www.example.com"]}},response:{version:"1.1",status:"200",reason:"OK"}}}},grpc:{label:"gRPC",content:{serviceName:"GunService"}},ws:{label:"WebSocket",content:{path:"/",headers:{Host:"v2ray.com"}}},httpupgrade:{label:"HttpUpgrade",content:{acceptProxyProtocol:!1,path:"/",host:"xray.com",headers:{key:"value"}}},xhttp:{label:"XHTTP",content:{host:"example.com",path:"/yourpath",mode:"auto",extra:{headers:{},xPaddingBytes:"100-1000",noGRPCHeader:!1,noSSEHeader:!1,scMaxEachPostBytes:1e6,scMinPostsIntervalMs:30,scMaxBufferedPosts:30,xmux:{maxConcurrency:"16-32",maxConnections:0,cMaxReuseTimes:"64-128",cMaxLifetimeMs:0,hMaxRequestTimes:"800-900",hKeepAlivePeriod:0},downloadSettings:{address:"",port:443,network:"xhttp",security:"tls",tlsSettings:{},xhttpSettings:{path:"/yourpath"},sockopt:{}}}}}},T=()=>{switch(l){case"tcp":return["tcp","tcp-http"];case"grpc":return["grpc"];case"ws":return["ws"];case"httpupgrade":return["httpupgrade"];case"xhttp":return["xhttp"];default:return[]}},S=()=>{const g=h(c||"");if(g){A.error(g);return}try{if(!c){t(null),r(!1);return}t(JSON.parse(c)),r(!1)}catch{A.error(n("network_settings.errors.save_failed"))}},C=g=>{u(g),d(h(g))},N=g=>{const k=_[g];if(k){const R=JSON.stringify(k.content,null,2);u(R),d(null)}};return m.useEffect(()=>{o&&console.log(a)},[o,a]),m.useEffect(()=>{o&&a&&Object.keys(a).length>0&&u(JSON.stringify(a,null,2))},[o,a]),e.jsxs(pe,{open:o,onOpenChange:g=>{!g&&o&&S(),r(g)},children:[e.jsx(rs,{asChild:!0,children:s??e.jsx(G,{variant:"link",children:n("network_settings.edit_protocol")})}),e.jsxs(ue,{className:"sm:max-w-[425px]",children:[e.jsx(ve,{children:e.jsx(ge,{children:n("network_settings.edit_protocol_config")})}),e.jsxs("div",{className:"space-y-4",children:[T().length>0&&e.jsx("div",{className:"flex flex-wrap gap-2 pt-2",children:T().map(g=>e.jsx(G,{variant:"outline",size:"sm",onClick:()=>N(g),children:n("network_settings.use_template",{template:_[g].label})},g))}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(Ts,{className:`min-h-[200px] font-mono text-sm ${i?"border-red-500 focus-visible:ring-red-500":""}`,value:c,placeholder:T().length>0?n("network_settings.json_config_placeholder_with_template"):n("network_settings.json_config_placeholder"),onChange:g=>C(g.target.value)}),i&&e.jsx("p",{className:"text-sm text-red-500",children:i})]})]}),e.jsxs(Re,{className:"gap-2",children:[e.jsx(G,{variant:"outline",onClick:()=>r(!1),children:n("common.cancel")}),e.jsx(G,{onClick:S,disabled:!!i,children:n("common.confirm")})]})]})]})}function qh(s){throw new Error('Could not dynamically require "'+s+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}const Tu={},Du=Object.freeze(Object.defineProperty({__proto__:null,default:Tu},Symbol.toStringTag,{value:"Module"})),Hh=Lc(Du),Jn=s=>s.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""),Pu=()=>{try{const s=yc.box.keyPair(),a=Jn(qn.encodeBase64(s.secretKey)),t=Jn(qn.encodeBase64(s.publicKey));return{privateKey:a,publicKey:t}}catch(s){throw console.error("Error generating x25519 key pair:",s),s}},Eu=()=>{try{return Pu()}catch(s){throw console.error("Error generating key pair:",s),s}},Ru=s=>{const a=new Uint8Array(Math.ceil(s/2));return window.crypto.getRandomValues(a),Array.from(a).map(t=>t.toString(16).padStart(2,"0")).join("").substring(0,s)},Iu=()=>{const s=Math.floor(Math.random()*8)*2+2;return Ru(s)},Lu=x.object({cipher:x.string().default("aes-128-gcm"),plugin:x.string().optional().default(""),plugin_opts:x.string().optional().default(""),client_fingerprint:x.string().optional().default("chrome")}),Vu=x.object({tls:x.coerce.number().default(0),tls_settings:x.object({server_name:x.string().default(""),allow_insecure:x.boolean().default(!1)}).default({}),network:x.string().default("tcp"),network_settings:x.record(x.any()).default({})}),Fu=x.object({server_name:x.string().default(""),allow_insecure:x.boolean().default(!1),network:x.string().default("tcp"),network_settings:x.record(x.any()).default({})}),Mu=x.object({version:x.coerce.number().default(2),alpn:x.string().default("h2"),obfs:x.object({open:x.coerce.boolean().default(!1),type:x.string().default("salamander"),password:x.string().default("")}).default({}),tls:x.object({server_name:x.string().default(""),allow_insecure:x.boolean().default(!1)}).default({}),bandwidth:x.object({up:x.string().default(""),down:x.string().default("")}).default({}),hop_interval:x.number().optional(),port_range:x.string().optional()}),Ou=x.object({tls:x.coerce.number().default(0),tls_settings:x.object({server_name:x.string().default(""),allow_insecure:x.boolean().default(!1)}).default({}),reality_settings:x.object({server_port:x.coerce.number().default(443),server_name:x.string().default(""),allow_insecure:x.boolean().default(!1),public_key:x.string().default(""),private_key:x.string().default(""),short_id:x.string().default("")}).default({}),network:x.string().default("tcp"),network_settings:x.record(x.any()).default({}),flow:x.string().default("")}),zu=x.object({version:x.coerce.number().default(5),congestion_control:x.string().default("bbr"),alpn:x.array(x.string()).default(["h3"]),udp_relay_mode:x.string().default("native"),tls:x.object({server_name:x.string().default(""),allow_insecure:x.boolean().default(!1)}).default({})}),$u=x.object({}),Au=x.object({tls:x.coerce.number().default(0),tls_settings:x.object({server_name:x.string().default(""),allow_insecure:x.boolean().default(!1)}).default({})}),qu=x.object({tls:x.coerce.number().default(0),tls_settings:x.object({server_name:x.string().default(""),allow_insecure:x.boolean().default(!1)}).default({})}),Hu=x.object({transport:x.string().default("tcp"),multiplexing:x.string().default("MULTIPLEXING_LOW")}),Uu=x.object({padding_scheme:x.array(x.string()).optional().default([]),tls:x.object({server_name:x.string().default(""),allow_insecure:x.boolean().default(!1)}).default({})}),Te={shadowsocks:{schema:Lu,ciphers:["aes-128-gcm","aes-192-gcm","aes-256-gcm","chacha20-ietf-poly1305","2022-blake3-aes-128-gcm","2022-blake3-aes-256-gcm"],plugins:[{value:"none",label:"None"},{value:"obfs",label:"Simple Obfs"},{value:"v2ray-plugin",label:"V2Ray Plugin"}],clientFingerprints:[{value:"chrome",label:"Chrome"},{value:"firefox",label:"Firefox"},{value:"safari",label:"Safari"},{value:"ios",label:"iOS"}]},vmess:{schema:Vu,networkOptions:[{value:"tcp",label:"TCP"},{value:"ws",label:"Websocket"},{value:"grpc",label:"gRPC"}]},trojan:{schema:Fu,networkOptions:[{value:"tcp",label:"TCP"},{value:"ws",label:"Websocket"},{value:"grpc",label:"gRPC"}]},hysteria:{schema:Mu,versions:["1","2"],alpnOptions:["hysteria","http/1.1","h2","h3"]},vless:{schema:Ou,networkOptions:[{value:"tcp",label:"TCP"},{value:"ws",label:"Websocket"},{value:"grpc",label:"gRPC"},{value:"kcp",label:"mKCP"},{value:"httpupgrade",label:"HttpUpgrade"},{value:"xhttp",label:"XHTTP"}],flowOptions:["none","xtls-rprx-direct","xtls-rprx-splice","xtls-rprx-vision"]},tuic:{schema:zu,versions:["5","4"],congestionControls:["bbr","cubic","new_reno"],alpnOptions:[{value:"h3",label:"HTTP/3"},{value:"h2",label:"HTTP/2"},{value:"http/1.1",label:"HTTP/1.1"}],udpRelayModes:[{value:"native",label:"Native"},{value:"quic",label:"QUIC"}]},socks:{schema:$u},naive:{schema:qu},http:{schema:Au},mieru:{schema:Hu,transportOptions:[{value:"tcp",label:"TCP"},{value:"udp",label:"UDP"}],multiplexingOptions:[{value:"MULTIPLEXING_OFF",label:"Off"},{value:"MULTIPLEXING_LOW",label:"Low"},{value:"MULTIPLEXING_MIDDLE",label:"Middle"},{value:"MULTIPLEXING_HIGH",label:"High"}]},anytls:{schema:Uu,defaultPaddingScheme:["stop=8","0=30-30","1=100-400","2=400-500,c,500-1000,c,500-1000,c,500-1000,c,500-1000","3=9-9,500-1000","4=500-1000","5=500-1000","6=500-1000","7=500-1000"]}},Ku=({serverType:s,value:a,onChange:t})=>{const{t:l}=V("server"),n=s?Te[s]:null,o=n?.schema||x.record(x.any()),r=s?o.parse({}):{},c=ye({resolver:_e(o),defaultValues:r,mode:"onChange"});if(m.useEffect(()=>{if(!a||Object.keys(a).length===0){if(s){const p=o.parse({});c.reset(p)}}else c.reset(a)},[s,a,t,c,o]),m.useEffect(()=>{const p=c.watch(w=>{t(w)});return()=>p.unsubscribe()},[c,t]),!s||!n)return null;const R={shadowsocks:()=>e.jsxs(e.Fragment,{children:[e.jsx(v,{control:c.control,name:"cipher",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.shadowsocks.cipher.label")}),e.jsx(b,{children:e.jsxs(X,{onValueChange:p.onChange,value:p.value,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.shadowsocks.cipher.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.shadowsocks.ciphers.map(w=>e.jsx($,{value:w,children:w},w))})})]})})]})}),e.jsx(v,{control:c.control,name:"plugin",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.shadowsocks.plugin.label","插件")}),e.jsx(b,{children:e.jsxs(X,{onValueChange:w=>p.onChange(w==="none"?"":w),value:p.value===""?"none":p.value||"none",children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.shadowsocks.plugin.placeholder","选择插件")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.shadowsocks.plugins.map(w=>e.jsx($,{value:w.value,children:w.label},w.value))})})]})}),e.jsx(F,{children:p.value&&p.value!=="none"&&p.value!==""&&e.jsxs(e.Fragment,{children:[p.value==="obfs"&&l("dynamic_form.shadowsocks.plugin.obfs_hint","提示：配置格式如 obfs=http;obfs-host=www.bing.com;path=/"),p.value==="v2ray-plugin"&&l("dynamic_form.shadowsocks.plugin.v2ray_hint","提示：WebSocket模式格式为 mode=websocket;host=mydomain.me;path=/;tls=true，QUIC模式格式为 mode=quic;host=mydomain.me")]})})]})}),c.watch("plugin")&&c.watch("plugin")!=="none"&&c.watch("plugin")!==""&&e.jsx(v,{control:c.control,name:"plugin_opts",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.shadowsocks.plugin_opts.label","插件选项")}),e.jsx(F,{children:l("dynamic_form.shadowsocks.plugin_opts.description","按照 key=value;key2=value2 格式输入插件选项")}),e.jsx(b,{children:e.jsx(D,{type:"text",placeholder:l("dynamic_form.shadowsocks.plugin_opts.placeholder","例如: mode=tls;host=bing.com"),...p})})]})}),(c.watch("plugin")==="shadow-tls"||c.watch("plugin")==="restls")&&e.jsx(v,{control:c.control,name:"client_fingerprint",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.shadowsocks.client_fingerprint","客户端指纹")}),e.jsx(b,{children:e.jsxs(X,{value:p.value||"chrome",onValueChange:p.onChange,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.shadowsocks.client_fingerprint_placeholder","选择客户端指纹")})}),e.jsx(J,{children:Te.shadowsocks.clientFingerprints.map(w=>e.jsx($,{value:w.value,children:w.label},w.value))})]})}),e.jsx(F,{children:l("dynamic_form.shadowsocks.client_fingerprint_description","客户端伪装指纹，用于降低被识别风险")})]})})]}),vmess:()=>e.jsxs(e.Fragment,{children:[e.jsx(v,{control:c.control,name:"tls",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.vmess.tls.label")}),e.jsx(b,{children:e.jsxs(X,{value:p.value?.toString(),onValueChange:w=>p.onChange(Number(w)),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.vmess.tls.placeholder")})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:l("dynamic_form.vmess.tls.disabled")}),e.jsx($,{value:"1",children:l("dynamic_form.vmess.tls.enabled")})]})]})})]})}),c.watch("tls")==1&&e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"tls_settings.server_name",render:({field:p})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.vmess.tls_settings.server_name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.vmess.tls_settings.server_name.placeholder"),...p})})]})}),e.jsx(v,{control:c.control,name:"tls_settings.allow_insecure",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.vmess.tls_settings.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(b,{children:e.jsx(ee,{checked:p.value,onCheckedChange:p.onChange})})})]})})]}),e.jsx(v,{control:c.control,name:"network",render:({field:p})=>e.jsxs(f,{children:[e.jsxs(j,{children:[l("dynamic_form.vmess.network.label"),e.jsx(Ga,{value:c.watch("network_settings"),setValue:w=>c.setValue("network_settings",w),templateType:c.watch("network")})]}),e.jsx(b,{children:e.jsxs(X,{onValueChange:p.onChange,value:p.value,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.vmess.network.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.vmess.networkOptions.map(w=>e.jsx($,{value:w.value,className:"cursor-pointer",children:w.label},w.value))})})]})})]})})]}),trojan:()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"server_name",render:({field:p})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.trojan.server_name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.trojan.server_name.placeholder"),...p,value:p.value||""})})]})}),e.jsx(v,{control:c.control,name:"allow_insecure",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.trojan.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(b,{children:e.jsx(ee,{checked:p.value||!1,onCheckedChange:p.onChange})})})]})})]}),e.jsx(v,{control:c.control,name:"network",render:({field:p})=>e.jsxs(f,{children:[e.jsxs(j,{children:[l("dynamic_form.trojan.network.label"),e.jsx(Ga,{value:c.watch("network_settings")||{},setValue:w=>c.setValue("network_settings",w),templateType:c.watch("network")||"tcp"})]}),e.jsx(b,{children:e.jsxs(X,{onValueChange:p.onChange,value:p.value||"tcp",children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.trojan.network.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.trojan.networkOptions.map(w=>e.jsx($,{value:w.value,className:"cursor-pointer",children:w.label},w.value))})})]})})]})})]}),hysteria:()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"version",render:({field:p})=>e.jsxs(f,{className:"flex-1",children:[e.jsx(j,{children:l("dynamic_form.hysteria.version.label")}),e.jsx(b,{children:e.jsxs(X,{value:(p.value||2).toString(),onValueChange:w=>p.onChange(Number(w)),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.hysteria.version.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.hysteria.versions.map(w=>e.jsxs($,{value:w,className:"cursor-pointer",children:["V",w]},w))})})]})})]})}),c.watch("version")==1&&e.jsx(v,{control:c.control,name:"alpn",render:({field:p})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.hysteria.alpn.label")}),e.jsx(b,{children:e.jsxs(X,{value:p.value||"h2",onValueChange:p.onChange,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.hysteria.alpn.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.hysteria.alpnOptions.map(w=>e.jsx($,{value:w,children:w},w))})})]})})]})})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"obfs.open",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.hysteria.obfs.label")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(b,{children:e.jsx(ee,{checked:p.value||!1,onCheckedChange:p.onChange})})})]})}),!!c.watch("obfs.open")&&e.jsxs(e.Fragment,{children:[c.watch("version")=="2"&&e.jsx(v,{control:c.control,name:"obfs.type",render:({field:p})=>e.jsxs(f,{className:"flex-1",children:[e.jsx(j,{children:l("dynamic_form.hysteria.obfs.type.label")}),e.jsx(b,{children:e.jsxs(X,{value:p.value||"salamander",onValueChange:p.onChange,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.hysteria.obfs.type.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:e.jsx($,{value:"salamander",children:l("dynamic_form.hysteria.obfs.type.salamander")})})})]})})]})}),e.jsx(v,{control:c.control,name:"obfs.password",render:({field:p})=>e.jsxs(f,{className:c.watch("version")==2?"w-full":"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.hysteria.obfs.password.label")}),e.jsxs("div",{className:"relative",children:[e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.hysteria.obfs.password.placeholder"),...p,value:p.value||"",className:"pr-9"})}),e.jsx(G,{type:"button",variant:"ghost",size:"icon",onClick:()=>{const w="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",I=Array.from(crypto.getRandomValues(new Uint8Array(16))).map(H=>w[H%w.length]).join("");c.setValue("obfs.password",I),A.success(l("dynamic_form.hysteria.obfs.password.generate_success"))},className:"absolute right-0 top-0 h-full px-2  active:scale-90 transition-transform duration-150",children:e.jsx(ze,{icon:"ion:refresh-outline",className:"h-4 w-4 transition-transform hover:rotate-180 duration-300"})})]})]})})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"tls.server_name",render:({field:p})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.hysteria.tls.server_name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.hysteria.tls.server_name.placeholder"),...p,value:p.value||""})})]})}),e.jsx(v,{control:c.control,name:"tls.allow_insecure",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.hysteria.tls.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(b,{children:e.jsx(ee,{checked:p.value||!1,onCheckedChange:p.onChange})})})]})})]}),e.jsx(v,{control:c.control,name:"bandwidth.up",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.hysteria.bandwidth.up.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:l("dynamic_form.hysteria.bandwidth.up.placeholder")+(c.watch("version")==2?l("dynamic_form.hysteria.bandwidth.up.bbr_tip"):""),className:"rounded-br-none rounded-tr-none",...p,value:p.value||""})}),e.jsx("div",{className:"pointer-events-none z-[-1] flex items-center rounded-md rounded-bl-none rounded-tl-none border border-l-0 border-input px-3 shadow-sm",children:e.jsx("span",{className:"text-gray-500",children:l("dynamic_form.hysteria.bandwidth.up.suffix")})})]})]})}),e.jsx(v,{control:c.control,name:"bandwidth.down",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.hysteria.bandwidth.down.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:l("dynamic_form.hysteria.bandwidth.down.placeholder")+(c.watch("version")==2?l("dynamic_form.hysteria.bandwidth.down.bbr_tip"):""),className:"rounded-br-none rounded-tr-none",...p,value:p.value||""})}),e.jsx("div",{className:"pointer-events-none z-[-1] flex items-center rounded-md rounded-bl-none rounded-tl-none border border-l-0 border-input px-3 shadow-sm",children:e.jsx("span",{className:"text-gray-500",children:l("dynamic_form.hysteria.bandwidth.down.suffix")})})]})]})}),e.jsx(e.Fragment,{children:e.jsx(v,{control:c.control,name:"hop_interval",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.hysteria.hop_interval.label","Hop 间隔 (秒)")}),e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:l("dynamic_form.hysteria.hop_interval.placeholder","例如: 30"),...p,value:p.value||"",onChange:w=>{const I=w.target.value?parseInt(w.target.value):void 0;p.onChange(I)}})}),e.jsx(F,{children:l("dynamic_form.hysteria.hop_interval.description","Hop 间隔时间，单位为秒")})]})})})]}),vless:()=>e.jsxs(e.Fragment,{children:[e.jsx(v,{control:c.control,name:"tls",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.vless.tls.label")}),e.jsx(b,{children:e.jsxs(X,{value:p.value?.toString(),onValueChange:w=>p.onChange(Number(w)),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.vless.tls.placeholder")})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:l("dynamic_form.vless.tls.none")}),e.jsx($,{value:"1",children:l("dynamic_form.vless.tls.tls")}),e.jsx($,{value:"2",children:l("dynamic_form.vless.tls.reality")})]})]})})]})}),c.watch("tls")=="1"&&e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"tls_settings.server_name",render:({field:p})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.vless.tls_settings.server_name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.vless.tls_settings.server_name.placeholder"),...p})})]})}),e.jsx(v,{control:c.control,name:"tls_settings.allow_insecure",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.vless.tls_settings.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(b,{children:e.jsx(ee,{checked:p.value,onCheckedChange:p.onChange})})})]})})]}),c.watch("tls")==2&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"reality_settings.server_name",render:({field:p})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.vless.reality_settings.server_name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.vless.reality_settings.server_name.placeholder"),...p})})]})}),e.jsx(v,{control:c.control,name:"reality_settings.server_port",render:({field:p})=>e.jsxs(f,{className:"flex-1",children:[e.jsx(j,{children:l("dynamic_form.vless.reality_settings.server_port.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.vless.reality_settings.server_port.placeholder"),...p})})]})}),e.jsx(v,{control:c.control,name:"reality_settings.allow_insecure",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.vless.reality_settings.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(b,{children:e.jsx(ee,{checked:p.value,onCheckedChange:p.onChange})})})]})})]}),e.jsx("div",{className:"flex items-end gap-2",children:e.jsx(v,{control:c.control,name:"reality_settings.private_key",render:({field:p})=>e.jsxs(f,{className:"flex-1",children:[e.jsx(j,{children:l("dynamic_form.vless.reality_settings.private_key.label")}),e.jsxs("div",{className:"relative",children:[e.jsx(b,{children:e.jsx(D,{...p,className:"pr-9"})}),e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(G,{type:"button",variant:"ghost",size:"icon",onClick:()=>{try{const w=Eu();c.setValue("reality_settings.private_key",w.privateKey),c.setValue("reality_settings.public_key",w.publicKey),A.success(l("dynamic_form.vless.reality_settings.key_pair.success"))}catch{A.error(l("dynamic_form.vless.reality_settings.key_pair.error"))}},className:"absolute right-0 top-0 h-full px-2 active:scale-90 transition-transform duration-150",children:e.jsx(ze,{icon:"ion:key-outline",className:"h-4 w-4 transition-transform hover:rotate-180 duration-300"})})}),e.jsx(ma,{children:e.jsx(de,{children:e.jsx("p",{children:l("dynamic_form.vless.reality_settings.key_pair.generate")})})})]})]})]})})}),e.jsx(v,{control:c.control,name:"reality_settings.public_key",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.vless.reality_settings.public_key.label")}),e.jsx(b,{children:e.jsx(D,{...p})})]})}),e.jsx(v,{control:c.control,name:"reality_settings.short_id",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.vless.reality_settings.short_id.label")}),e.jsxs("div",{className:"relative",children:[e.jsx(b,{children:e.jsx(D,{...p,className:"pr-9",placeholder:l("dynamic_form.vless.reality_settings.short_id.placeholder")})}),e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(G,{type:"button",variant:"ghost",size:"icon",onClick:()=>{const w=Iu();c.setValue("reality_settings.short_id",w),A.success(l("dynamic_form.vless.reality_settings.short_id.success"))},className:"absolute right-0 top-0 h-full px-2 active:scale-90 transition-transform duration-150",children:e.jsx(ze,{icon:"ion:refresh-outline",className:"h-4 w-4 transition-transform hover:rotate-180 duration-300"})})}),e.jsx(ma,{children:e.jsx(de,{children:e.jsx("p",{children:l("dynamic_form.vless.reality_settings.short_id.generate")})})})]})]}),e.jsx(F,{className:"text-xs text-muted-foreground",children:l("dynamic_form.vless.reality_settings.short_id.description")})]})})]}),e.jsx(v,{control:c.control,name:"network",render:({field:p})=>e.jsxs(f,{children:[e.jsxs(j,{children:[l("dynamic_form.vless.network.label"),e.jsx(Ga,{value:c.watch("network_settings"),setValue:w=>c.setValue("network_settings",w),templateType:c.watch("network")})]}),e.jsx(b,{children:e.jsxs(X,{onValueChange:p.onChange,value:p.value,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.vless.network.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.vless.networkOptions.map(w=>e.jsx($,{value:w.value,className:"cursor-pointer",children:w.label},w.value))})})]})})]})}),e.jsx(v,{control:c.control,name:"flow",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.vless.flow.label")}),e.jsx(b,{children:e.jsxs(X,{onValueChange:w=>p.onChange(w==="none"?null:w),value:p.value||"none",children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.vless.flow.placeholder")})}),e.jsx(J,{children:Te.vless.flowOptions.map(w=>e.jsx($,{value:w,children:w},w))})]})})]})})]}),tuic:()=>e.jsxs(e.Fragment,{children:[e.jsx(v,{control:c.control,name:"version",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.tuic.version.label")}),e.jsx(b,{children:e.jsxs(X,{value:p.value?.toString(),onValueChange:w=>p.onChange(Number(w)),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.tuic.version.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.tuic.versions.map(w=>e.jsxs($,{value:w,children:["V",w]},w))})})]})})]})}),e.jsx(v,{control:c.control,name:"congestion_control",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.tuic.congestion_control.label")}),e.jsx(b,{children:e.jsxs(X,{onValueChange:p.onChange,value:p.value,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.tuic.congestion_control.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.tuic.congestionControls.map(w=>e.jsx($,{value:w,children:w.toUpperCase()},w))})})]})})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"tls.server_name",render:({field:p})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.tuic.tls.server_name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.tuic.tls.server_name.placeholder"),...p})})]})}),e.jsx(v,{control:c.control,name:"tls.allow_insecure",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.tuic.tls.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(b,{children:e.jsx(ee,{checked:p.value,onCheckedChange:p.onChange})})})]})})]}),e.jsx(v,{control:c.control,name:"alpn",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.tuic.tls.alpn.label")}),e.jsx(b,{children:e.jsx(_t,{options:Te.tuic.alpnOptions,onChange:w=>p.onChange(w.map(I=>I.value)),value:Te.tuic.alpnOptions.filter(w=>p.value?.includes(w.value)),placeholder:l("dynamic_form.tuic.tls.alpn.placeholder"),emptyIndicator:e.jsx("p",{className:"text-center text-lg leading-10 text-gray-600 dark:text-gray-400",children:l("dynamic_form.tuic.tls.alpn.empty")})})})]})}),e.jsx(v,{control:c.control,name:"udp_relay_mode",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.tuic.udp_relay_mode.label")}),e.jsx(b,{children:e.jsxs(X,{onValueChange:p.onChange,value:p.value,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.tuic.udp_relay_mode.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.tuic.udpRelayModes.map(w=>e.jsx($,{value:w.value,children:w.label},w.value))})})]})})]})})]}),socks:()=>e.jsx(e.Fragment,{}),naive:()=>e.jsxs(e.Fragment,{children:[e.jsx(v,{control:c.control,name:"tls",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.naive.tls.label")}),e.jsx(b,{children:e.jsxs(X,{value:p.value?.toString(),onValueChange:w=>p.onChange(Number(w)),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.naive.tls.placeholder")})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:l("dynamic_form.naive.tls.disabled")}),e.jsx($,{value:"1",children:l("dynamic_form.naive.tls.enabled")})]})]})})]})}),c.watch("tls")==1&&e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"tls_settings.server_name",render:({field:p})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.naive.tls_settings.server_name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.naive.tls_settings.server_name.placeholder"),...p})})]})}),e.jsx(v,{control:c.control,name:"tls_settings.allow_insecure",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.naive.tls_settings.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(b,{children:e.jsx(ee,{checked:p.value,onCheckedChange:p.onChange})})})]})})]})]}),http:()=>e.jsxs(e.Fragment,{children:[e.jsx(v,{control:c.control,name:"tls",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.http.tls.label")}),e.jsx(b,{children:e.jsxs(X,{value:p.value?.toString(),onValueChange:w=>p.onChange(Number(w)),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.http.tls.placeholder")})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:l("dynamic_form.http.tls.disabled")}),e.jsx($,{value:"1",children:l("dynamic_form.http.tls.enabled")})]})]})})]})}),c.watch("tls")==1&&e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"tls_settings.server_name",render:({field:p})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.http.tls_settings.server_name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.http.tls_settings.server_name.placeholder"),...p})})]})}),e.jsx(v,{control:c.control,name:"tls_settings.allow_insecure",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.http.tls_settings.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(b,{children:e.jsx(ee,{checked:p.value,onCheckedChange:p.onChange})})})]})})]})]}),mieru:()=>e.jsxs(e.Fragment,{children:[e.jsx(v,{control:c.control,name:"transport",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.mieru.transport.label")}),e.jsx(b,{children:e.jsxs(X,{onValueChange:p.onChange,value:p.value,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.mieru.transport.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.mieru.transportOptions.map(w=>e.jsx($,{value:w.value,children:w.label},w.value))})})]})})]})}),e.jsx(v,{control:c.control,name:"multiplexing",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.mieru.multiplexing.label")}),e.jsx(b,{children:e.jsxs(X,{onValueChange:p.onChange,value:p.value,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dynamic_form.mieru.multiplexing.placeholder")})}),e.jsx(J,{children:e.jsx(Be,{children:Te.mieru.multiplexingOptions.map(w=>e.jsx($,{value:w.value,children:w.label},w.value))})})]})})]})})]}),anytls:()=>e.jsx(e.Fragment,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:c.control,name:"padding_scheme",render:({field:p})=>e.jsxs(f,{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(j,{children:l("dynamic_form.anytls.padding_scheme.label","AnyTLS 填充方案")}),e.jsx(G,{type:"button",variant:"outline",size:"sm",onClick:()=>{c.setValue("padding_scheme",Te.anytls.defaultPaddingScheme),A.success(l("dynamic_form.anytls.padding_scheme.default_success","已设置默认填充方案"))},className:"h-7 px-2",children:l("dynamic_form.anytls.padding_scheme.use_default","使用默认方案")})]}),e.jsx(F,{children:l("dynamic_form.anytls.padding_scheme.description","每行一个填充规则，格式如: stop=8, 0=30-30")}),e.jsx(b,{children:e.jsx("textarea",{className:"flex min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",placeholder:l("dynamic_form.anytls.padding_scheme.placeholder",`例如:
stop=8
0=30-30
1=100-400
2=400-500,c,500-1000`),...p,value:Array.isArray(p.value)?p.value.join(`
`):"",onChange:w=>{const H=w.target.value.split(`
`).filter(O=>O.trim()!=="");p.onChange(H)}})})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:c.control,name:"tls.server_name",render:({field:p})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:l("dynamic_form.anytls.tls.server_name.label","SNI")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dynamic_form.anytls.tls.server_name.placeholder","服务器名称"),...p})})]})}),e.jsx(v,{control:c.control,name:"tls.allow_insecure",render:({field:p})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dynamic_form.anytls.tls.allow_insecure","允许不安全连接")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(b,{children:e.jsx(ee,{checked:p.value,onCheckedChange:p.onChange})})})]})})]})]})})};return e.jsx(be,{children:R[s]?.()})};function Bu(){const{t:s}=V("server"),a=x.object({id:x.number().optional().nullable(),specific_key:x.string().optional().nullable(),code:x.string().optional(),show:x.boolean().optional().nullable(),name:x.string().min(1,s("form.name.error")),rate:x.string().min(1,s("form.rate.error")).refine(I=>!isNaN(parseFloat(I))&&isFinite(Number(I)),{message:s("form.rate.error_numeric")}).refine(I=>parseFloat(I)>=0,{message:s("form.rate.error_gte_zero")}),tags:x.array(x.string()).default([]),excludes:x.array(x.string()).default([]),ips:x.array(x.string()).default([]),group_ids:x.array(x.string()).default([]),host:x.string().min(1,s("form.host.error")),port:x.string().min(1,s("form.port.error")),server_port:x.string().min(1,s("form.server_port.error")),parent_id:x.string().default("0").nullable(),route_ids:x.array(x.string()).default([]),protocol_settings:x.record(x.any()).default({}).nullable()}),t={id:null,specific_key:null,code:"",show:!1,name:"",rate:"1",tags:[],excludes:[],ips:[],group_ids:[],host:"",port:"",server_port:"",parent_id:"0",route_ids:[],protocol_settings:null},{isOpen:l,setIsOpen:n,editingServer:o,setEditingServer:r,serverType:c,setServerType:u,refetch:i}=Vr(),[d,h]=m.useState([]),[_,T]=m.useState([]),[S,C]=m.useState([]),N=ye({resolver:_e(a),defaultValues:t,mode:"onChange"});m.useEffect(()=>{g()},[l]),m.useEffect(()=>{o?.type&&o.type!==c&&u(o.type)},[o,c,u]),m.useEffect(()=>{o?o.type===c&&N.reset({...t,...o}):N.reset({...t,protocol_settings:Te[c].schema.parse({})})},[o,N,c]);const g=async()=>{if(!l)return;const[I,H,O]=await Promise.all([at.getList(),Sa.getList(),Js.getList()]);h(I.data?.map(K=>({label:K.name,value:K.id.toString()}))||[]),T(H.data?.map(K=>({label:K.remarks,value:K.id.toString()}))||[]),C(O.data||[])},k=m.useMemo(()=>S?.filter(I=>(I.parent_id===0||I.parent_id===null)&&I.type===c&&I.id!==N.watch("id")),[c,S,N]),R=()=>e.jsxs(Es,{children:[e.jsx(Rs,{asChild:!0,children:e.jsxs(E,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(ze,{icon:"ion:add"}),e.jsx("div",{children:s("form.add_node")})]})}),e.jsx(Cs,{align:"start",children:e.jsx(jd,{children:cs.map(({type:I,label:H})=>e.jsx(Ne,{onClick:()=>{u(I),n(!0)},className:"cursor-pointer",children:e.jsx(B,{variant:"outline",className:"text-white",style:{background:Ge[I]},children:H})},I))})})]}),p=()=>{n(!1),r(null),N.reset(t)},w=async()=>{const I=N.getValues();(await Js.save({...I,type:c})).data&&(p(),A.success(s("form.success")),i())};return e.jsxs(pe,{open:l,onOpenChange:p,children:[R(),e.jsxs(ue,{className:"sm:max-w-[425px]",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:s(o?"form.edit_node":"form.new_node")}),e.jsx(Le,{})]}),e.jsxs(we,{...N,children:[e.jsxs("div",{className:"grid gap-4",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{control:N.control,name:"name",render:({field:I})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:s("form.name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("form.name.placeholder"),...I})}),e.jsx(P,{})]})}),e.jsx(v,{control:N.control,name:"rate",render:({field:I})=>e.jsxs(f,{className:"flex-[1]",children:[e.jsx(j,{children:s("form.rate.label")}),e.jsx("div",{className:"relative flex",children:e.jsx(b,{children:e.jsx(D,{type:"number",min:"0",step:"0.1",...I})})}),e.jsx(P,{})]})})]}),e.jsx(v,{control:N.control,name:"code",render:({field:I})=>e.jsxs(f,{children:[e.jsxs(j,{children:[s("form.code.label"),e.jsx("span",{className:"ml-1 text-xs text-muted-foreground",children:s("form.code.optional")})]}),e.jsx(b,{children:e.jsx(D,{placeholder:s("form.code.placeholder"),...I,value:I.value||""})}),e.jsx(P,{})]})}),e.jsx(v,{control:N.control,name:"tags",render:({field:I})=>e.jsxs(f,{children:[e.jsx(j,{children:s("form.tags.label")}),e.jsx(b,{children:e.jsx(Sn,{value:I.value,onChange:I.onChange,placeholder:s("form.tags.placeholder"),className:"w-full"})}),e.jsx(P,{})]})}),e.jsx(v,{control:N.control,name:"group_ids",render:({field:I})=>e.jsxs(f,{children:[e.jsxs(j,{className:"flex items-center justify-between",children:[s("form.groups.label"),e.jsx(Va,{dialogTrigger:e.jsx(E,{variant:"link",children:s("form.groups.add")}),refetch:g})]}),e.jsx(b,{children:e.jsx(_t,{options:d,onChange:H=>I.onChange(H.map(O=>O.value)),value:d?.filter(H=>I.value.includes(H.value)),placeholder:s("form.groups.placeholder"),emptyIndicator:e.jsx("p",{className:"text-center text-lg leading-10 text-gray-600 dark:text-gray-400",children:s("form.groups.empty")})})}),e.jsx(P,{})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:N.control,name:"host",render:({field:I})=>e.jsxs(f,{children:[e.jsx(j,{children:s("form.host.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:s("form.host.placeholder"),...I})}),e.jsx(P,{})]})}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(v,{control:N.control,name:"port",render:({field:I})=>e.jsxs(f,{className:"flex-1",children:[e.jsxs(j,{className:"flex items-center gap-1.5",children:[s("form.port.label"),e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(ze,{icon:"ph:info-light",className:"size-3.5 cursor-help text-muted-foreground"})}),e.jsx(ma,{children:e.jsx(de,{side:"top",sideOffset:8,className:"max-w-80 p-3",children:e.jsx("p",{children:s("form.port.tooltip")})})})]})})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(b,{children:e.jsx(D,{placeholder:s("form.port.placeholder"),...I})}),e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(E,{type:"button",variant:"ghost",size:"icon",className:"size-6 shrink-0 text-muted-foreground/50 hover:text-muted-foreground",onClick:()=>{const H=I.value;H&&N.setValue("server_port",H)},children:e.jsx(ze,{icon:"tabler:arrows-right",className:"size-3"})})}),e.jsx(de,{side:"right",children:e.jsx("p",{children:s("form.port.sync")})})]})})]}),e.jsx(P,{})]})}),e.jsx(v,{control:N.control,name:"server_port",render:({field:I})=>e.jsxs(f,{className:"flex-1",children:[e.jsxs(j,{className:"flex items-center gap-1.5",children:[s("form.server_port.label"),e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(ze,{icon:"ph:info-light",className:"size-3.5 cursor-help text-muted-foreground"})}),e.jsx(ma,{children:e.jsx(de,{side:"top",sideOffset:8,className:"max-w-80 p-3",children:e.jsx("p",{children:s("form.server_port.tooltip")})})})]})})]}),e.jsx(b,{children:e.jsx(D,{placeholder:s("form.server_port.placeholder"),...I})}),e.jsx(P,{})]})})]})]}),l&&e.jsx(Ku,{serverType:c,value:N.watch("protocol_settings"),onChange:I=>N.setValue("protocol_settings",I,{shouldDirty:!0,shouldTouch:!0,shouldValidate:!0})}),e.jsx(v,{control:N.control,name:"parent_id",render:({field:I})=>e.jsxs(f,{children:[e.jsx(j,{children:s("form.parent.label")}),e.jsxs(X,{onValueChange:I.onChange,value:I.value?.toString()||"0",children:[e.jsx(b,{children:e.jsx(Y,{children:e.jsx(Z,{placeholder:s("form.parent.placeholder")})})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:s("form.parent.none")}),k?.map(H=>e.jsx($,{value:H.id.toString(),className:"cursor-pointer",children:H.name},H.id))]})]}),e.jsx(P,{})]})}),e.jsx(v,{control:N.control,name:"route_ids",render:({field:I})=>e.jsxs(f,{children:[e.jsx(j,{children:s("form.route.label")}),e.jsx(b,{children:e.jsx(_t,{options:_,onChange:H=>I.onChange(H.map(O=>O.value)),value:_?.filter(H=>I.value.includes(H.value)),placeholder:s("form.route.placeholder"),emptyIndicator:e.jsx("p",{className:"text-center text-lg leading-10 text-gray-600 dark:text-gray-400",children:s("form.route.empty")})})}),e.jsx(P,{})]})})]}),e.jsxs(Re,{className:"mt-6 flex flex-col sm:flex-row gap-2 sm:gap-0",children:[e.jsx(E,{type:"button",variant:"outline",onClick:p,className:"w-full sm:w-auto",children:s("form.cancel")}),e.jsx(E,{type:"submit",onClick:w,className:"w-full sm:w-auto",children:s("form.submit")})]})]})]})]})}function Qn({column:s,title:a,options:t}){const l=s?.getFacetedUniqueValues(),n=new Set(s?.getFilterValue());return e.jsxs(Ss,{children:[e.jsx(ks,{asChild:!0,children:e.jsxs(E,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(_a,{className:"mr-2 h-4 w-4"}),a,n?.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(ke,{orientation:"vertical",className:"mx-2 h-4"}),e.jsx(B,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:n.size}),e.jsx("div",{className:"hidden space-x-1 lg:flex",children:n.size>2?e.jsxs(B,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[n.size," selected"]}):t.filter(o=>n.has(o.value)).map(o=>e.jsx(B,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:o.label},o.value))})]})]})}),e.jsx(bs,{className:"w-[200px] p-0",align:"start",children:e.jsxs(Us,{children:[e.jsx(nt,{placeholder:a}),e.jsxs(Ks,{children:[e.jsx(lt,{children:"No results found."}),e.jsx(as,{children:t.map(o=>{const r=n.has(o.value);return e.jsxs($e,{onSelect:()=>{r?n.delete(o.value):n.add(o.value);const c=Array.from(n);s?.setFilterValue(c.length?c:void 0)},className:"cursor-pointer",children:[e.jsx("div",{className:y("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",r?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(et,{className:y("h-4 w-4")})}),o.icon&&e.jsx(o.icon,{className:`mr-2 h-4 w-4 text-muted-foreground text-${o.color}`}),e.jsx("span",{children:o.label}),l?.get(o.value)&&e.jsx("span",{className:"ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs",children:l.get(o.value)})]},o.value)})}),n.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(St,{}),e.jsx(as,{children:e.jsx($e,{onSelect:()=>s?.setFilterValue(void 0),className:"justify-center text-center cursor-pointer",children:"Clear filters"})})]})]})]})})]})}const Gu=[{value:re.Shadowsocks,label:cs.find(s=>s.type===re.Shadowsocks)?.label,color:Ge[re.Shadowsocks]},{value:re.Vmess,label:cs.find(s=>s.type===re.Vmess)?.label,color:Ge[re.Vmess]},{value:re.Trojan,label:cs.find(s=>s.type===re.Trojan)?.label,color:Ge[re.Trojan]},{value:re.Hysteria,label:cs.find(s=>s.type===re.Hysteria)?.label,color:Ge[re.Hysteria]},{value:re.Vless,label:cs.find(s=>s.type===re.Vless)?.label,color:Ge[re.Vless]},{value:re.Tuic,label:cs.find(s=>s.type===re.Tuic)?.label,color:Ge[re.Tuic]},{value:re.Socks,label:cs.find(s=>s.type===re.Socks)?.label,color:Ge[re.Socks]},{value:re.Naive,label:cs.find(s=>s.type===re.Naive)?.label,color:Ge[re.Naive]},{value:re.Http,label:cs.find(s=>s.type===re.Http)?.label,color:Ge[re.Http]},{value:re.Mieru,label:cs.find(s=>s.type===re.Mieru)?.label,color:Ge[re.Mieru]}];function Wu({table:s,saveOrder:a,isSortMode:t,groups:l}){const n=s.getState().columnFilters.length>0,{t:o}=V("server");return e.jsxs("div",{className:"flex items-center justify-between ",children:[e.jsxs("div",{className:"flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2",children:[!t&&e.jsxs(e.Fragment,{children:[e.jsx(Bu,{}),e.jsx(D,{placeholder:o("toolbar.search"),value:s.getColumn("name")?.getFilterValue()??"",onChange:r=>s.getColumn("name")?.setFilterValue(r.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),e.jsxs("div",{className:"flex gap-x-2",children:[s.getColumn("type")&&e.jsx(Qn,{column:s.getColumn("type"),title:o("toolbar.type"),options:Gu}),s.getColumn("group_ids")&&e.jsx(Qn,{column:s.getColumn("group_ids"),title:o("columns.groups.title"),options:l.map(r=>({label:r.name,value:r.id.toString()}))})]}),n&&e.jsxs(E,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:[o("toolbar.reset"),e.jsx(ds,{className:"ml-2 h-4 w-4"})]})]}),t&&e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx("p",{className:"text-sm text-muted-foreground",children:o("toolbar.sort.tip")})})]}),s.getRowCount()>0&&e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(E,{variant:t?"default":"outline",onClick:a,size:"sm",children:o(t?"toolbar.sort.save":"toolbar.sort.edit")})})]})}const Ut=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M15.71 12.71a6 6 0 1 0-7.42 0a10 10 0 0 0-6.22 8.18a1 1 0 0 0 2 .22a8 8 0 0 1 15.9 0a1 1 0 0 0 1 .89h.11a1 1 0 0 0 .88-1.1a10 10 0 0 0-6.25-8.19M12 12a4 4 0 1 1 4-4a4 4 0 0 1-4 4"})}),ta={0:"bg-destructive/80 shadow-sm shadow-destructive/50",1:"bg-yellow-500/80 shadow-sm shadow-yellow-500/50",2:"bg-emerald-500/80 shadow-sm shadow-emerald-500/50"},Pe=(s,a)=>a>0?Math.round(s/a*100):0,Yu=s=>{const{t:a}=V("server");return[{id:"drag-handle",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.sort")}),cell:()=>e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(Na,{className:"size-4 cursor-move text-muted-foreground transition-colors hover:text-primary","aria-hidden":"true"})}),size:50},{accessorKey:"id",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.nodeId")}),cell:({row:t})=>{const l=t.getValue("id"),n=t.original.code;return e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsxs("div",{className:"group/id flex items-center space-x-2",children:[e.jsxs(B,{variant:"outline",className:y("border-2 font-medium transition-all duration-200 hover:opacity-80","flex items-center gap-1.5"),style:{borderColor:Ge[t.original.type]},children:[e.jsx(Ql,{className:"size-3"}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"flex items-center gap-0.5",children:n??l}),t.original.parent?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-sm text-muted-foreground/30",children:"→"}),e.jsx("span",{children:t.original.parent?.code||t.original.parent?.id})]}):""]})]}),e.jsx(E,{variant:"ghost",size:"icon",className:"size-5 text-muted-foreground/40 opacity-0 transition-all duration-200 hover:text-muted-foreground group-hover/id:opacity-100",onClick:o=>{o.stopPropagation(),ha(n||l.toString()).then(()=>{A.success(a("common:copy.success"))})},children:e.jsx(Hn,{className:"size-3"})})]})}),e.jsxs(de,{side:"top",className:"flex flex-col gap-1 p-3",children:[e.jsxs("p",{className:"font-medium",children:[cs.find(o=>o.type===t.original.type)?.label,t.original.parent?" (子节点)":""]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:n?"点击括号内容或复制按钮可复制节点代码":"点击复制按钮可复制节点ID"})]})]})})},size:50,enableSorting:!0},{accessorKey:"show",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.show")}),cell:({row:t})=>{const[l,n]=m.useState(!!t.getValue("show"));return e.jsx(ee,{checked:l,onCheckedChange:async o=>{n(o),Js.update({id:t.original.id,type:t.original.type,show:o?1:0}).catch(()=>{n(!o),s()})},style:{backgroundColor:l?Ge[t.original.type]:void 0}})},size:50,enableSorting:!1},{accessorKey:"name",header:({column:t})=>e.jsx("div",{className:"flex items-center",children:e.jsx(z,{column:t,title:a("columns.node"),tooltip:e.jsxs("div",{className:"grid grid-cols-1 gap-3 p-2",children:[e.jsxs("div",{className:"flex items-center space-x-2.5",children:[e.jsx("span",{className:y("h-2.5 w-2.5 rounded-full",ta[0])}),e.jsx("span",{className:"text-sm font-medium",children:a("columns.status.0")})]}),e.jsxs("div",{className:"flex items-center space-x-2.5",children:[e.jsx("span",{className:y("h-2.5 w-2.5 rounded-full",ta[1])}),e.jsx("span",{className:"text-sm font-medium",children:a("columns.status.1")})]}),e.jsxs("div",{className:"flex items-center space-x-2.5",children:[e.jsx("span",{className:y("h-2.5 w-2.5 rounded-full",ta[2])}),e.jsx("span",{className:"text-sm font-medium",children:a("columns.status.2")})]})]})})}),cell:({row:t})=>e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{children:e.jsxs("div",{className:"flex items-center space-x-2.5",children:[e.jsx("span",{className:y("size-2.5 flex-shrink-0 rounded-full transition-all duration-200",ta[t.original.available_status])}),e.jsx("span",{className:"text-left font-medium transition-colors hover:text-primary",children:t.getValue("name")})]})}),e.jsx(de,{children:e.jsxs("div",{className:" space-y-3",children:[e.jsx("p",{className:"font-medium",children:a(`columns.status.${t.original.available_status}`)}),t.original.load_status&&e.jsxs("div",{className:"border-t border-border/50 pt-3",children:[e.jsx("p",{className:"mb-3 text-sm font-medium",children:a("columns.loadStatus.details")}),e.jsxs("div",{className:"space-y-3 text-xs",children:[e.jsx("div",{children:e.jsxs("div",{className:"mb-1.5 flex items-center justify-between",children:[e.jsxs("span",{className:"font-medium",children:[a("columns.loadStatus.cpu"),":"]}),e.jsxs("div",{className:"ml-2 flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-20 overflow-hidden rounded-full bg-muted",children:e.jsx("div",{className:y("h-full transition-all duration-300",t.original.load_status.cpu>=90?"bg-destructive":t.original.load_status.cpu>=70?"bg-yellow-500":"bg-emerald-500"),style:{width:`${Math.min(100,t.original.load_status.cpu)}%`}})}),e.jsxs("span",{className:y("min-w-[3rem] text-right font-semibold",t.original.load_status.cpu>=90?"text-destructive":t.original.load_status.cpu>=70?"text-yellow-600":"text-emerald-600"),children:[Math.round(t.original.load_status.cpu),"%"]})]})]})}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-1.5 flex items-center justify-between",children:[e.jsxs("span",{className:"font-medium",children:[a("columns.loadStatus.memory"),":"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-20 overflow-hidden rounded-full bg-muted",children:e.jsx("div",{className:y("h-full transition-all duration-300",Pe(t.original.load_status.mem.used,t.original.load_status.mem.total)>=90?"bg-destructive":Pe(t.original.load_status.mem.used,t.original.load_status.mem.total)>=70?"bg-yellow-500":"bg-emerald-500"),style:{width:`${Pe(t.original.load_status.mem.used,t.original.load_status.mem.total)}%`}})}),e.jsxs("span",{className:y("min-w-[3rem] text-right font-semibold",Pe(t.original.load_status.mem.used,t.original.load_status.mem.total)>=90?"text-destructive":Pe(t.original.load_status.mem.used,t.original.load_status.mem.total)>=70?"text-yellow-600":"text-emerald-600"),children:[Pe(t.original.load_status.mem.used,t.original.load_status.mem.total),"%"]})]})]}),e.jsxs("div",{className:"ml-auto w-[9.5rem] text-right text-xs text-muted-foreground",children:[Oe(t.original.load_status.mem.used)," ","/"," ",Oe(t.original.load_status.mem.total)]})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-1.5 flex items-center justify-between",children:[e.jsxs("span",{className:"font-medium",children:[a("columns.loadStatus.swap"),":"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-20 overflow-hidden rounded-full bg-muted",children:e.jsx("div",{className:y("h-full transition-all duration-300",Pe(t.original.load_status.swap.used,t.original.load_status.swap.total)>=80?"bg-destructive":Pe(t.original.load_status.swap.used,t.original.load_status.swap.total)>=50?"bg-yellow-500":"bg-emerald-500"),style:{width:`${Pe(t.original.load_status.swap.used,t.original.load_status.swap.total)}%`}})}),e.jsxs("span",{className:y("min-w-[3rem] text-right font-semibold",Pe(t.original.load_status.swap.used,t.original.load_status.swap.total)>=80?"text-destructive":Pe(t.original.load_status.swap.used,t.original.load_status.swap.total)>=50?"text-yellow-600":"text-emerald-600"),children:[Pe(t.original.load_status.swap.used,t.original.load_status.swap.total),"%"]})]})]}),e.jsxs("div",{className:"ml-auto w-[9.5rem] text-right text-xs text-muted-foreground",children:[Oe(t.original.load_status.swap.used)," ","/"," ",Oe(t.original.load_status.swap.total)]})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-1.5 flex items-center justify-between",children:[e.jsxs("span",{className:"font-medium",children:[a("columns.loadStatus.disk"),":"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-20 overflow-hidden rounded-full bg-muted",children:e.jsx("div",{className:y("h-full transition-all duration-300",Pe(t.original.load_status.disk.used,t.original.load_status.disk.total)>=90?"bg-destructive":Pe(t.original.load_status.disk.used,t.original.load_status.disk.total)>=70?"bg-yellow-500":"bg-emerald-500"),style:{width:`${Pe(t.original.load_status.disk.used,t.original.load_status.disk.total)}%`}})}),e.jsxs("span",{className:y("min-w-[3rem] text-right font-semibold",Pe(t.original.load_status.disk.used,t.original.load_status.disk.total)>=90?"text-destructive":Pe(t.original.load_status.disk.used,t.original.load_status.disk.total)>=70?"text-yellow-600":"text-emerald-600"),children:[Pe(t.original.load_status.disk.used,t.original.load_status.disk.total),"%"]})]})]}),e.jsxs("div",{className:"ml-auto w-[9.5rem] text-right text-xs text-muted-foreground",children:[Oe(t.original.load_status.disk.used)," ","/"," ",Oe(t.original.load_status.disk.total)]})]})]})]})]})})]})}),enableSorting:!1,size:200},{accessorKey:"host",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.address")}),cell:({row:t})=>{const l=`${t.original.host}:${t.original.port}`,n=t.original.port!==t.original.server_port;return e.jsxs("div",{className:"group relative flex min-w-0 items-start",children:[e.jsxs("div",{className:"flex min-w-0 flex-wrap items-baseline gap-x-1 gap-y-0.5 pr-7",children:[e.jsx("div",{className:"flex items-center ",children:e.jsxs("span",{className:"font-mono text-sm font-medium text-foreground/90",children:[t.original.host,":",t.original.port]})}),n&&e.jsxs("span",{className:"whitespace-nowrap text-[0.7rem] tracking-tight text-muted-foreground/40",children:["(",a("columns.internalPort")," ",t.original.server_port,")"]})]}),e.jsx("div",{className:"absolute right-0 top-0",children:e.jsx(be,{delayDuration:0,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(E,{variant:"ghost",size:"icon",className:"size-6 text-muted-foreground/40 opacity-0 transition-all duration-200 hover:bg-muted/50 hover:text-muted-foreground group-hover:opacity-100",onClick:o=>{o.stopPropagation(),ha(l).then(()=>{A.success(a("common:copy.success"))})},children:e.jsx(Hn,{className:"size-3"})})}),e.jsx(de,{side:"top",sideOffset:10,children:a("columns.copyAddress")})]})})})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"online",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.onlineUsers.title"),tooltip:a("columns.onlineUsers.tooltip")}),cell:({row:t})=>e.jsxs("div",{className:"flex items-center space-x-2 px-4",children:[e.jsx(Ut,{className:"size-4"}),e.jsx("span",{className:"font-medium",children:t.getValue("online")})]}),size:80,enableSorting:!0,enableHiding:!0},{accessorKey:"rate",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.rate.title"),tooltip:a("columns.rate.tooltip")}),cell:({row:t})=>e.jsxs(B,{variant:"secondary",className:"font-medium",children:[t.getValue("rate")," x"]}),size:80,enableSorting:!1,enableHiding:!0},{accessorKey:"group_ids",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.groups.title"),tooltip:a("columns.groups.tooltip")}),cell:({row:t})=>{const l=t.original.groups||[];return e.jsxs("div",{className:"flex flex-wrap gap-1.5",children:[l.map((n,o)=>e.jsx(B,{variant:"secondary",className:y("px-2 py-0.5 font-medium","bg-secondary/50 hover:bg-secondary/70","border border-border/50","transition-all duration-200","cursor-default select-none","flex items-center gap-1.5"),children:n.name},o)),l.length===0&&e.jsx("span",{className:"text-sm text-muted-foreground",children:a("columns.groups.empty")})]})},enableSorting:!1,filterFn:(t,l,n)=>{const o=t.getValue(l);return o?n.some(r=>o.includes(r)):!1}},{accessorKey:"type",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.type")}),cell:({row:t})=>{const l=t.getValue("type");return e.jsx(B,{variant:"outline",className:"border-2 font-medium transition-colors",style:{borderColor:Ge[l]},children:l})},enableSorting:!1,enableHiding:!0,enableColumnFilter:!1,size:8e3},{id:"actions",header:({column:t})=>e.jsx(z,{className:"justify-end",column:t,title:a("columns.actions")}),cell:({row:t})=>{const{setIsOpen:l,setEditingServer:n,setServerType:o}=Vr();return e.jsx("div",{className:"flex justify-center",children:e.jsxs(Es,{modal:!1,children:[e.jsx(Rs,{asChild:!0,children:e.jsx(E,{variant:"ghost",className:"h-8 w-8 p-0 hover:bg-muted","aria-label":a("columns.actions"),children:e.jsx(ua,{className:"size-4"})})}),e.jsxs(Cs,{align:"end",className:"w-40",children:[e.jsx(Ne,{className:"cursor-pointer",onClick:()=>{o(t.original.type),n(t.original),l(!0)},children:e.jsxs("div",{className:"flex w-full items-center",children:[e.jsx(Nc,{className:"mr-2 size-4"}),a("columns.actions_dropdown.edit")]})}),e.jsxs(Ne,{className:"cursor-pointer",onClick:async()=>{Js.copy({id:t.original.id}).then(({data:r})=>{r&&(A.success(a("columns.actions_dropdown.copy_success")),s())})},children:[e.jsx(_c,{className:"mr-2 size-4"}),a("columns.actions_dropdown.copy")]}),e.jsx(yt,{}),e.jsx(Ne,{className:"cursor-pointer text-destructive focus:text-destructive",onSelect:r=>r.preventDefault(),children:e.jsx(ns,{title:a("columns.actions_dropdown.delete.title"),description:a("columns.actions_dropdown.delete.description"),confirmText:a("columns.actions_dropdown.delete.confirm"),variant:"destructive",onConfirm:async()=>{Js.drop({id:t.original.id}).then(({data:r})=>{r&&(A.success(a("columns.actions_dropdown.delete_success")),s())})},children:e.jsxs("div",{className:"flex w-full items-center",children:[e.jsx(fs,{className:"mr-2 size-4"}),a("columns.actions_dropdown.delete.confirm")]})})})]})]})})},size:50}]};function Ju(){const[s,a]=m.useState({}),[t,l]=m.useState({"drag-handle":!1}),[n,o]=m.useState([]),[r,c]=m.useState({pageSize:500,pageIndex:0}),[u,i]=m.useState([]),[d,h]=m.useState(!1),[_,T]=m.useState({}),[S,C]=m.useState([]),{refetch:N}=ne({queryKey:["nodeList"],queryFn:async()=>{const{data:I}=await Js.getList();return C(I),I}}),{data:g}=ne({queryKey:["groups"],queryFn:async()=>{const{data:I}=await at.getList();return I}});m.useEffect(()=>{l({"drag-handle":d,show:!d,host:!d,online:!d,rate:!d,groups:!d,type:!1,actions:!d}),T({name:d?2e3:200}),c({pageSize:d?99999:500,pageIndex:0})},[d]);const k=(I,H)=>{d&&(I.dataTransfer.setData("text/plain",H.toString()),I.currentTarget.classList.add("opacity-50"))},R=(I,H)=>{if(!d)return;I.preventDefault(),I.currentTarget.classList.remove("bg-muted");const O=parseInt(I.dataTransfer.getData("text/plain"));if(O===H)return;const K=[...S],[oe]=K.splice(O,1);K.splice(H,0,oe),C(K)},p=async()=>{if(!d){h(!0);return}const I=S?.map((H,O)=>({id:H.id,order:O+1}));Js.sort(I).then(()=>{A.success("排序保存成功"),h(!1),N()}).finally(()=>{h(!1)})},w=Je({data:S||[],columns:Yu(N),state:{sorting:u,columnVisibility:t,rowSelection:s,columnFilters:n,columnSizing:_,pagination:r},enableRowSelection:!0,onRowSelectionChange:a,onSortingChange:i,onColumnFiltersChange:o,onColumnVisibilityChange:l,onColumnSizingChange:T,onPaginationChange:c,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),getSortedRowModel:vs(),getFacetedRowModel:Ls(),getFacetedUniqueValues:Vs(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(ku,{refetch:N,children:e.jsx("div",{className:"space-y-4",children:e.jsx(is,{table:w,toolbar:I=>e.jsx(Wu,{table:I,refetch:N,saveOrder:p,isSortMode:d,groups:g||[]}),draggable:d,onDragStart:k,onDragEnd:I=>I.currentTarget.classList.remove("opacity-50"),onDragOver:I=>{I.preventDefault(),I.currentTarget.classList.add("bg-muted")},onDragLeave:I=>I.currentTarget.classList.remove("bg-muted"),onDrop:R,showPagination:!d})})})}function Qu(){const{t:s}=V("server");return e.jsxs(Ve,{children:[e.jsxs(Fe,{children:[e.jsx(Xe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("manage.title")}),e.jsx("p",{className:"text-muted-foreground mt-2",children:s("manage.description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(Ju,{})})]})]})}const Xu=Object.freeze(Object.defineProperty({__proto__:null,default:Qu},Symbol.toStringTag,{value:"Module"}));function Zu({table:s,refetch:a}){const t=s.getState().columnFilters.length>0,{t:l}=V("group");return e.jsx("div",{className:"flex items-center justify-between space-x-4",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsx(Va,{refetch:a}),e.jsx(D,{placeholder:l("toolbar.searchPlaceholder"),value:s.getColumn("name")?.getFilterValue()??"",onChange:n=>s.getColumn("name")?.setFilterValue(n.target.value),className:y("h-8  w-[150px] lg:w-[250px]",t&&"border-primary/50 ring-primary/20")}),t&&e.jsxs(E,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:[l("toolbar.reset"),e.jsx(ds,{className:"ml-2 h-4 w-4"})]})]})})}const ex=s=>{const{t:a}=V("group");return[{accessorKey:"id",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.id")}),cell:({row:t})=>e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(B,{variant:"outline",children:t.getValue("id")})}),enableSorting:!0},{accessorKey:"name",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.name")}),cell:({row:t})=>e.jsx("div",{className:"flex space-x-2",children:e.jsx("span",{className:"max-w-32 truncate font-medium",children:t.getValue("name")})})},{accessorKey:"users_count",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.usersCount")}),cell:({row:t})=>e.jsxs("div",{className:"flex items-center space-x-2 px-4",children:[e.jsx(Ut,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:t.getValue("users_count")})]}),enableSorting:!0},{accessorKey:"server_count",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.serverCount")}),cell:({row:t})=>e.jsxs("div",{className:"flex items-center space-x-2 px-4",children:[e.jsx(Ql,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:t.getValue("server_count")})]}),enableSorting:!0,size:8e3},{id:"actions",header:({column:t})=>e.jsx(z,{className:"justify-end",column:t,title:a("columns.actions")}),cell:({row:t})=>e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx(Va,{defaultValues:t.original,refetch:s,type:"edit",dialogTrigger:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:[e.jsx(tt,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:a("form.edit")})]})}),e.jsx(ns,{title:a("messages.deleteConfirm"),description:a("messages.deleteDescription"),confirmText:a("messages.deleteButton"),variant:"destructive",onConfirm:async()=>{at.drop({id:t.original.id}).then(({data:l})=>{l&&(A.success(a("messages.updateSuccess")),s())})},children:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(fs,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:a("messages.deleteButton")})]})})]})}]};function sx(){const[s,a]=m.useState({}),[t,l]=m.useState({}),[n,o]=m.useState([]),[r,c]=m.useState([]),{data:u,refetch:i,isLoading:d}=ne({queryKey:["serverGroupList"],queryFn:async()=>{const{data:_}=await at.getList();return _}}),h=Je({data:u||[],columns:ex(i),state:{sorting:r,columnVisibility:t,rowSelection:s,columnFilters:n},enableRowSelection:!0,onRowSelectionChange:a,onSortingChange:c,onColumnFiltersChange:o,onColumnVisibilityChange:l,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),getSortedRowModel:vs(),getFacetedRowModel:Ls(),getFacetedUniqueValues:Vs(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(is,{table:h,toolbar:_=>e.jsx(Zu,{table:_,refetch:i}),isLoading:d})}function tx(){const{t:s}=V("group");return e.jsxs(Ve,{children:[e.jsxs(Fe,{children:[e.jsx(Xe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(sx,{})})]})]})}const ax=Object.freeze(Object.defineProperty({__proto__:null,default:tx},Symbol.toStringTag,{value:"Module"})),nx=s=>x.object({remarks:x.string().min(1,s("form.validation.remarks")),match:x.array(x.string()),action:x.enum(["block","dns"]),action_value:x.string().optional()});function Fr({refetch:s,dialogTrigger:a,defaultValues:t={remarks:"",match:[],action:"block",action_value:""},type:l="add"}){const{t:n}=V("route"),o=ye({resolver:_e(nx(n)),defaultValues:t,mode:"onChange"}),[r,c]=m.useState(!1);return e.jsxs(pe,{open:r,onOpenChange:c,children:[e.jsx(rs,{asChild:!0,children:a||e.jsxs(E,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(ze,{icon:"ion:add"})," ",e.jsx("div",{children:n("form.add")})]})}),e.jsxs(ue,{className:"sm:max-w-[425px]",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:n(l==="edit"?"form.edit":"form.create")}),e.jsx(Le,{})]}),e.jsxs(we,{...o,children:[e.jsx(v,{control:o.control,name:"remarks",render:({field:u})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:n("form.remarks")}),e.jsx("div",{className:"relative",children:e.jsx(b,{children:e.jsx(D,{type:"text",placeholder:n("form.remarksPlaceholder"),...u})})}),e.jsx(P,{})]})}),e.jsx(v,{control:o.control,name:"match",render:({field:u})=>e.jsxs(f,{className:"flex-[2]",children:[e.jsx(j,{children:n("form.match")}),e.jsx("div",{className:"relative",children:e.jsx(b,{children:e.jsx(Ts,{className:"min-h-[120px]",placeholder:n("form.matchPlaceholder"),value:u.value.join(`
`),onChange:i=>{u.onChange(i.target.value.split(`
`))}})})}),e.jsx(P,{})]})}),e.jsx(v,{control:o.control,name:"action",render:({field:u})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.action")}),e.jsx("div",{className:"relative",children:e.jsx(b,{children:e.jsxs(X,{onValueChange:u.onChange,defaultValue:u.value,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:n("form.actionPlaceholder")})}),e.jsxs(J,{children:[e.jsx($,{value:"block",children:n("actions.block")}),e.jsx($,{value:"dns",children:n("actions.dns")})]})]})})}),e.jsx(P,{})]})}),o.watch("action")==="dns"&&e.jsx(v,{control:o.control,name:"action_value",render:({field:u})=>e.jsxs(f,{children:[e.jsx(j,{children:n("form.dns")}),e.jsx("div",{className:"relative",children:e.jsx(b,{children:e.jsx(D,{type:"text",placeholder:n("form.dnsPlaceholder"),...u})})})]})}),e.jsxs(Re,{children:[e.jsx(qs,{asChild:!0,children:e.jsx(E,{variant:"outline",children:n("form.cancel")})}),e.jsx(E,{type:"submit",onClick:()=>{Sa.getList(o.getValues()).then(({data:u})=>{u&&(c(!1),s&&s(),A.success(n(l==="edit"?"messages.updateSuccess":"messages.createSuccess")),o.reset())})},children:n("form.submit")})]})]})]})]})}function lx({table:s,refetch:a}){const t=s.getState().columnFilters.length>0,{t:l}=V("route");return e.jsx("div",{className:"flex items-center justify-between ",children:e.jsxs("div",{className:"flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2",children:[e.jsx(Fr,{refetch:a}),e.jsx(D,{placeholder:l("toolbar.searchPlaceholder"),value:s.getColumn("remarks")?.getFilterValue()??"",onChange:n=>s.getColumn("remarks")?.setFilterValue(n.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),t&&e.jsxs(E,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:[l("toolbar.reset"),e.jsx(ds,{className:"ml-2 h-4 w-4"})]})]})})}function rx({columns:s,data:a,refetch:t}){const[l,n]=m.useState({}),[o,r]=m.useState({}),[c,u]=m.useState([]),[i,d]=m.useState([]),h=Je({data:a,columns:s,state:{sorting:i,columnVisibility:o,rowSelection:l,columnFilters:c},enableRowSelection:!0,onRowSelectionChange:n,onSortingChange:d,onColumnFiltersChange:u,onColumnVisibilityChange:r,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),getSortedRowModel:vs(),getFacetedRowModel:Ls(),getFacetedUniqueValues:Vs(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(is,{table:h,toolbar:_=>e.jsx(lx,{table:_,refetch:t})})}const ix=s=>{const{t:a}=V("route"),t={block:{icon:wc,variant:"destructive",className:"bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800"},dns:{icon:Cc,variant:"secondary",className:"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-800"}};return[{accessorKey:"id",header:({column:l})=>e.jsx(z,{column:l,title:a("columns.id")}),cell:({row:l})=>e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(B,{variant:"outline",children:l.getValue("id")})}),enableSorting:!0,enableHiding:!1},{accessorKey:"remarks",header:({column:l})=>e.jsx(z,{column:l,title:a("columns.remarks")}),cell:({row:l})=>e.jsx("div",{className:"flex space-x-2",children:e.jsx("span",{className:"max-w-32 truncate font-medium sm:max-w-72 md:max-w-[31rem]",children:l.original.remarks})}),enableHiding:!1,enableSorting:!1},{accessorKey:"action_value",header:({column:l})=>e.jsx(z,{column:l,title:a("columns.action_value.title")}),cell:({row:l})=>{const n=l.original.action,o=l.original.action_value,r=l.original.match?.length||0;return e.jsxs("div",{className:"flex flex-col space-y-1",children:[e.jsx("span",{className:"text-sm font-medium",children:n==="dns"&&o?a("columns.action_value.dns",{value:o}):n==="block"?e.jsx("span",{className:"text-destructive",children:a("columns.action_value.block")}):a("columns.action_value.direct")}),e.jsx("span",{className:"text-xs text-muted-foreground",children:a("columns.matchRules",{count:r})})]})},enableHiding:!1,enableSorting:!1,size:300},{accessorKey:"action",header:({column:l})=>e.jsx(z,{column:l,title:a("columns.action")}),cell:({row:l})=>{const n=l.getValue("action"),o=t[n]?.icon;return e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs(B,{variant:t[n]?.variant||"default",className:y("flex items-center gap-1.5 px-3 py-1 capitalize",t[n]?.className),children:[o&&e.jsx(o,{className:"h-3.5 w-3.5"}),a(`actions.${n}`)]})})},enableSorting:!1,size:9e3},{id:"actions",header:()=>e.jsx("div",{className:"text-right",children:a("columns.actions")}),cell:({row:l})=>e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx(Fr,{defaultValues:l.original,refetch:s,type:"edit",dialogTrigger:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:[e.jsx(tt,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:a("form.edit")})]})}),e.jsx(ns,{title:a("messages.deleteConfirm"),description:a("messages.deleteDescription"),confirmText:a("messages.deleteButton"),variant:"destructive",onConfirm:async()=>{Sa.drop({id:l.original.id}).then(({data:n})=>{n&&(A.success(a("messages.deleteSuccess")),s())})},children:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(fs,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:a("messages.deleteButton")})]})})]})}]};function ox(){const{t:s}=V("route"),[a,t]=m.useState([]);function l(){Sa.getList().then(({data:n})=>{t(n)})}return m.useEffect(()=>{l()},[]),e.jsxs(Ve,{children:[e.jsxs(Fe,{children:[e.jsx(Xe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(rx,{data:a,columns:ix(l),refetch:l})})]})]})}const cx=Object.freeze(Object.defineProperty({__proto__:null,default:ox},Symbol.toStringTag,{value:"Module"})),Mr=m.createContext(void 0);function dx({children:s,refreshData:a}){const[t,l]=m.useState(!1),[n,o]=m.useState(null);return e.jsx(Mr.Provider,{value:{isOpen:t,setIsOpen:l,editingPlan:n,setEditingPlan:o,refreshData:a},children:s})}function kn(){const s=m.useContext(Mr);if(s===void 0)throw new Error("usePlanEdit must be used within a PlanEditProvider");return s}function mx({table:s,saveOrder:a,isSortMode:t}){const{setIsOpen:l}=kn(),{t:n}=V("subscribe");return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsxs(E,{variant:"outline",className:"space-x-2",size:"sm",onClick:()=>l(!0),children:[e.jsx(ze,{icon:"ion:add"}),e.jsx("div",{children:n("plan.add")})]}),e.jsx(D,{placeholder:n("plan.search"),value:s.getColumn("name")?.getFilterValue()??"",onChange:o=>s.getColumn("name")?.setFilterValue(o.target.value),className:"h-8 w-[150px] lg:w-[250px]"})]}),s.getRowCount()>0&&e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(E,{variant:t?"default":"outline",onClick:a,size:"sm",children:n(t?"plan.sort.save":"plan.sort.edit")})})]})}const Xn={monthly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},quarterly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},half_yearly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},yearly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},two_yearly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},three_yearly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},onetime:{color:"text-slate-700",bgColor:"bg-slate-100/80"},reset_traffic:{color:"text-slate-700",bgColor:"bg-slate-100/80"}},ux=s=>{const{t:a}=V("subscribe");return[{id:"drag-handle",header:()=>null,cell:()=>e.jsx("div",{className:"cursor-move",children:e.jsx(Na,{className:"size-4"})}),size:40,enableSorting:!1},{accessorKey:"id",header:({column:t})=>e.jsx(z,{column:t,title:a("plan.columns.id")}),cell:({row:t})=>e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(B,{variant:"outline",children:t.getValue("id")})}),enableSorting:!0,enableHiding:!1},{accessorKey:"show",header:({column:t})=>e.jsx(z,{column:t,title:a("plan.columns.show")}),cell:({row:t})=>e.jsx(ee,{defaultChecked:t.getValue("show"),onCheckedChange:l=>{es.update({id:t.original.id,show:l}).then(({data:n})=>{!n&&s()})}}),enableSorting:!1,enableHiding:!1},{accessorKey:"sell",header:({column:t})=>e.jsx(z,{column:t,title:a("plan.columns.sell")}),cell:({row:t})=>e.jsx(ee,{defaultChecked:t.getValue("sell"),onCheckedChange:l=>{es.update({id:t.original.id,sell:l}).then(({data:n})=>{!n&&s()})}}),enableSorting:!1,enableHiding:!1},{accessorKey:"renew",header:({column:t})=>e.jsx(z,{column:t,title:a("plan.columns.renew"),tooltip:a("plan.columns.renew_tooltip")}),cell:({row:t})=>e.jsx(ee,{defaultChecked:t.getValue("renew"),onCheckedChange:l=>{es.update({id:t.original.id,renew:l}).then(({data:n})=>{!n&&s()})}}),enableSorting:!1,enableHiding:!1},{accessorKey:"name",header:({column:t})=>e.jsx(z,{column:t,title:a("plan.columns.name")}),cell:({row:t})=>e.jsx("div",{className:"flex space-x-2",children:e.jsx("span",{className:"max-w-32 truncate font-medium sm:max-w-72 md:max-w-[31rem]",children:t.getValue("name")})}),enableSorting:!1,enableHiding:!1,size:900},{accessorKey:"users_count",header:({column:t})=>e.jsx(z,{column:t,title:a("plan.columns.stats")}),cell:({row:t})=>e.jsxs("div",{className:"flex items-center space-x-2 px-2",children:[e.jsx(Ut,{}),e.jsx("span",{className:"max-w-32 truncate font-medium sm:max-w-72 md:max-w-[31rem]",children:t.getValue("users_count")})]}),enableSorting:!0},{accessorKey:"group",header:({column:t})=>e.jsx(z,{column:t,title:a("plan.columns.group")}),cell:({row:t})=>e.jsx("div",{className:"flex max-w-[600px] flex-wrap items-center gap-1.5 text-nowrap",children:e.jsx(B,{variant:"secondary",className:y("px-2 py-0.5 font-medium","bg-secondary/50 hover:bg-secondary/70","border border-border/50","transition-all duration-200","cursor-default select-none","flex items-center gap-1.5"),children:t.getValue("group")?.name})}),enableSorting:!1,enableHiding:!1},{accessorKey:"prices",header:({column:t})=>e.jsx(z,{column:t,title:a("plan.columns.price")}),cell:({row:t})=>{const l=t.getValue("prices"),n=[{period:a("plan.columns.price_period.monthly"),key:"monthly",unit:a("plan.columns.price_period.unit.month")},{period:a("plan.columns.price_period.quarterly"),key:"quarterly",unit:a("plan.columns.price_period.unit.quarter")},{period:a("plan.columns.price_period.half_yearly"),key:"half_yearly",unit:a("plan.columns.price_period.unit.half_year")},{period:a("plan.columns.price_period.yearly"),key:"yearly",unit:a("plan.columns.price_period.unit.year")},{period:a("plan.columns.price_period.two_yearly"),key:"two_yearly",unit:a("plan.columns.price_period.unit.two_year")},{period:a("plan.columns.price_period.three_yearly"),key:"three_yearly",unit:a("plan.columns.price_period.unit.three_year")},{period:a("plan.columns.price_period.onetime"),key:"onetime",unit:""},{period:a("plan.columns.price_period.reset_traffic"),key:"reset_traffic",unit:a("plan.columns.price_period.unit.times")}];return e.jsx("div",{className:"flex flex-wrap items-center gap-2",children:n.map(({period:o,key:r,unit:c})=>l[r]!=null&&e.jsxs(B,{variant:"secondary",className:y("px-2 py-0.5 font-medium transition-colors text-nowrap",Xn[r].color,Xn[r].bgColor,"border border-border/50","hover:bg-slate-200/80"),children:[o," ¥",l[r],c]},r))})},enableSorting:!1,size:9e3},{id:"actions",header:({column:t})=>e.jsx(z,{className:"justify-end",column:t,title:a("plan.columns.actions")}),cell:({row:t})=>{const{setIsOpen:l,setEditingPlan:n}=kn();return e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",onClick:()=>{n(t.original),l(!0)},children:[e.jsx(tt,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:a("plan.columns.edit")})]}),e.jsx(ns,{title:a("plan.columns.delete_confirm.title"),description:a("plan.columns.delete_confirm.description"),confirmText:a("plan.columns.delete"),variant:"destructive",onConfirm:async()=>{es.drop({id:t.original.id}).then(({data:o})=>{o&&(A.success(a("plan.columns.delete_confirm.success")),s())})},children:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(fs,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:a("plan.columns.delete")})]})})]})}}]},xx=x.object({id:x.number().nullable(),group_id:x.union([x.number(),x.string()]).nullable().optional(),name:x.string().min(1).max(250),content:x.string().nullable().optional(),transfer_enable:x.union([x.number().min(0),x.string().min(1)]),prices:x.object({monthly:x.union([x.number(),x.string()]).nullable().optional(),quarterly:x.union([x.number(),x.string()]).nullable().optional(),half_yearly:x.union([x.number(),x.string()]).nullable().optional(),yearly:x.union([x.number(),x.string()]).nullable().optional(),two_yearly:x.union([x.number(),x.string()]).nullable().optional(),three_yearly:x.union([x.number(),x.string()]).nullable().optional(),onetime:x.union([x.number(),x.string()]).nullable().optional(),reset_traffic:x.union([x.number(),x.string()]).nullable().optional()}).default({}),speed_limit:x.union([x.number(),x.string()]).nullable().optional(),capacity_limit:x.union([x.number(),x.string()]).nullable().optional(),device_limit:x.union([x.number(),x.string()]).nullable().optional(),force_update:x.boolean().optional(),reset_traffic_method:x.number().nullable(),users_count:x.number().optional()}),Tn=m.forwardRef(({className:s,...a},t)=>e.jsx(Xl,{ref:t,className:y("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",s),...a,children:e.jsx(Sc,{className:y("flex items-center justify-center text-current"),children:e.jsx(et,{className:"h-4 w-4"})})}));Tn.displayName=Xl.displayName;const aa={id:null,group_id:null,name:"",content:"",transfer_enable:"",prices:{monthly:"",quarterly:"",half_yearly:"",yearly:"",two_yearly:"",three_yearly:"",onetime:"",reset_traffic:""},speed_limit:"",capacity_limit:"",device_limit:"",force_update:!1,reset_traffic_method:null},na={monthly:{label:"月付",months:1,discount:1},quarterly:{label:"季付",months:3,discount:.95},half_yearly:{label:"半年付",months:6,discount:.9},yearly:{label:"年付",months:12,discount:.85},two_yearly:{label:"两年付",months:24,discount:.8},three_yearly:{label:"三年付",months:36,discount:.75},onetime:{label:"流量包",months:1,discount:1},reset_traffic:{label:"重置包",months:1,discount:1}},hx=[{value:null,label:"follow_system"},{value:0,label:"monthly_first"},{value:1,label:"monthly_reset"},{value:2,label:"no_reset"},{value:3,label:"yearly_first"},{value:4,label:"yearly_reset"}];function px(){const{isOpen:s,setIsOpen:a,editingPlan:t,setEditingPlan:l,refreshData:n}=kn(),[o,r]=m.useState(!1),{t:c}=V("subscribe"),u=ye({resolver:_e(xx),defaultValues:{...aa,...t||{}},mode:"onChange"});m.useEffect(()=>{t?u.reset({...aa,...t}):u.reset(aa)},[t,u]);const i=new fn({html:!0}),[d,h]=m.useState();async function _(){at.getList().then(({data:C})=>{h(C)})}m.useEffect(()=>{s&&_()},[s]);const T=C=>{if(isNaN(C))return;const N=Object.entries(na).reduce((g,[k,R])=>{const p=C*R.months*R.discount;return{...g,[k]:p.toFixed(2)}},{});u.setValue("prices",N,{shouldDirty:!0})},S=()=>{a(!1),l(null),u.reset(aa)};return e.jsx(pe,{open:s,onOpenChange:S,children:e.jsxs(ue,{children:[e.jsxs(ve,{children:[e.jsx(ge,{children:c(t?"plan.form.edit_title":"plan.form.add_title")}),e.jsx(Le,{})]}),e.jsxs(we,{...u,children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(v,{control:u.control,name:"name",render:({field:C})=>e.jsxs(f,{children:[e.jsx(j,{children:c("plan.form.name.label")}),e.jsx(b,{children:e.jsx(D,{placeholder:c("plan.form.name.placeholder"),...C})}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"group_id",render:({field:C})=>e.jsxs(f,{children:[e.jsxs(j,{className:"flex items-center justify-between",children:[c("plan.form.group.label"),e.jsx(Va,{dialogTrigger:e.jsx(E,{variant:"link",children:c("plan.form.group.add")}),refetch:_})]}),e.jsxs(X,{value:C.value?.toString()??"",onValueChange:N=>C.onChange(N?Number(N):null),children:[e.jsx(b,{children:e.jsx(Y,{children:e.jsx(Z,{placeholder:c("plan.form.group.placeholder")})})}),e.jsx(J,{children:d?.map(N=>e.jsx($,{value:N.id.toString(),children:N.name},N.id))})]}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"transfer_enable",render:({field:C})=>e.jsxs(f,{className:"flex-1",children:[e.jsx(j,{children:c("plan.form.transfer.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(b,{children:e.jsx(D,{type:"number",min:0,placeholder:c("plan.form.transfer.placeholder"),className:"rounded-r-none",...C})}),e.jsx("div",{className:"flex items-center rounded-r-md border border-l-0 border-input bg-muted px-3 text-sm text-muted-foreground",children:c("plan.form.transfer.unit")})]}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"speed_limit",render:({field:C})=>e.jsxs(f,{className:"flex-1",children:[e.jsx(j,{children:c("plan.form.speed.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(b,{children:e.jsx(D,{type:"number",min:0,placeholder:c("plan.form.speed.placeholder"),className:"rounded-r-none",...C,value:C.value??""})}),e.jsx("div",{className:"flex items-center rounded-r-md border border-l-0 border-input bg-muted px-3 text-sm text-muted-foreground",children:c("plan.form.speed.unit")})]}),e.jsx(P,{})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center",children:[e.jsx("div",{className:"flex-grow border-t border-gray-200 dark:border-gray-700"}),e.jsx("h3",{className:"mx-4 text-sm font-medium text-gray-500 dark:text-gray-400",children:c("plan.form.price.title")}),e.jsx("div",{className:"flex-grow border-t border-gray-200 dark:border-gray-700"})]}),e.jsxs("div",{className:"ml-4 flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2",children:e.jsx("span",{className:"text-sm font-medium text-gray-400",children:"¥"})}),e.jsx(D,{type:"number",placeholder:c("plan.form.price.base_price"),className:"h-7 w-32 border-0 bg-gray-50 pl-6 pr-2 text-sm shadow-none ring-1 ring-gray-200 transition-shadow focus-visible:ring-2 focus-visible:ring-primary dark:bg-gray-800/50 dark:ring-gray-700 dark:placeholder:text-gray-500",onChange:C=>{const N=parseFloat(C.target.value);T(N)}})]}),e.jsx(be,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(E,{variant:"outline",size:"sm",className:"h-7 text-xs",onClick:()=>{const C=Object.keys(na).reduce((N,g)=>({...N,[g]:""}),{});u.setValue("prices",C,{shouldDirty:!0})},children:c("plan.form.price.clear.button")})}),e.jsx(de,{side:"top",align:"end",children:e.jsx("p",{className:"text-xs",children:c("plan.form.price.clear.tooltip")})})]})})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-3 lg:grid-cols-3",children:Object.entries(na).filter(([C])=>!["onetime","reset_traffic"].includes(C)).map(([C,N])=>e.jsx("div",{className:"group relative rounded-md bg-card p-2 ring-1 ring-gray-200 transition-all hover:ring-primary dark:ring-gray-800",children:e.jsx(v,{control:u.control,name:`prices.${C}`,render:({field:g})=>e.jsxs(f,{children:[e.jsxs(j,{className:"text-xs font-medium text-muted-foreground",children:[c(`plan.columns.price_period.${C}`),e.jsxs("span",{className:"ml-1 text-[10px] text-gray-400",children:["(",N.months===1?c("plan.form.price.period.monthly"):c("plan.form.price.period.months",{count:N.months}),")"]})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2",children:e.jsx("span",{className:"text-sm font-medium text-gray-400",children:"¥"})}),e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:"0.00",min:0,...g,value:g.value??"",onChange:k=>g.onChange(k.target.value),className:"h-7 border-0 bg-gray-50 pl-6 pr-2 text-sm shadow-none ring-1 ring-gray-200 transition-shadow focus-visible:ring-2 focus-visible:ring-primary dark:bg-gray-800/50 dark:ring-gray-700 dark:placeholder:text-gray-500"})})]})]})})},C))}),e.jsx("div",{className:"grid grid-cols-1 gap-3 md:grid-cols-2",children:Object.entries(na).filter(([C])=>["onetime","reset_traffic"].includes(C)).map(([C,N])=>e.jsx("div",{className:"rounded-md border border-dashed border-gray-200 bg-muted/30 p-3 dark:border-gray-700",children:e.jsx(v,{control:u.control,name:`prices.${C}`,render:({field:g})=>e.jsx(f,{children:e.jsxs("div",{className:"flex flex-col gap-2 md:flex-row md:items-center md:justify-between",children:[e.jsxs("div",{className:"space-y-0",children:[e.jsx(j,{className:"text-xs font-medium",children:c(`plan.columns.price_period.${C}`)}),e.jsx("p",{className:"text-[10px] text-muted-foreground",children:c(C==="onetime"?"plan.form.price.onetime_desc":"plan.form.price.reset_desc")})]}),e.jsxs("div",{className:"relative w-full md:w-32",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2",children:e.jsx("span",{className:"text-sm font-medium text-gray-400",children:"¥"})}),e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:"0.00",min:0,...g,className:"h-7 border-0 bg-gray-50 pl-6 pr-2 text-sm shadow-none ring-1 ring-gray-200 transition-shadow focus-visible:ring-2 focus-visible:ring-primary dark:bg-gray-800/50 dark:ring-gray-700 dark:placeholder:text-gray-500"})})]})]})})})},C))})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(v,{control:u.control,name:"device_limit",render:({field:C})=>e.jsxs(f,{className:"flex-1",children:[e.jsx(j,{children:c("plan.form.device.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(b,{children:e.jsx(D,{type:"number",min:0,placeholder:c("plan.form.device.placeholder"),className:"rounded-r-none",...C,value:C.value??""})}),e.jsx("div",{className:"flex items-center rounded-r-md border border-l-0 border-input bg-muted px-3 text-sm text-muted-foreground",children:c("plan.form.device.unit")})]}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"capacity_limit",render:({field:C})=>e.jsxs(f,{className:"flex-1",children:[e.jsx(j,{children:c("plan.form.capacity.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(b,{children:e.jsx(D,{type:"number",min:0,placeholder:c("plan.form.capacity.placeholder"),className:"rounded-r-none",...C,value:C.value??""})}),e.jsx("div",{className:"flex items-center rounded-r-md border border-l-0 border-input bg-muted px-3 text-sm text-muted-foreground",children:c("plan.form.capacity.unit")})]}),e.jsx(P,{})]})})]}),e.jsx(v,{control:u.control,name:"reset_traffic_method",render:({field:C})=>e.jsxs(f,{children:[e.jsx(j,{children:c("plan.form.reset_method.label")}),e.jsxs(X,{value:C.value?.toString()??"null",onValueChange:N=>C.onChange(N=="null"?null:Number(N)),children:[e.jsx(b,{children:e.jsx(Y,{children:e.jsx(Z,{placeholder:c("plan.form.reset_method.placeholder")})})}),e.jsx(J,{children:hx.map(N=>e.jsx($,{value:N.value?.toString()??"null",children:c(`plan.form.reset_method.options.${N.label}`)},N.value))})]}),e.jsx(F,{className:"text-xs",children:c("plan.form.reset_method.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:u.control,name:"content",render:({field:C})=>{const[N,g]=m.useState(!1);return e.jsxs(f,{className:"space-y-2",children:[e.jsxs(j,{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[c("plan.form.content.label"),e.jsx(be,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(E,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:()=>g(!N),children:N?e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4",children:[e.jsx("path",{d:"M10 12.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5z"}),e.jsx("path",{fillRule:"evenodd",d:"M.664 10.59a1.651 1.651 0 010-1.186A10.004 10.004 0 0110 3c4.257 0 7.893 2.66 9.336 6.41.147.381.146.804 0 1.186A10.004 10.004 0 0110 17c-4.257 0-7.893-2.66-9.336-6.41zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]}):e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4",children:[e.jsx("path",{fillRule:"evenodd",d:"M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06l-1.745-1.745a10.029 10.029 0 003.3-4.38 1.651 1.651 0 000-1.185A10.004 10.004 0 009.999 3a9.956 9.956 0 00-4.744 1.194L3.28 2.22zM7.752 6.69l1.092 1.092a2.5 2.5 0 013.374 3.373l1.091 1.092a4 4 0 00-5.557-5.557z",clipRule:"evenodd"}),e.jsx("path",{d:"M10.748 13.93l2.523 2.523a9.987 9.987 0 01-3.27.547c-4.258 0-7.894-2.66-9.337-6.41a1.651 1.651 0 010-1.186A10.007 10.007 0 012.839 6.02L6.07 9.252a4 4 0 004.678 4.678z"})]})})}),e.jsx(de,{side:"top",children:e.jsx("p",{className:"text-xs",children:c(N?"plan.form.content.preview_button.hide":"plan.form.content.preview_button.show")})})]})})]}),e.jsx(be,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(E,{variant:"outline",size:"sm",onClick:()=>{C.onChange(c("plan.form.content.template.content"))},children:c("plan.form.content.template.button")})}),e.jsx(de,{side:"left",align:"center",children:e.jsx("p",{className:"text-xs",children:c("plan.form.content.template.tooltip")})})]})})]}),e.jsxs("div",{className:`grid gap-4 ${N?"grid-cols-1 lg:grid-cols-2":"grid-cols-1"}`,children:[e.jsx("div",{className:"space-y-2",children:e.jsx(b,{children:e.jsx(jn,{style:{height:"400px"},value:C.value||"",renderHTML:k=>i.render(k),onChange:({text:k})=>C.onChange(k),config:{view:{menu:!0,md:!0,html:!1},canView:{menu:!0,md:!0,html:!1,fullScreen:!1,hideMenu:!1}},placeholder:c("plan.form.content.placeholder"),className:"rounded-md border"})})}),N&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:c("plan.form.content.preview")}),e.jsx("div",{className:"prose prose-sm dark:prose-invert h-[400px] max-w-none overflow-y-auto rounded-md border p-4",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:i.render(C.value||"")}})})]})]}),e.jsx(F,{className:"text-xs",children:c("plan.form.content.description")}),e.jsx(P,{})]})}})]}),e.jsx(Re,{className:"mt-6",children:e.jsxs("div",{className:"flex w-full items-center justify-between",children:[e.jsx("div",{className:"flex-shrink-0",children:t&&e.jsx(v,{control:u.control,name:"force_update",render:({field:C})=>e.jsxs(f,{className:"flex flex-row items-center space-x-2 space-y-0",children:[e.jsx(b,{children:e.jsx(Tn,{checked:C.value,onCheckedChange:C.onChange})}),e.jsx("div",{className:"",children:e.jsx(j,{className:"text-sm",children:c("plan.form.force_update.label")})})]})})}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(E,{type:"button",variant:"outline",onClick:S,children:c("plan.form.submit.cancel")}),e.jsx(E,{type:"submit",disabled:o,onClick:()=>{u.handleSubmit(async C=>{r(!0),(await es.save(C)).data&&(A.success(c(t?"plan.form.submit.success.update":"plan.form.submit.success.add")),S(),n()),r(!1)})()},children:c(o?"plan.form.submit.submitting":"plan.form.submit.submit")})]})]})})]})]})})}function gx(){const[s,a]=m.useState({}),[t,l]=m.useState({"drag-handle":!1}),[n,o]=m.useState([]),[r,c]=m.useState([]),[u,i]=m.useState(!1),[d,h]=m.useState({pageSize:20,pageIndex:0}),[_,T]=m.useState([]),{refetch:S}=ne({queryKey:["planList"],queryFn:async()=>{const{data:R}=await es.getList();return T(R),R}});m.useEffect(()=>{l({"drag-handle":u}),h({pageSize:u?99999:10,pageIndex:0})},[u]);const C=(R,p)=>{u&&(R.dataTransfer.setData("text/plain",p.toString()),R.currentTarget.classList.add("opacity-50"))},N=(R,p)=>{if(!u)return;R.preventDefault(),R.currentTarget.classList.remove("bg-muted");const w=parseInt(R.dataTransfer.getData("text/plain"));if(w===p)return;const I=[..._],[H]=I.splice(w,1);I.splice(p,0,H),T(I)},g=async()=>{if(!u){i(!0);return}const R=_?.map(p=>p.id);es.sort(R).then(()=>{A.success("排序保存成功"),i(!1),S()}).finally(()=>{i(!1)})},k=Je({data:_||[],columns:ux(S),state:{sorting:r,columnVisibility:t,rowSelection:s,columnFilters:n,pagination:d},enableRowSelection:!0,onPaginationChange:h,onRowSelectionChange:a,onSortingChange:c,onColumnFiltersChange:o,onColumnVisibilityChange:l,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),getSortedRowModel:vs(),getFacetedRowModel:Ls(),getFacetedUniqueValues:Vs(),initialState:{columnPinning:{right:["actions"]}},pageCount:u?1:void 0});return e.jsx(dx,{refreshData:S,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(is,{table:k,toolbar:R=>e.jsx(mx,{table:R,refetch:S,saveOrder:g,isSortMode:u}),draggable:u,onDragStart:C,onDragEnd:R=>R.currentTarget.classList.remove("opacity-50"),onDragOver:R=>{R.preventDefault(),R.currentTarget.classList.add("bg-muted")},onDragLeave:R=>R.currentTarget.classList.remove("bg-muted"),onDrop:N,showPagination:!u}),e.jsx(px,{})]})})}function fx(){const{t:s}=V("subscribe");return e.jsxs(Ve,{children:[e.jsxs(Fe,{children:[e.jsx(Xe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("plan.title")}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:s("plan.page.description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(gx,{})})]})]})}const jx=Object.freeze(Object.defineProperty({__proto__:null,default:fx},Symbol.toStringTag,{value:"Module"})),ht=[{value:ae.PENDING,label:Pt[ae.PENDING],icon:kc,color:Et[ae.PENDING]},{value:ae.PROCESSING,label:Pt[ae.PROCESSING],icon:Zl,color:Et[ae.PROCESSING]},{value:ae.COMPLETED,label:Pt[ae.COMPLETED],icon:nn,color:Et[ae.COMPLETED]},{value:ae.CANCELLED,label:Pt[ae.CANCELLED],icon:er,color:Et[ae.CANCELLED]},{value:ae.DISCOUNTED,label:Pt[ae.DISCOUNTED],icon:nn,color:Et[ae.DISCOUNTED]}],Vt=[{value:je.PENDING,label:Zt[je.PENDING],icon:Tc,color:ea[je.PENDING]},{value:je.PROCESSING,label:Zt[je.PROCESSING],icon:Zl,color:ea[je.PROCESSING]},{value:je.VALID,label:Zt[je.VALID],icon:nn,color:ea[je.VALID]},{value:je.INVALID,label:Zt[je.INVALID],icon:er,color:ea[je.INVALID]}];function la({column:s,title:a,options:t}){const l=s?.getFacetedUniqueValues(),n=s?.getFilterValue(),o=Array.isArray(n)?new Set(n):n!==void 0?new Set([n]):new Set;return e.jsxs(Ss,{children:[e.jsx(ks,{asChild:!0,children:e.jsxs(E,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(_a,{className:"mr-2 h-4 w-4"}),a,o?.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(ke,{orientation:"vertical",className:"mx-2 h-4"}),e.jsx(B,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:o.size}),e.jsx("div",{className:"hidden space-x-1 lg:flex",children:o.size>2?e.jsxs(B,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[o.size," selected"]}):t.filter(r=>o.has(r.value)).map(r=>e.jsx(B,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:r.label},r.value))})]})]})}),e.jsx(bs,{className:"w-[200px] p-0",align:"start",children:e.jsxs(Us,{children:[e.jsx(nt,{placeholder:a}),e.jsxs(Ks,{children:[e.jsx(lt,{children:"No results found."}),e.jsx(as,{children:t.map(r=>{const c=o.has(r.value);return e.jsxs($e,{onSelect:()=>{const u=new Set(o);c?u.delete(r.value):u.add(r.value);const i=Array.from(u);s?.setFilterValue(i.length?i:void 0)},children:[e.jsx("div",{className:y("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",c?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(et,{className:y("h-4 w-4")})}),r.icon&&e.jsx(r.icon,{className:`mr-2 h-4 w-4 text-muted-foreground text-${r.color}`}),e.jsx("span",{children:r.label}),l?.get(r.value)&&e.jsx("span",{className:"ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs",children:l.get(r.value)})]},r.value)})}),o.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(St,{}),e.jsx(as,{children:e.jsx($e,{onSelect:()=>s?.setFilterValue(void 0),className:"justify-center text-center",children:"Clear filters"})})]})]})]})})]})}const vx=x.object({email:x.string().min(1),plan_id:x.number(),period:x.string(),total_amount:x.number()}),bx={email:"",plan_id:0,total_amount:0,period:""};function Or({refetch:s,trigger:a,defaultValues:t}){const{t:l}=V("order"),[n,o]=m.useState(!1),r=ye({resolver:_e(vx),defaultValues:{...bx,...t},mode:"onChange"}),[c,u]=m.useState([]);return m.useEffect(()=>{n&&es.getList().then(({data:i})=>{u(i)})},[n]),e.jsxs(pe,{open:n,onOpenChange:o,children:[e.jsx(rs,{asChild:!0,children:a||e.jsxs(E,{variant:"outline",size:"sm",className:"h-8 space-x-2",children:[e.jsx(ze,{icon:"ion:add"}),e.jsx("div",{children:l("dialog.addOrder")})]})}),e.jsxs(ue,{className:"sm:max-w-[425px]",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:l("dialog.assignOrder")}),e.jsx(Le,{})]}),e.jsxs(we,{...r,children:[e.jsx(v,{control:r.control,name:"email",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dialog.fields.userEmail")}),e.jsx(b,{children:e.jsx(D,{placeholder:l("dialog.placeholders.email"),...i})})]})}),e.jsx(v,{control:r.control,name:"plan_id",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dialog.fields.subscriptionPlan")}),e.jsx(b,{children:e.jsxs(X,{value:i.value?i.value?.toString():void 0,onValueChange:d=>i.onChange(parseInt(d)),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dialog.placeholders.plan")})}),e.jsx(J,{children:c.map(d=>e.jsx($,{value:d.id.toString(),children:d.name},d.id))})]})})]})}),e.jsx(v,{control:r.control,name:"period",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dialog.fields.orderPeriod")}),e.jsx(b,{children:e.jsxs(X,{value:i.value,onValueChange:i.onChange,children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:l("dialog.placeholders.period")})}),e.jsx(J,{children:Object.keys(Ud).map(d=>e.jsx($,{value:d,children:l(`period.${d}`)},d))})]})})]})}),e.jsx(v,{control:r.control,name:"total_amount",render:({field:i})=>e.jsxs(f,{children:[e.jsx(j,{children:l("dialog.fields.paymentAmount")}),e.jsx(b,{children:e.jsx(D,{type:"number",placeholder:l("dialog.placeholders.amount"),value:i.value/100,onChange:d=>i.onChange(parseFloat(d.currentTarget.value)*100)})}),e.jsx(P,{})]})}),e.jsxs(Re,{children:[e.jsx(E,{variant:"outline",onClick:()=>o(!1),children:l("dialog.actions.cancel")}),e.jsx(E,{type:"submit",onClick:()=>{r.handleSubmit(i=>{Ys.assign(i).then(({data:d})=>{d&&(s&&s(),r.reset(),o(!1),A.success(l("dialog.messages.addSuccess")))})})()},children:l("dialog.actions.confirm")})]})]})]})]})}function yx({table:s,refetch:a}){const{t}=V("order"),l=s.getState().columnFilters.length>0,n=Object.values(ps).filter(u=>typeof u=="number").map(u=>({label:t(`type.${ps[u]}`),value:u,color:u===ps.NEW?"green-500":u===ps.RENEWAL?"blue-500":u===ps.UPGRADE?"purple-500":"orange-500"})),o=Object.values(Ie).map(u=>({label:t(`period.${u}`),value:u,color:u===Ie.MONTH_PRICE?"slate-500":u===Ie.QUARTER_PRICE?"cyan-500":u===Ie.HALF_YEAR_PRICE?"indigo-500":u===Ie.YEAR_PRICE?"violet-500":u===Ie.TWO_YEAR_PRICE?"fuchsia-500":u===Ie.THREE_YEAR_PRICE?"pink-500":u===Ie.ONETIME_PRICE?"rose-500":"orange-500"})),r=Object.values(ae).filter(u=>typeof u=="number").map(u=>({label:t(`status.${ae[u]}`),value:u,icon:u===ae.PENDING?ht[0].icon:u===ae.PROCESSING?ht[1].icon:u===ae.COMPLETED?ht[2].icon:u===ae.CANCELLED?ht[3].icon:ht[4].icon,color:u===ae.PENDING?"yellow-500":u===ae.PROCESSING?"blue-500":u===ae.COMPLETED?"green-500":u===ae.CANCELLED?"red-500":"green-500"})),c=Object.values(je).filter(u=>typeof u=="number").map(u=>({label:t(`commission.${je[u]}`),value:u,icon:u===je.PENDING?Vt[0].icon:u===je.PROCESSING?Vt[1].icon:u===je.VALID?Vt[2].icon:Vt[3].icon,color:u===je.PENDING?"yellow-500":u===je.PROCESSING?"blue-500":u===je.VALID?"green-500":"red-500"}));return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Or,{refetch:a}),e.jsx(D,{placeholder:t("search.placeholder"),value:s.getColumn("trade_no")?.getFilterValue()??"",onChange:u=>s.getColumn("trade_no")?.setFilterValue(u.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),e.jsxs("div",{className:"flex flex-wrap gap-x-2",children:[s.getColumn("type")&&e.jsx(la,{column:s.getColumn("type"),title:t("table.columns.type"),options:n}),s.getColumn("period")&&e.jsx(la,{column:s.getColumn("period"),title:t("table.columns.period"),options:o}),s.getColumn("status")&&e.jsx(la,{column:s.getColumn("status"),title:t("table.columns.status"),options:r}),s.getColumn("commission_status")&&e.jsx(la,{column:s.getColumn("commission_status"),title:t("table.columns.commissionStatus"),options:c})]}),l&&e.jsxs(E,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:[t("actions.reset"),e.jsx(ds,{className:"ml-2 h-4 w-4"})]})]})}function us({label:s,value:a,className:t,valueClassName:l}){return e.jsxs("div",{className:y("flex items-center py-1.5",t),children:[e.jsx("div",{className:"w-28 shrink-0 text-sm text-muted-foreground",children:s}),e.jsx("div",{className:y("text-sm",l),children:a||"-"})]})}function Nx({status:s}){const{t:a}=V("order"),t={[ae.PENDING]:"bg-yellow-100 text-yellow-800 hover:bg-yellow-100",[ae.PROCESSING]:"bg-blue-100 text-blue-800 hover:bg-blue-100",[ae.CANCELLED]:"bg-red-100 text-red-800 hover:bg-red-100",[ae.COMPLETED]:"bg-green-100 text-green-800 hover:bg-green-100",[ae.DISCOUNTED]:"bg-gray-100 text-gray-800 hover:bg-gray-100"};return e.jsx(B,{variant:"secondary",className:y("font-medium",t[s]),children:a(`status.${ae[s]}`)})}function _x({id:s,trigger:a}){const[t,l]=m.useState(!1),[n,o]=m.useState(),{t:r}=V("order");return m.useEffect(()=>{(async()=>{if(t){const{data:u}=await Ys.getInfo({id:s});o(u)}})()},[t,s]),e.jsxs(pe,{onOpenChange:l,open:t,children:[e.jsx(rs,{asChild:!0,children:a}),e.jsxs(ue,{className:"max-w-xl",children:[e.jsxs(ve,{className:"space-y-2",children:[e.jsx(ge,{className:"text-lg font-medium",children:r("dialog.title")}),e.jsx("div",{className:"flex items-center justify-between text-sm",children:e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:"text-muted-foreground",children:[r("table.columns.tradeNo"),"：",n?.trade_no]}),n?.status&&e.jsx(Nx,{status:n.status})]})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"rounded-lg border p-4",children:[e.jsx("div",{className:"mb-2 text-sm font-medium",children:r("dialog.basicInfo")}),e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(us,{label:r("dialog.fields.userEmail"),value:n?.user?.email?e.jsxs(st,{to:`/user/manage?email=${n.user.email}`,className:"group inline-flex items-center gap-1 text-primary hover:underline",children:[n.user.email,e.jsx(sr,{className:"h-3.5 w-3.5 opacity-0 transition-opacity group-hover:opacity-100"})]}):"-"}),e.jsx(us,{label:r("dialog.fields.orderPeriod"),value:n&&r(`period.${n.period}`)}),e.jsx(us,{label:r("dialog.fields.subscriptionPlan"),value:n?.plan?.name,valueClassName:"font-medium"}),e.jsx(us,{label:r("dialog.fields.callbackNo"),value:n?.callback_no,valueClassName:"font-mono text-xs"})]})]}),e.jsxs("div",{className:"rounded-lg border p-4",children:[e.jsx("div",{className:"mb-2 text-sm font-medium",children:r("dialog.amountInfo")}),e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(us,{label:r("dialog.fields.paymentAmount"),value:Ws(n?.total_amount||0),valueClassName:"font-medium text-primary"}),e.jsx(ke,{className:"my-2"}),e.jsx(us,{label:r("dialog.fields.balancePayment"),value:Ws(n?.balance_amount||0)}),e.jsx(us,{label:r("dialog.fields.discountAmount"),value:Ws(n?.discount_amount||0),valueClassName:"text-green-600"}),e.jsx(us,{label:r("dialog.fields.refundAmount"),value:Ws(n?.refund_amount||0),valueClassName:"text-red-600"}),e.jsx(us,{label:r("dialog.fields.deductionAmount"),value:Ws(n?.surplus_amount||0)})]})]}),e.jsxs("div",{className:"rounded-lg border p-4",children:[e.jsx("div",{className:"mb-2 text-sm font-medium",children:r("dialog.timeInfo")}),e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(us,{label:r("dialog.fields.createdAt"),value:Ce(n?.created_at),valueClassName:"font-mono text-xs"}),e.jsx(us,{label:r("dialog.fields.updatedAt"),value:Ce(n?.updated_at),valueClassName:"font-mono text-xs"})]})]})]})]})]})}const wx={[ps.NEW]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[ps.RENEWAL]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[ps.UPGRADE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[ps.RESET_FLOW]:{color:"text-slate-700",bgColor:"bg-slate-100/80"}},Cx={[Ie.MONTH_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[Ie.QUARTER_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[Ie.HALF_YEAR_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[Ie.YEAR_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[Ie.TWO_YEAR_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[Ie.THREE_YEAR_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[Ie.ONETIME_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[Ie.RESET_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"}},Sx=s=>ae[s],kx=s=>je[s],Tx=s=>ps[s],Dx=s=>{const{t:a}=V("order");return[{accessorKey:"trade_no",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.tradeNo")}),cell:({row:t})=>{const l=t.original.trade_no,n=l.length>6?`${l.slice(0,3)}...${l.slice(-3)}`:l;return e.jsx("div",{className:"flex items-center",children:e.jsx(_x,{trigger:e.jsxs(G,{variant:"ghost",size:"sm",className:"flex h-8 items-center gap-1.5 px-2 font-medium text-primary transition-colors hover:bg-primary/10 hover:text-primary/80",children:[e.jsx("span",{className:"font-mono",children:n}),e.jsx(sr,{className:"h-3.5 w-3.5 opacity-70"})]}),id:t.original.id})})},enableSorting:!1,enableHiding:!1},{accessorKey:"type",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.type")}),cell:({row:t})=>{const l=t.getValue("type"),n=wx[l];return e.jsx(B,{variant:"secondary",className:y("font-medium transition-colors text-nowrap",n.color,n.bgColor,"border border-border/50","hover:bg-slate-200/80"),children:a(`type.${Tx(l)}`)})},enableSorting:!1,enableHiding:!1},{accessorKey:"plan.name",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.plan")}),cell:({row:t})=>e.jsx("div",{className:"flex space-x-2",children:e.jsx("span",{className:"max-w-32 truncate font-medium text-foreground/90 sm:max-w-72 md:max-w-[31rem]",children:t.original.plan?.name||"-"})}),enableSorting:!1,enableHiding:!1},{accessorKey:"period",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.period")}),cell:({row:t})=>{const l=t.getValue("period"),n=Cx[l];return e.jsx(B,{variant:"secondary",className:y("font-medium transition-colors text-nowrap",n?.color,n?.bgColor,"hover:bg-opacity-80"),children:a(`period.${l}`)})},enableSorting:!1,enableHiding:!1},{accessorKey:"total_amount",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.amount")}),cell:({row:t})=>{const l=t.getValue("total_amount"),n=typeof l=="number"?(l/100).toFixed(2):"N/A";return e.jsxs("div",{className:"flex items-center font-mono text-foreground/90",children:["¥",n]})},enableSorting:!0,enableHiding:!1},{accessorKey:"status",header:({column:t})=>e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(z,{column:t,title:a("table.columns.status")}),e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{children:e.jsx(Pr,{className:"h-4 w-4 text-muted-foreground/70 transition-colors hover:text-muted-foreground"})}),e.jsx(de,{side:"top",className:"max-w-[200px] text-sm",children:a("status.tooltip")})]})})]}),cell:({row:t})=>{const l=ht.find(n=>n.value===t.getValue("status"));return l?e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[l.icon&&e.jsx(l.icon,{className:`h-4 w-4 text-${l.color}`}),e.jsx("span",{className:"text-sm font-medium",children:a(`status.${Sx(l.value)}`)})]}),l.value===ae.PENDING&&e.jsxs(Es,{modal:!0,children:[e.jsx(Rs,{asChild:!0,children:e.jsxs(G,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-muted/60",children:[e.jsx(ua,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:a("actions.openMenu")})]})}),e.jsxs(Cs,{align:"end",className:"w-[140px]",children:[e.jsx(Ne,{className:"cursor-pointer",onClick:async()=>{await Ys.markPaid({trade_no:t.original.trade_no}),s()},children:a("actions.markAsPaid")}),e.jsx(Ne,{className:"cursor-pointer text-destructive focus:text-destructive",onClick:async()=>{await Ys.makeCancel({trade_no:t.original.trade_no}),s()},children:a("actions.cancel")})]})]})]}):null},enableSorting:!0,enableHiding:!1},{accessorKey:"commission_balance",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.commission")}),cell:({row:t})=>{const l=t.getValue("commission_balance"),n=l?(l/100).toFixed(2):"-";return e.jsx("div",{className:"flex items-center font-mono text-foreground/90",children:l?`¥${n}`:"-"})},enableSorting:!0,enableHiding:!1},{accessorKey:"commission_status",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.commissionStatus")}),cell:({row:t})=>{const l=t.original.status,n=t.original.commission_balance,o=Vt.find(r=>r.value===t.getValue("commission_status"));return n==0||!o?e.jsx("span",{className:"text-muted-foreground",children:"-"}):e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[o.icon&&e.jsx(o.icon,{className:`h-4 w-4 text-${o.color}`}),e.jsx("span",{className:"text-sm font-medium",children:a(`commission.${kx(o.value)}`)})]}),o.value===je.PENDING&&l===ae.COMPLETED&&e.jsxs(Es,{modal:!0,children:[e.jsx(Rs,{asChild:!0,children:e.jsxs(G,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-muted/60",children:[e.jsx(ua,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:a("actions.openMenu")})]})}),e.jsxs(Cs,{align:"end",className:"w-[120px]",children:[e.jsx(Ne,{className:"cursor-pointer",onClick:async()=>{await Ys.update({trade_no:t.original.trade_no,commission_status:je.PROCESSING}),s()},children:a("commission.PROCESSING")}),e.jsx(Ne,{className:"cursor-pointer text-destructive focus:text-destructive",onClick:async()=>{await Ys.update({trade_no:t.original.trade_no,commission_status:je.INVALID}),s()},children:a("commission.INVALID")})]})]})]})},enableSorting:!0,enableHiding:!1},{accessorKey:"created_at",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.createdAt")}),cell:({row:t})=>e.jsx("div",{className:"text-nowrap font-mono text-sm text-muted-foreground",children:Ce(t.getValue("created_at"),"YYYY/MM/DD HH:mm:ss")}),enableSorting:!0,enableHiding:!1}]};function Px(){const[s]=tr(),[a,t]=m.useState({}),[l,n]=m.useState({}),[o,r]=m.useState([]),[c,u]=m.useState([]),[i,d]=m.useState({pageIndex:0,pageSize:20});m.useEffect(()=>{const N=Object.entries({user_id:"string",order_id:"string",commission_status:"number",status:"number",commission_balance:"string"}).map(([g,k])=>{const R=s.get(g);return R?{id:g,value:k==="number"?parseInt(R):R}:null}).filter(Boolean);N.length>0&&r(N)},[s]);const{refetch:h,data:_,isLoading:T}=ne({queryKey:["orderList",i,o,c],queryFn:()=>Ys.getList({pageSize:i.pageSize,current:i.pageIndex+1,filter:o,sort:c})}),S=Je({data:_?.data??[],columns:Dx(h),state:{sorting:c,columnVisibility:l,rowSelection:a,columnFilters:o,pagination:i},rowCount:_?.total??0,manualPagination:!0,manualFiltering:!0,manualSorting:!0,enableRowSelection:!0,onRowSelectionChange:t,onSortingChange:u,onColumnFiltersChange:r,onColumnVisibilityChange:n,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),onPaginationChange:d,getSortedRowModel:vs(),getFacetedRowModel:Ls(),getFacetedUniqueValues:Vs(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(is,{table:S,toolbar:e.jsx(yx,{table:S,refetch:h}),showPagination:!0})}function Ex(){const{t:s}=V("order");return e.jsxs(Ve,{children:[e.jsxs(Fe,{children:[e.jsx(Xe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")}),e.jsx("p",{className:"text-muted-foreground mt-2",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(Px,{})})]})]})}const Rx=Object.freeze(Object.defineProperty({__proto__:null,default:Ex},Symbol.toStringTag,{value:"Module"}));function Ix({column:s,title:a,options:t}){const l=s?.getFacetedUniqueValues(),n=new Set(s?.getFilterValue());return e.jsxs(Ss,{children:[e.jsx(ks,{asChild:!0,children:e.jsxs(E,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(_a,{className:"mr-2 h-4 w-4"}),a,n?.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(ke,{orientation:"vertical",className:"mx-2 h-4"}),e.jsx(B,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:n.size}),e.jsx("div",{className:"hidden space-x-1 lg:flex",children:n.size>2?e.jsxs(B,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[n.size," selected"]}):t.filter(o=>n.has(o.value)).map(o=>e.jsx(B,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:o.label},o.value))})]})]})}),e.jsx(bs,{className:"w-[200px] p-0",align:"start",children:e.jsxs(Us,{children:[e.jsx(nt,{placeholder:a}),e.jsxs(Ks,{children:[e.jsx(lt,{children:"No results found."}),e.jsx(as,{children:t.map(o=>{const r=n.has(o.value);return e.jsxs($e,{onSelect:()=>{r?n.delete(o.value):n.add(o.value);const c=Array.from(n);s?.setFilterValue(c.length?c:void 0)},children:[e.jsx("div",{className:y("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",r?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(et,{className:y("h-4 w-4")})}),o.icon&&e.jsx(o.icon,{className:`mr-2 h-4 w-4 text-muted-foreground text-${o.color}`}),e.jsx("span",{children:o.label}),l?.get(o.value)&&e.jsx("span",{className:"ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs",children:l.get(o.value)})]},o.value)})}),n.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(St,{}),e.jsx(as,{children:e.jsx($e,{onSelect:()=>s?.setFilterValue(void 0),className:"justify-center text-center",children:"Clear filters"})})]})]})]})})]})}const Lx=x.object({id:x.coerce.number().nullable().optional(),name:x.string().min(1,"请输入优惠券名称"),code:x.string().nullable(),type:x.coerce.number(),value:x.coerce.number(),started_at:x.coerce.number(),ended_at:x.coerce.number(),limit_use:x.union([x.string(),x.number()]).nullable(),limit_use_with_user:x.union([x.string(),x.number()]).nullable(),generate_count:x.coerce.number().nullable().optional(),limit_plan_ids:x.array(x.coerce.number()).default([]).nullable(),limit_period:x.array(x.nativeEnum(zt)).default([]).nullable()}).refine(s=>s.ended_at>s.started_at,{message:"结束时间必须晚于开始时间",path:["ended_at"]}),Zn={name:"",code:null,type:Ze.AMOUNT,value:0,started_at:Math.floor(Date.now()/1e3),ended_at:Math.floor(Date.now()/1e3)+7*24*60*60,limit_use:null,limit_use_with_user:null,limit_plan_ids:[],limit_period:[],generate_count:null};function zr({defaultValues:s,refetch:a,type:t="create",dialogTrigger:l=null,open:n,onOpenChange:o}){const{t:r}=V("coupon"),[c,u]=m.useState(!1),i=n??c,d=o??u,[h,_]=m.useState([]),T=ye({resolver:_e(Lx),defaultValues:s||Zn});m.useEffect(()=>{s&&T.reset(s)},[s,T]),m.useEffect(()=>{es.getList().then(({data:g})=>_(g))},[]);const S=g=>{if(!g)return;const k=(R,p)=>{const w=new Date(p*1e3);return R.setHours(w.getHours(),w.getMinutes(),w.getSeconds()),Math.floor(R.getTime()/1e3)};g.from&&T.setValue("started_at",k(g.from,T.watch("started_at"))),g.to&&T.setValue("ended_at",k(g.to,T.watch("ended_at")))},C=async g=>{const k=await ga.save(g);if(g.generate_count&&k){const R=new Blob([k],{type:"text/csv;charset=utf-8;"}),p=document.createElement("a");p.href=window.URL.createObjectURL(R),p.download=`coupons_${new Date().getTime()}.csv`,p.click(),window.URL.revokeObjectURL(p.href)}d(!1),t==="create"&&T.reset(Zn),a()},N=(g,k)=>e.jsxs("div",{className:"flex-1 space-y-1.5",children:[e.jsx("div",{className:"text-sm font-medium text-muted-foreground",children:k}),e.jsx(D,{type:"datetime-local",step:"1",value:Ce(T.watch(g),"YYYY-MM-DDTHH:mm:ss"),onChange:R=>{const p=new Date(R.target.value);T.setValue(g,Math.floor(p.getTime()/1e3))},className:"h-8 [&::-webkit-calendar-picker-indicator]:hidden"})]});return e.jsxs(pe,{open:i,onOpenChange:d,children:[l&&e.jsx(rs,{asChild:!0,children:l}),e.jsxs(ue,{className:"sm:max-w-[425px]",children:[e.jsx(ve,{children:e.jsx(ge,{children:r(t==="create"?"form.add":"form.edit")})}),e.jsx(we,{...T,children:e.jsxs("form",{onSubmit:T.handleSubmit(C),className:"space-y-4",children:[e.jsx(v,{control:T.control,name:"name",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:r("form.name.label")}),e.jsx(D,{placeholder:r("form.name.placeholder"),...g}),e.jsx(P,{})]})}),t==="create"&&e.jsx(v,{control:T.control,name:"generate_count",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:r("form.generateCount.label")}),e.jsx(D,{type:"number",min:0,placeholder:r("form.generateCount.placeholder"),...g,value:g.value===void 0?"":g.value,onChange:k=>g.onChange(k.target.value===""?"":parseInt(k.target.value)),className:"h-9"}),e.jsx(F,{className:"text-xs",children:r("form.generateCount.description")}),e.jsx(P,{})]})}),(!T.watch("generate_count")||T.watch("generate_count")==null)&&e.jsx(v,{control:T.control,name:"code",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:r("form.code.label")}),e.jsx(D,{placeholder:r("form.code.placeholder"),...g,className:"h-9"}),e.jsx(F,{className:"text-xs",children:r("form.code.description")}),e.jsx(P,{})]})}),e.jsxs(f,{children:[e.jsx(j,{children:r("form.type.label")}),e.jsxs("div",{className:"flex",children:[e.jsx(v,{control:T.control,name:"type",render:({field:g})=>e.jsxs(X,{value:g.value.toString(),onValueChange:k=>{const R=g.value,p=parseInt(k);g.onChange(p);const w=T.getValues("value");w&&(R===Ze.AMOUNT&&p===Ze.PERCENTAGE?T.setValue("value",w/100):R===Ze.PERCENTAGE&&p===Ze.AMOUNT&&T.setValue("value",w*100))},children:[e.jsx(Y,{className:"flex-[1.2] rounded-r-none border-r-0 focus:z-10",children:e.jsx(Z,{placeholder:r("form.type.placeholder")})}),e.jsx(J,{children:Object.entries(Kd).map(([k,R])=>e.jsx($,{value:k,children:r(`table.toolbar.types.${k}`)},k))})]})}),e.jsx(v,{control:T.control,name:"value",render:({field:g})=>{const k=g.value==null?"":T.watch("type")===Ze.AMOUNT&&typeof g.value=="number"?(g.value/100).toString():g.value.toString();return e.jsx(D,{type:"number",placeholder:r("form.value.placeholder"),...g,value:k,onChange:R=>{const p=R.target.value;if(p===""){g.onChange("");return}const w=parseFloat(p);isNaN(w)||g.onChange(T.watch("type")===Ze.AMOUNT?Math.round(w*100):w)},step:"any",min:0,className:"flex-[2] rounded-none border-x-0 text-left"})}}),e.jsx("div",{className:"flex min-w-[40px] items-center justify-center rounded-md rounded-l-none border border-l-0 border-input bg-muted/50 px-3 font-medium text-muted-foreground",children:e.jsx("span",{children:T.watch("type")==Ze.AMOUNT?"¥":"%"})})]})]}),e.jsxs(f,{children:[e.jsx(j,{children:r("form.validity.label")}),e.jsxs(Ss,{children:[e.jsx(ks,{asChild:!0,children:e.jsxs(E,{variant:"outline",className:y("w-full justify-start text-left font-normal",!T.watch("started_at")&&"text-muted-foreground"),children:[e.jsx(Kt,{className:"mr-2 h-4 w-4"}),Ce(T.watch("started_at"),"YYYY-MM-DD HH:mm:ss")," ",r("form.validity.to")," ",Ce(T.watch("ended_at"),"YYYY-MM-DD HH:mm:ss")]})}),e.jsxs(bs,{className:"w-auto p-0",align:"start",children:[e.jsx("div",{className:"border-b border-border",children:e.jsx(rt,{mode:"range",selected:{from:new Date(T.watch("started_at")*1e3),to:new Date(T.watch("ended_at")*1e3)},onSelect:S,numberOfMonths:2})}),e.jsx("div",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-4",children:[N("started_at",r("table.validity.startTime")),e.jsx("div",{className:"mt-6 text-sm text-muted-foreground",children:r("form.validity.to")}),N("ended_at",r("table.validity.endTime"))]})})]})]}),e.jsx(P,{})]}),e.jsx(v,{control:T.control,name:"limit_use",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:r("form.limitUse.label")}),e.jsx(D,{type:"number",min:0,placeholder:r("form.limitUse.placeholder"),...g,value:g.value===null?"":g.value,onChange:k=>g.onChange(k.target.value===""?null:parseInt(k.target.value)),className:"h-9"}),e.jsx(F,{className:"text-xs",children:r("form.limitUse.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:T.control,name:"limit_use_with_user",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:r("form.limitUseWithUser.label")}),e.jsx(D,{type:"number",min:0,placeholder:r("form.limitUseWithUser.placeholder"),...g,value:g.value===null?"":g.value,onChange:k=>g.onChange(k.target.value===""?null:parseInt(k.target.value)),className:"h-9"}),e.jsx(F,{className:"text-xs",children:r("form.limitUseWithUser.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:T.control,name:"limit_period",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:r("form.limitPeriod.label")}),e.jsx(_t,{options:Object.entries(zt).filter(([k])=>isNaN(Number(k))).map(([k,R])=>({label:r(`coupon:period.${R}`),value:k})),onChange:k=>{if(k.length===0){g.onChange([]);return}const R=k.map(p=>zt[p.value]);g.onChange(R)},value:(g.value||[]).map(k=>({label:r(`coupon:period.${k}`),value:Object.entries(zt).find(([R,p])=>p===k)?.[0]||""})),placeholder:r("form.limitPeriod.placeholder"),emptyIndicator:e.jsx("p",{className:"text-center text-sm text-muted-foreground",children:r("form.limitPeriod.empty")})}),e.jsx(F,{className:"text-xs",children:r("form.limitPeriod.description")}),e.jsx(P,{})]})}),e.jsx(v,{control:T.control,name:"limit_plan_ids",render:({field:g})=>e.jsxs(f,{children:[e.jsx(j,{children:r("form.limitPlan.label")}),e.jsx(_t,{options:h?.map(k=>({label:k.name,value:k.id.toString()}))||[],onChange:k=>g.onChange(k.map(R=>Number(R.value))),value:(h||[]).filter(k=>(g.value||[]).includes(k.id)).map(k=>({label:k.name,value:k.id.toString()})),placeholder:r("form.limitPlan.placeholder"),emptyIndicator:e.jsx("p",{className:"text-center text-sm text-muted-foreground",children:r("form.limitPlan.empty")})}),e.jsx(P,{})]})}),e.jsx(Re,{children:e.jsx(E,{type:"submit",disabled:T.formState.isSubmitting,children:T.formState.isSubmitting?r("form.submit.saving"):r("form.submit.save")})})]})})]})]})}function Vx({table:s,refetch:a}){const t=s.getState().columnFilters.length>0,{t:l}=V("coupon");return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(zr,{refetch:a,dialogTrigger:e.jsxs(E,{variant:"outline",size:"sm",className:"h-8 space-x-2",children:[e.jsx(ze,{icon:"ion:add"}),e.jsx("div",{children:l("form.add")})]})}),e.jsx(D,{placeholder:l("table.toolbar.search"),value:s.getColumn("name")?.getFilterValue()??"",onChange:n=>s.getColumn("name")?.setFilterValue(n.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),s.getColumn("type")&&e.jsx(Ix,{column:s.getColumn("type"),title:l("table.toolbar.type"),options:[{value:Ze.AMOUNT,label:l(`table.toolbar.types.${Ze.AMOUNT}`)},{value:Ze.PERCENTAGE,label:l(`table.toolbar.types.${Ze.PERCENTAGE}`)}]}),t&&e.jsxs(E,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:[l("table.toolbar.reset"),e.jsx(ds,{className:"ml-2 h-4 w-4"})]})]})}const $r=m.createContext(void 0);function Fx({children:s,refetch:a}){const[t,l]=m.useState(!1),[n,o]=m.useState(null),r=u=>{o(u),l(!0)},c=()=>{l(!1),o(null)};return e.jsxs($r.Provider,{value:{isOpen:t,currentCoupon:n,openEdit:r,closeEdit:c},children:[s,n&&e.jsx(zr,{defaultValues:n,refetch:a,type:"edit",open:t,onOpenChange:l})]})}function Mx(){const s=m.useContext($r);if(s===void 0)throw new Error("useCouponEdit must be used within a CouponEditProvider");return s}const Ox=s=>{const{t:a}=V("coupon");return[{accessorKey:"id",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.id")}),cell:({row:t})=>e.jsx(B,{children:t.original.id}),enableSorting:!0},{accessorKey:"show",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.show")}),cell:({row:t})=>e.jsx(ee,{defaultChecked:t.original.show,onCheckedChange:l=>{ga.update({id:t.original.id,show:l}).then(({data:n})=>!n&&s())}}),enableSorting:!1},{accessorKey:"name",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.name")}),cell:({row:t})=>e.jsx("div",{className:"flex items-center",children:e.jsx("span",{children:t.original.name})}),enableSorting:!1,size:800},{accessorKey:"type",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.type")}),cell:({row:t})=>e.jsx(B,{variant:"outline",children:a(`table.toolbar.types.${t.original.type}`)}),enableSorting:!0},{accessorKey:"code",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.code")}),cell:({row:t})=>e.jsx(B,{variant:"secondary",children:t.original.code}),enableSorting:!0},{accessorKey:"limit_use",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.limitUse")}),cell:({row:t})=>e.jsx(B,{variant:"outline",children:t.original.limit_use===null?a("table.validity.unlimited"):t.original.limit_use}),enableSorting:!0},{accessorKey:"limit_use_with_user",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.limitUseWithUser")}),cell:({row:t})=>e.jsx(B,{variant:"outline",children:t.original.limit_use_with_user===null?a("table.validity.noLimit"):t.original.limit_use_with_user}),enableSorting:!0},{accessorKey:"#",header:({column:t})=>e.jsx(z,{column:t,title:a("table.columns.validity")}),cell:({row:t})=>{const[l,n]=m.useState(!1),o=Date.now(),r=t.original.started_at*1e3,c=t.original.ended_at*1e3,u=o>c,i=o<r,d=Math.ceil((c-o)/(1e3*60*60*24)),_=u?{label:a("table.validity.expired",{days:Math.abs(d)}),color:"bg-red-50 text-red-600 dark:bg-red-500/10 dark:text-red-400"}:i?{label:a("table.validity.notStarted",{days:Math.abs(Math.ceil((r-o)/(1e3*60*60*24)))}),color:"bg-yellow-50 text-yellow-600 dark:bg-yellow-500/10 dark:text-yellow-400"}:{label:a("table.validity.remaining",{days:d}),color:"bg-green-50 text-green-600 dark:bg-green-500/10 dark:text-green-400"};return e.jsxs(gr,{open:l,onOpenChange:n,children:[e.jsx(fr,{asChild:!0,children:e.jsxs("div",{className:"group -m-0.5 flex max-w-[280px] cursor-pointer items-center gap-2 rounded-md p-0.5 transition-colors hover:bg-muted/40",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx("div",{className:y("whitespace-nowrap rounded-md px-1.5 py-0.5 text-xs font-medium",_.color),children:_.label}),e.jsxs("div",{className:"flex min-w-0 items-center gap-1 text-muted-foreground",children:[e.jsx("div",{className:"truncate text-xs",children:Ce(t.original.started_at,"MM/DD HH:mm")}),e.jsx("div",{className:"shrink-0 opacity-30",children:"→"}),e.jsx("div",{className:"truncate text-xs",children:Ce(t.original.ended_at,"MM/DD HH:mm")})]})]}),e.jsx(Dc,{className:y("h-3.5 w-3.5 shrink-0 text-muted-foreground/50 transition-transform duration-200",l&&"rotate-180")})]})}),e.jsx(jr,{children:e.jsx("div",{className:"px-0.5 pb-0.5 pt-1.5",children:e.jsxs("div",{className:"space-y-1.5 border-l-2 border-muted pl-3 text-xs text-muted-foreground",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:a("table.validity.startTime")}),e.jsx("span",{className:"font-medium text-foreground",children:Ce(t.original.started_at,"YYYY/MM/DD HH:mm")})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:a("table.validity.endTime")}),e.jsx("span",{className:"font-medium text-foreground",children:Ce(t.original.ended_at,"YYYY/MM/DD HH:mm")})]})]})})})]})},enableSorting:!1,size:8e3},{id:"actions",header:({column:t})=>e.jsx(z,{className:"justify-end",column:t,title:a("table.columns.actions")}),cell:({row:t})=>{const{openEdit:l}=Mx();return e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",onClick:()=>l(t.original),children:[e.jsx(tt,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:a("table.actions.edit")})]}),e.jsx(ns,{title:a("table.actions.deleteConfirm.title"),description:a("table.actions.deleteConfirm.description"),confirmText:a("table.actions.deleteConfirm.confirmText"),variant:"destructive",onConfirm:async()=>{ga.drop({id:t.original.id}).then(({data:n})=>{n&&(A.success("删除成功"),s())})},children:e.jsxs(E,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(fs,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:a("table.actions.delete")})]})})]})}}]};function zx(){const[s,a]=m.useState({}),[t,l]=m.useState({}),[n,o]=m.useState([]),[r,c]=m.useState([]),[u,i]=m.useState({pageIndex:0,pageSize:20}),{refetch:d,data:h}=ne({queryKey:["couponList",u,n,r],queryFn:()=>ga.getList({pageSize:u.pageSize,current:u.pageIndex+1,filter:n,sort:r})}),_=Je({data:h?.data??[],columns:Ox(d),state:{sorting:r,columnVisibility:t,rowSelection:s,columnFilters:n,pagination:u},pageCount:Math.ceil((h?.total??0)/u.pageSize),rowCount:h?.total??0,manualPagination:!0,manualFiltering:!0,manualSorting:!0,enableRowSelection:!0,onRowSelectionChange:a,onSortingChange:c,onColumnFiltersChange:o,onColumnVisibilityChange:l,onPaginationChange:i,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),getSortedRowModel:vs(),getFacetedRowModel:Ls(),getFacetedUniqueValues:Vs(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(Fx,{refetch:d,children:e.jsx("div",{className:"space-y-4",children:e.jsx(is,{table:_,toolbar:e.jsx(Vx,{table:_,refetch:d})})})})}function $x(){const{t:s}=V("coupon");return e.jsxs(Ve,{children:[e.jsxs(Fe,{children:[e.jsx(Xe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")}),e.jsx("p",{className:"text-muted-foreground mt-2",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(zx,{})})]})]})}const Ax=Object.freeze(Object.defineProperty({__proto__:null,default:$x},Symbol.toStringTag,{value:"Module"})),qx=1,Hx=1e6;let Wa=0;function Ux(){return Wa=(Wa+1)%Number.MAX_SAFE_INTEGER,Wa.toString()}const Ya=new Map,el=s=>{if(Ya.has(s))return;const a=setTimeout(()=>{Ya.delete(s),$t({type:"REMOVE_TOAST",toastId:s})},Hx);Ya.set(s,a)},Kx=(s,a)=>{switch(a.type){case"ADD_TOAST":return{...s,toasts:[a.toast,...s.toasts].slice(0,qx)};case"UPDATE_TOAST":return{...s,toasts:s.toasts.map(t=>t.id===a.toast.id?{...t,...a.toast}:t)};case"DISMISS_TOAST":{const{toastId:t}=a;return t?el(t):s.toasts.forEach(l=>{el(l.id)}),{...s,toasts:s.toasts.map(l=>l.id===t||t===void 0?{...l,open:!1}:l)}}case"REMOVE_TOAST":return a.toastId===void 0?{...s,toasts:[]}:{...s,toasts:s.toasts.filter(t=>t.id!==a.toastId)}}},oa=[];let ca={toasts:[]};function $t(s){ca=Kx(ca,s),oa.forEach(a=>{a(ca)})}function Bx({...s}){const a=Ux(),t=n=>$t({type:"UPDATE_TOAST",toast:{...n,id:a}}),l=()=>$t({type:"DISMISS_TOAST",toastId:a});return $t({type:"ADD_TOAST",toast:{...s,id:a,open:!0,onOpenChange:n=>{n||l()}}}),{id:a,dismiss:l,update:t}}function Ar(){const[s,a]=m.useState(ca);return m.useEffect(()=>(oa.push(a),()=>{const t=oa.indexOf(a);t>-1&&oa.splice(t,1)}),[s]),{...s,toast:Bx,dismiss:t=>$t({type:"DISMISS_TOAST",toastId:t})}}function Gx({open:s,onOpenChange:a,table:t}){const{t:l}=V("user"),{toast:n}=Ar(),[o,r]=m.useState(!1),[c,u]=m.useState(""),[i,d]=m.useState(""),h=async()=>{if(!c||!i){n({title:l("messages.error"),description:l("messages.send_mail.required_fields"),variant:"destructive"});return}try{r(!0),await ws.sendMail({subject:c,content:i,filter:t.getState().columnFilters,sort:t.getState().sorting[0]?.id,sort_type:t.getState().sorting[0]?.desc?"DESC":"ASC"}),n({title:l("messages.success"),description:l("messages.send_mail.success")}),a(!1),u(""),d("")}catch{n({title:l("messages.error"),description:l("messages.send_mail.failed"),variant:"destructive"})}finally{r(!1)}};return e.jsx(pe,{open:s,onOpenChange:a,children:e.jsxs(ue,{className:"sm:max-w-[500px]",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:l("send_mail.title")}),e.jsx(Le,{children:l("send_mail.description")})]}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx("label",{htmlFor:"subject",className:"text-right",children:l("send_mail.subject")}),e.jsx(D,{id:"subject",value:c,onChange:_=>u(_.target.value),className:"col-span-3"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx("label",{htmlFor:"content",className:"text-right",children:l("send_mail.content")}),e.jsx(Ts,{id:"content",value:i,onChange:_=>d(_.target.value),className:"col-span-3",rows:6})]})]}),e.jsx(Re,{children:e.jsx(G,{type:"submit",onClick:h,disabled:o,children:l(o?"send_mail.sending":"send_mail.send")})})]})})}const Wx=x.object({email_prefix:x.string().optional(),email_suffix:x.string().min(1),password:x.string().optional(),expired_at:x.number().optional().nullable(),plan_id:x.number().nullable(),generate_count:x.number().optional().nullable(),download_csv:x.boolean().optional()}).refine(s=>s.generate_count===null?s.email_prefix!==void 0&&s.email_prefix!=="":!0,{message:"Email prefix is required when generate_count is null",path:["email_prefix"]}),Yx={email_prefix:"",email_suffix:"",password:"",expired_at:null,plan_id:null,generate_count:void 0,download_csv:!1};function Jx({refetch:s}){const{t:a}=V("user"),[t,l]=m.useState(!1),n=ye({resolver:_e(Wx),defaultValues:Yx,mode:"onChange"}),[o,r]=m.useState([]);return m.useEffect(()=>{t&&es.getList().then(({data:c})=>{c&&r(c)})},[t]),e.jsxs(pe,{open:t,onOpenChange:l,children:[e.jsx(rs,{asChild:!0,children:e.jsxs(G,{size:"sm",variant:"outline",className:"gap-0 space-x-2",children:[e.jsx(ze,{icon:"ion:add"}),e.jsx("div",{children:a("generate.button")})]})}),e.jsxs(ue,{className:"sm:max-w-[425px]",children:[e.jsxs(ve,{children:[e.jsx(ge,{children:a("generate.title")}),e.jsx(Le,{})]}),e.jsxs(we,{...n,children:[e.jsxs(f,{children:[e.jsx(j,{children:a("generate.form.email")}),e.jsxs("div",{className:"flex",children:[!n.watch("generate_count")&&e.jsx(v,{control:n.control,name:"email_prefix",render:({field:c})=>e.jsx(D,{className:"flex-[5] rounded-r-none",placeholder:a("generate.form.email_prefix"),...c})}),e.jsx("div",{className:`z-[-1] border border-r-0 border-input px-3 py-1 shadow-sm ${n.watch("generate_count")?"rounded-l-md":"border-l-0"}`,children:"@"}),e.jsx(v,{control:n.control,name:"email_suffix",render:({field:c})=>e.jsx(D,{className:"flex-[4] rounded-l-none",placeholder:a("generate.form.email_domain"),...c})})]})]}),e.jsx(v,{control:n.control,name:"password",render:({field:c})=>e.jsxs(f,{children:[e.jsx(j,{children:a("generate.form.password")}),e.jsx(D,{placeholder:a("generate.form.password_placeholder"),...c}),e.jsx(P,{})]})}),e.jsx(v,{control:n.control,name:"expired_at",render:({field:c})=>e.jsxs(f,{className:"flex flex-col",children:[e.jsx(j,{children:a("generate.form.expire_time")}),e.jsxs(Ss,{children:[e.jsx(ks,{asChild:!0,children:e.jsx(b,{children:e.jsxs(G,{variant:"outline",className:y("w-full pl-3 text-left font-normal",!c.value&&"text-muted-foreground"),children:[c.value?Ce(c.value):e.jsx("span",{children:a("generate.form.expire_time_placeholder")}),e.jsx(Kt,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),e.jsxs(bs,{className:"flex w-auto flex-col space-y-2 p-2",children:[e.jsx(Pc,{asChild:!0,children:e.jsx(G,{variant:"outline",className:"w-full",onClick:()=>{c.onChange(null)},children:a("generate.form.permanent")})}),e.jsx("div",{className:"rounded-md border",children:e.jsx(rt,{mode:"single",selected:c.value?new Date(c.value*1e3):void 0,onSelect:u=>{u&&c.onChange(u?.getTime()/1e3)}})})]})]})]})}),e.jsx(v,{control:n.control,name:"plan_id",render:({field:c})=>e.jsxs(f,{children:[e.jsx(j,{children:a("generate.form.subscription")}),e.jsx(b,{children:e.jsxs(X,{value:c.value?c.value.toString():"null",onValueChange:u=>c.onChange(u==="null"?null:parseInt(u)),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:a("generate.form.subscription_none")})}),e.jsxs(J,{children:[e.jsx($,{value:"null",children:a("generate.form.subscription_none")}),o.map(u=>e.jsx($,{value:u.id.toString(),children:u.name},u.id))]})]})})]})}),!n.watch("email_prefix")&&e.jsx(v,{control:n.control,name:"generate_count",render:({field:c})=>e.jsxs(f,{children:[e.jsx(j,{children:a("generate.form.generate_count")}),e.jsx(D,{type:"number",placeholder:a("generate.form.generate_count_placeholder"),value:c.value||"",onChange:u=>c.onChange(u.target.value?parseInt(u.target.value):null)})]})}),n.watch("generate_count")&&e.jsx(v,{control:n.control,name:"download_csv",render:({field:c})=>e.jsxs(f,{className:"flex cursor-pointer flex-row items-center space-x-2 space-y-0",children:[e.jsx(b,{children:e.jsx(Tn,{checked:c.value,onCheckedChange:c.onChange})}),e.jsx(j,{children:a("generate.form.download_csv")})]})})]}),e.jsxs(Re,{children:[e.jsx(G,{variant:"outline",onClick:()=>l(!1),children:a("generate.form.cancel")}),e.jsx(G,{onClick:()=>n.handleSubmit(async c=>{if(c.download_csv){const u=await ws.generate(c);if(u&&u instanceof Blob){const i=window.URL.createObjectURL(u),d=document.createElement("a");d.href=i,d.download=`users_${new Date().getTime()}.csv`,document.body.appendChild(d),d.click(),d.remove(),window.URL.revokeObjectURL(i),A.success(a("generate.form.success")),n.reset(),s(),l(!1)}}else{const{data:u}=await ws.generate(c);u&&(A.success(a("generate.form.success")),n.reset(),s(),l(!1))}})(),children:a("generate.form.submit")})]})]})]})}const qr=ol,Qx=cl,Xx=dl,Hr=m.forwardRef(({className:s,...a},t)=>e.jsx(ja,{className:y("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...a,ref:t}));Hr.displayName=ja.displayName;const Zx=Zs("fixed overflow-y-scroll  z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-300 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-md",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-md"}},defaultVariants:{side:"right"}}),Dn=m.forwardRef(({side:s="right",className:a,children:t,...l},n)=>e.jsxs(Xx,{children:[e.jsx(Hr,{}),e.jsxs(va,{ref:n,className:y(Zx({side:s}),a),...l,children:[e.jsxs(mn,{className:"absolute right-4 top-4  rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[e.jsx(ds,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]}),t]})]}));Dn.displayName=va.displayName;const Pn=({className:s,...a})=>e.jsx("div",{className:y("flex flex-col space-y-2 text-center sm:text-left",s),...a});Pn.displayName="SheetHeader";const Ur=({className:s,...a})=>e.jsx("div",{className:y("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a});Ur.displayName="SheetFooter";const En=m.forwardRef(({className:s,...a},t)=>e.jsx(ba,{ref:t,className:y("text-lg font-semibold text-foreground",s),...a}));En.displayName=ba.displayName;const Rn=m.forwardRef(({className:s,...a},t)=>e.jsx(ya,{ref:t,className:y("text-sm text-muted-foreground",s),...a}));Rn.displayName=ya.displayName;function eh({table:s,refetch:a,permissionGroups:t=[],subscriptionPlans:l=[]}){const{t:n}=V("user"),{toast:o}=Ar(),r=s.getState().columnFilters.length>0,[c,u]=m.useState([]),[i,d]=m.useState(!1),[h,_]=m.useState(!1),[T,S]=m.useState(!1),[C,N]=m.useState(!1),g=async()=>{try{const W=await ws.dumpCSV({filter:s.getState().columnFilters,sort:s.getState().sorting[0]?.id,sort_type:s.getState().sorting[0]?.desc?"DESC":"ASC"}),te=W;console.log(W);const q=new Blob([te],{type:"text/csv;charset=utf-8;"}),L=window.URL.createObjectURL(q),U=document.createElement("a");U.href=L,U.setAttribute("download",`users_${new Date().toISOString()}.csv`),document.body.appendChild(U),U.click(),U.remove(),window.URL.revokeObjectURL(L),o({title:n("messages.success"),description:n("messages.export.success")})}catch{o({title:n("messages.error"),description:n("messages.export.failed"),variant:"destructive"})}},k=async()=>{try{N(!0),await ws.batchBan({filter:s.getState().columnFilters,sort:s.getState().sorting[0]?.id,sort_type:s.getState().sorting[0]?.desc?"DESC":"ASC"}),o({title:n("messages.success"),description:n("messages.batch_ban.success")}),a()}catch{o({title:n("messages.error"),description:n("messages.batch_ban.failed"),variant:"destructive"})}finally{N(!1),S(!1)}},R=[{label:n("filter.fields.email"),value:"email",type:"text",operators:[{label:n("filter.operators.contains"),value:"contains"},{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.id"),value:"id",type:"number",operators:[{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.plan_id"),value:"plan_id",type:"select",operators:[{label:n("filter.operators.eq"),value:"eq"}],useOptions:!0},{label:n("filter.fields.transfer_enable"),value:"transfer_enable",type:"number",unit:"GB",operators:[{label:n("filter.operators.gt"),value:"gt"},{label:n("filter.operators.lt"),value:"lt"},{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.total_used"),value:"total_used",type:"number",unit:"GB",operators:[{label:n("filter.operators.gt"),value:"gt"},{label:n("filter.operators.lt"),value:"lt"},{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.online_count"),value:"online_count",type:"number",operators:[{label:n("filter.operators.eq"),value:"eq"},{label:n("filter.operators.gt"),value:"gt"},{label:n("filter.operators.lt"),value:"lt"}]},{label:n("filter.fields.expired_at"),value:"expired_at",type:"date",operators:[{label:n("filter.operators.lt"),value:"lt"},{label:n("filter.operators.gt"),value:"gt"},{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.uuid"),value:"uuid",type:"text",operators:[{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.token"),value:"token",type:"text",operators:[{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.banned"),value:"banned",type:"select",operators:[{label:n("filter.operators.eq"),value:"eq"}],options:[{label:n("filter.status.normal"),value:"0"},{label:n("filter.status.banned"),value:"1"}]},{label:n("filter.fields.remark"),value:"remarks",type:"text",operators:[{label:n("filter.operators.contains"),value:"contains"},{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.inviter_email"),value:"invite_user.email",type:"text",operators:[{label:n("filter.operators.contains"),value:"contains"},{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.invite_user_id"),value:"invite_user_id",type:"number",operators:[{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.is_admin"),value:"is_admin",type:"boolean",operators:[{label:n("filter.operators.eq"),value:"eq"}]},{label:n("filter.fields.is_staff"),value:"is_staff",type:"boolean",operators:[{label:n("filter.operators.eq"),value:"eq"}]}],p=W=>W*1024*1024*1024,w=W=>W/(1024*1024*1024),I=()=>{u([...c,{field:"",operator:"",value:""}])},H=W=>{u(c.filter((te,q)=>q!==W))},O=(W,te,q)=>{const L=[...c];if(L[W]={...L[W],[te]:q},te==="field"){const U=R.find(ms=>ms.value===q);U&&(L[W].operator=U.operators[0].value,L[W].value=U.type==="boolean"?!1:"")}u(L)},K=(W,te)=>{const q=R.find(L=>L.value===W.field);if(!q)return null;switch(q.type){case"text":return e.jsx(D,{placeholder:n("filter.sheet.value"),value:W.value,onChange:L=>O(te,"value",L.target.value)});case"number":return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(D,{type:"number",placeholder:n("filter.sheet.value_number",{unit:q.unit}),value:q.unit==="GB"?w(W.value||0):W.value,onChange:L=>{const U=Number(L.target.value);O(te,"value",q.unit==="GB"?p(U):U)}}),q.unit&&e.jsx("span",{className:"text-sm text-muted-foreground",children:q.unit})]});case"date":return e.jsx(rt,{mode:"single",selected:W.value,onSelect:L=>O(te,"value",L),className:"flex flex-1 justify-center rounded-md border"});case"select":return e.jsxs(X,{value:W.value,onValueChange:L=>O(te,"value",L),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:n("filter.sheet.value")})}),e.jsx(J,{children:q.useOptions?l.map(L=>e.jsx($,{value:L.value.toString(),children:L.label},L.value)):q.options?.map(L=>e.jsx($,{value:L.value.toString(),children:L.label},L.value))})]});case"boolean":return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ee,{checked:W.value,onCheckedChange:L=>O(te,"value",L)}),e.jsx(xa,{children:W.value?n("filter.boolean.true"):n("filter.boolean.false")})]});default:return null}},oe=()=>{const W=c.filter(te=>te.field&&te.operator&&te.value!=="").map(te=>{const q=R.find(U=>U.value===te.field);let L=te.value;return te.operator==="contains"?{id:te.field,value:L}:(q?.type==="date"&&L instanceof Date&&(L=Math.floor(L.getTime()/1e3)),q?.type==="boolean"&&(L=L?1:0),{id:te.field,value:`${te.operator}:${L}`})});s.setColumnFilters(W),d(!1)};return e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[e.jsxs("div",{className:"flex flex-1 flex-wrap items-center gap-2",children:[e.jsx(Jx,{refetch:a}),e.jsx(D,{placeholder:n("filter.email_search"),value:s.getColumn("email")?.getFilterValue()??"",onChange:W=>s.getColumn("email")?.setFilterValue(W.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),e.jsxs(qr,{open:i,onOpenChange:d,children:[e.jsx(Qx,{asChild:!0,children:e.jsxs(E,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(Ec,{className:"mr-2 h-4 w-4"}),n("filter.advanced"),c.length>0&&e.jsx(B,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:c.length})]})}),e.jsxs(Dn,{className:"w-[400px] sm:w-[540px]",children:[e.jsxs(Pn,{children:[e.jsx(En,{children:n("filter.sheet.title")}),e.jsx(Rn,{children:n("filter.sheet.description")})]}),e.jsxs("div",{className:"mt-6 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"font-medium",children:n("filter.sheet.conditions")}),e.jsx(E,{variant:"outline",size:"sm",onClick:I,children:n("filter.sheet.add")})]}),e.jsx(Nt,{className:"h-[calc(100vh-280px)] ",children:e.jsx("div",{className:"space-y-4",children:c.map((W,te)=>e.jsxs("div",{className:"space-y-3 rounded-lg border p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(xa,{children:n("filter.sheet.condition",{number:te+1})}),e.jsx(E,{variant:"ghost",size:"sm",onClick:()=>H(te),children:e.jsx(ds,{className:"h-4 w-4"})})]}),e.jsxs(X,{value:W.field,onValueChange:q=>O(te,"field",q),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:n("filter.sheet.field")})}),e.jsx(J,{children:e.jsx(Be,{children:R.map(q=>e.jsx($,{value:q.value,className:"cursor-pointer",children:q.label},q.value))})})]}),W.field&&e.jsxs(X,{value:W.operator,onValueChange:q=>O(te,"operator",q),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:n("filter.sheet.operator")})}),e.jsx(J,{children:R.find(q=>q.value===W.field)?.operators.map(q=>e.jsx($,{value:q.value,children:q.label},q.value))})]}),W.field&&W.operator&&K(W,te)]},te))})}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(E,{variant:"outline",onClick:()=>{u([]),d(!1)},children:n("filter.sheet.reset")}),e.jsx(E,{onClick:oe,children:n("filter.sheet.apply")})]})]})]})]}),r&&e.jsxs(E,{variant:"ghost",onClick:()=>{s.resetColumnFilters(),u([])},className:"h-8 px-2 lg:px-3",children:[n("filter.sheet.reset"),e.jsx(ds,{className:"ml-2 h-4 w-4"})]}),e.jsxs(Es,{modal:!1,children:[e.jsx(Rs,{asChild:!0,children:e.jsx(E,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:n("actions.title")})}),e.jsxs(Cs,{children:[e.jsx(Ne,{onClick:()=>_(!0),children:n("actions.send_email")}),e.jsx(Ne,{onClick:g,children:n("actions.export_csv")}),e.jsx(yt,{}),e.jsx(Ne,{onClick:()=>S(!0),className:"text-red-600 focus:text-red-600",children:n("actions.batch_ban")})]})]})]}),e.jsx(Gx,{open:h,onOpenChange:_,table:s}),e.jsx(Cn,{open:T,onOpenChange:S,children:e.jsxs(Ta,{children:[e.jsxs(Da,{children:[e.jsx(Ea,{children:n("actions.confirm_ban.title")}),e.jsx(Ra,{children:n(r?"actions.confirm_ban.filtered_description":"actions.confirm_ban.all_description")})]}),e.jsxs(Pa,{children:[e.jsx(La,{disabled:C,children:n("actions.confirm_ban.cancel")}),e.jsx(Ia,{onClick:k,disabled:C,className:"bg-red-600 hover:bg-red-700 focus:ring-red-600",children:n(C?"actions.confirm_ban.banning":"actions.confirm_ban.confirm")})]})]})})]})}const Kr=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"m17.71 11.29l-5-5a1 1 0 0 0-.33-.21a1 1 0 0 0-.76 0a1 1 0 0 0-.33.21l-5 5a1 1 0 0 0 1.42 1.42L11 9.41V17a1 1 0 0 0 2 0V9.41l3.29 3.3a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.42"})}),Br=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M17.71 11.29a1 1 0 0 0-1.42 0L13 14.59V7a1 1 0 0 0-2 0v7.59l-3.29-3.3a1 1 0 0 0-1.42 1.42l5 5a1 1 0 0 0 .33.21a.94.94 0 0 0 .76 0a1 1 0 0 0 .33-.21l5-5a1 1 0 0 0 0-1.42"})}),sh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M17 11H9.41l3.3-3.29a1 1 0 1 0-1.42-1.42l-5 5a1 1 0 0 0-.21.33a1 1 0 0 0 0 .76a1 1 0 0 0 .21.33l5 5a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.42L9.41 13H17a1 1 0 0 0 0-2"})}),th=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M17.92 11.62a1 1 0 0 0-.21-.33l-5-5a1 1 0 0 0-1.42 1.42l3.3 3.29H7a1 1 0 0 0 0 2h7.59l-3.3 3.29a1 1 0 0 0 0 1.42a1 1 0 0 0 1.42 0l5-5a1 1 0 0 0 .21-.33a1 1 0 0 0 0-.76"})}),Ja=[{accessorKey:"record_at",header:"时间",cell:({row:s})=>e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx("time",{className:"text-sm text-muted-foreground",children:Yc(s.original.record_at)})})},{accessorKey:"u",header:"上行流量",cell:({row:s})=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Kr,{className:"h-4 w-4 text-emerald-500"}),e.jsx("span",{className:"font-mono text-sm",children:Oe(s.original.u)})]})},{accessorKey:"d",header:"下行流量",cell:({row:s})=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Br,{className:"h-4 w-4 text-blue-500"}),e.jsx("span",{className:"font-mono text-sm",children:Oe(s.original.d)})]})},{accessorKey:"server_rate",header:"倍率",cell:({row:s})=>{const a=s.original.server_rate;return e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs(B,{variant:"outline",className:"font-mono",children:[a,"x"]})})}},{id:"total",header:"总计",cell:({row:s})=>{const a=s.original.u+s.original.d;return e.jsx("div",{className:"flex items-center justify-end font-mono text-sm",children:Oe(a)})}}];function Gr({user_id:s,dialogTrigger:a}){const{t}=V(["traffic"]),[l,n]=m.useState(!1),[o,r]=m.useState({pageIndex:0,pageSize:20}),{data:c,isLoading:u}=ne({queryKey:["userStats",s,o,l],queryFn:()=>l?ws.getStats({user_id:s,pageSize:o.pageSize,page:o.pageIndex+1}):null}),i=Je({data:c?.data??[],columns:Ja,pageCount:Math.ceil((c?.total??0)/o.pageSize),state:{pagination:o},manualPagination:!0,getCoreRowModel:Qe(),onPaginationChange:r});return e.jsxs(pe,{open:l,onOpenChange:n,children:[e.jsx(rs,{asChild:!0,children:a}),e.jsxs(ue,{className:"sm:max-w-[700px]",children:[e.jsx(ve,{children:e.jsx(ge,{children:t("trafficRecord.title")})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"rounded-md border",children:e.jsxs(bn,{children:[e.jsx(yn,{children:i.getHeaderGroups().map(d=>e.jsx(As,{children:d.headers.map(h=>e.jsx(_n,{className:y("h-10 px-2 text-xs",h.id==="total"&&"text-right"),children:h.isPlaceholder?null:da(h.column.columnDef.header,h.getContext())},h.id))},d.id))}),e.jsx(Nn,{children:u?Array.from({length:o.pageSize}).map((d,h)=>e.jsx(As,{children:Array.from({length:Ja.length}).map((_,T)=>e.jsx(jt,{className:"p-2",children:e.jsx(ce,{className:"h-6 w-full"})},T))},h)):i.getRowModel().rows?.length?i.getRowModel().rows.map(d=>e.jsx(As,{"data-state":d.getIsSelected()&&"selected",className:"h-10",children:d.getVisibleCells().map(h=>e.jsx(jt,{className:"px-2",children:da(h.column.columnDef.cell,h.getContext())},h.id))},d.id)):e.jsx(As,{children:e.jsx(jt,{colSpan:Ja.length,className:"h-24 text-center",children:t("trafficRecord.noRecords")})})})]})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("p",{className:"text-sm font-medium",children:t("trafficRecord.perPage")}),e.jsxs(X,{value:`${i.getState().pagination.pageSize}`,onValueChange:d=>{i.setPageSize(Number(d))},children:[e.jsx(Y,{className:"h-8 w-[70px]",children:e.jsx(Z,{placeholder:i.getState().pagination.pageSize})}),e.jsx(J,{side:"top",children:[10,20,30,40,50].map(d=>e.jsx($,{value:`${d}`,children:d},d))})]}),e.jsx("p",{className:"text-sm font-medium",children:t("trafficRecord.records")})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex w-[100px] items-center justify-center text-sm",children:t("trafficRecord.page",{current:i.getState().pagination.pageIndex+1,total:i.getPageCount()})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(G,{variant:"outline",size:"icon",className:"h-8 w-8",onClick:()=>i.previousPage(),disabled:!i.getCanPreviousPage()||u,children:e.jsx(sh,{className:"h-4 w-4"})}),e.jsx(G,{variant:"outline",size:"icon",className:"h-8 w-8",onClick:()=>i.nextPage(),disabled:!i.getCanNextPage()||u,children:e.jsx(th,{className:"h-4 w-4"})})]})]})]})]})]})]})}function ah({onConfirm:s,children:a,title:t="确认操作",description:l="确定要执行此操作吗？",cancelText:n="取消",confirmText:o="确认",variant:r="default",className:c}){return e.jsxs(Cn,{children:[e.jsx(Tr,{asChild:!0,children:a}),e.jsxs(Ta,{className:y("sm:max-w-[425px]",c),children:[e.jsxs(Da,{children:[e.jsx(Ea,{children:t}),e.jsx(Ra,{children:l})]}),e.jsxs(Pa,{children:[e.jsx(La,{asChild:!0,children:e.jsx(E,{variant:"outline",children:n})}),e.jsx(Ia,{asChild:!0,children:e.jsx(E,{variant:r,onClick:s,children:o})})]})]})]})}const nh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M5 18h4.24a1 1 0 0 0 .71-.29l6.92-6.93L19.71 8a1 1 0 0 0 0-1.42l-4.24-4.29a1 1 0 0 0-1.42 0l-2.82 2.83l-6.94 6.93a1 1 0 0 0-.29.71V17a1 1 0 0 0 1 1m9.76-13.59l2.83 2.83l-1.42 1.42l-2.83-2.83ZM6 13.17l5.93-5.93l2.83 2.83L8.83 16H6ZM21 20H3a1 1 0 0 0 0 2h18a1 1 0 0 0 0-2"})}),lh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2"})}),rh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M21 8.94a1.3 1.3 0 0 0-.06-.27v-.09a1 1 0 0 0-.19-.28l-6-6a1 1 0 0 0-.28-.19a.3.3 0 0 0-.09 0a.9.9 0 0 0-.33-.11H10a3 3 0 0 0-3 3v1H6a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3v-1h1a3 3 0 0 0 3-3zm-6-3.53L17.59 8H16a1 1 0 0 1-1-1ZM15 19a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h1v7a3 3 0 0 0 3 3h5Zm4-4a1 1 0 0 1-1 1h-8a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h3v3a3 3 0 0 0 3 3h3Z"})}),ih=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M21 11a1 1 0 0 0-1 1a8.05 8.05 0 1 1-2.22-5.5h-2.4a1 1 0 0 0 0 2h4.53a1 1 0 0 0 1-1V3a1 1 0 0 0-2 0v1.77A10 10 0 1 0 22 12a1 1 0 0 0-1-1"})}),oh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M9.5 10.5H12a1 1 0 0 0 0-2h-1V8a1 1 0 0 0-2 0v.55a2.5 2.5 0 0 0 .5 4.95h1a.5.5 0 0 1 0 1H8a1 1 0 0 0 0 2h1v.5a1 1 0 0 0 2 0v-.55a2.5 2.5 0 0 0-.5-4.95h-1a.5.5 0 0 1 0-1M21 12h-3V3a1 1 0 0 0-.5-.87a1 1 0 0 0-1 0l-3 1.72l-3-1.72a1 1 0 0 0-1 0l-3 1.72l-3-1.72a1 1 0 0 0-1 0A1 1 0 0 0 2 3v16a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3v-6a1 1 0 0 0-1-1M5 20a1 1 0 0 1-1-1V4.73l2 1.14a1.08 1.08 0 0 0 1 0l3-1.72l3 1.72a1.08 1.08 0 0 0 1 0l2-1.14V19a3 3 0 0 0 .18 1Zm15-1a1 1 0 0 1-2 0v-5h2Z"})}),ch=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M12.3 12.22A4.92 4.92 0 0 0 14 8.5a5 5 0 0 0-10 0a4.92 4.92 0 0 0 1.7 3.72A8 8 0 0 0 1 19.5a1 1 0 0 0 2 0a6 6 0 0 1 12 0a1 1 0 0 0 2 0a8 8 0 0 0-4.7-7.28M9 11.5a3 3 0 1 1 3-3a3 3 0 0 1-3 3m9.74.32A5 5 0 0 0 15 3.5a1 1 0 0 0 0 2a3 3 0 0 1 3 3a3 3 0 0 1-1.5 2.59a1 1 0 0 0-.5.84a1 1 0 0 0 .45.86l.39.26l.13.07a7 7 0 0 1 4 6.38a1 1 0 0 0 2 0a9 9 0 0 0-4.23-7.68"})}),dh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M12 2a10 10 0 0 0-6.88 2.77V3a1 1 0 0 0-2 0v4.5a1 1 0 0 0 1 1h4.5a1 1 0 0 0 0-2h-2.4A8 8 0 1 1 4 12a1 1 0 0 0-2 0A10 10 0 1 0 12 2m0 6a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h2a1 1 0 0 0 0-2h-1V9a1 1 0 0 0-1-1"})}),mh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M20 6h-4V5a3 3 0 0 0-3-3h-2a3 3 0 0 0-3 3v1H4a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V8h1a1 1 0 0 0 0-2M10 5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v1h-4Zm7 14a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1V8h10Z"})}),uh=(s,a,t,l)=>{const{t:n}=V("user");return[{accessorKey:"is_admin",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.is_admin")}),enableSorting:!1,enableHiding:!0,filterFn:(o,r,c)=>c.includes(o.getValue(r)),size:0},{accessorKey:"is_staff",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.is_staff")}),enableSorting:!1,enableHiding:!0,filterFn:(o,r,c)=>c.includes(o.getValue(r)),size:0},{accessorKey:"id",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.id")}),cell:({row:o})=>e.jsx(B,{variant:"outline",children:o.original.id}),enableSorting:!0,enableHiding:!1},{accessorKey:"email",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.email")}),cell:({row:o})=>{const r=o.original.t||0,c=Date.now()/1e3-r<120,u=Math.floor(Date.now()/1e3-r);let i=c?n("columns.online_status.online"):r===0?n("columns.online_status.never"):n("columns.online_status.last_online",{time:Ce(r)});if(!c&&r!==0){const d=Math.floor(u/60),h=Math.floor(d/60),_=Math.floor(h/24);_>0?i+=`
`+n("columns.online_status.offline_duration.days",{count:_}):h>0?i+=`
`+n("columns.online_status.offline_duration.hours",{count:h}):d>0?i+=`
`+n("columns.online_status.offline_duration.minutes",{count:d}):i+=`
`+n("columns.online_status.offline_duration.seconds",{count:u})}return e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{children:e.jsxs("div",{className:"flex items-center gap-2.5",children:[e.jsx("div",{className:y("size-2.5 rounded-full ring-2 ring-offset-2",c?"bg-green-500 ring-green-500/20":"bg-gray-300 ring-gray-300/20","transition-all duration-300")}),e.jsx("span",{className:"font-medium text-foreground/90",children:o.original.email})]})}),e.jsx(de,{side:"bottom",className:"max-w-[280px]",children:e.jsx("p",{className:"whitespace-pre-line text-sm",children:i})})]})})},enableSorting:!1,enableHiding:!1},{accessorKey:"online_count",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.online_count")}),cell:({row:o})=>{const r=o.original.device_limit,c=o.original.online_count||0;return e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{children:e.jsx("div",{className:"flex items-center gap-1.5",children:e.jsxs(B,{variant:"outline",className:y("min-w-[4rem] justify-center",r!==null&&c>=r?"border-destructive/50 bg-destructive/10 text-destructive":"border-primary/40 bg-primary/5 text-primary/90"),children:[c," / ",r===null?"∞":r]})})}),e.jsx(de,{side:"bottom",children:e.jsx("p",{className:"text-sm",children:r===null?n("columns.device_limit.unlimited"):n("columns.device_limit.limited",{count:r})})})]})})},enableSorting:!0,enableHiding:!1},{accessorKey:"banned",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.status")}),cell:({row:o})=>{const r=o.original.banned;return e.jsx("div",{className:"flex justify-center",children:e.jsx(B,{className:y("min-w-20 justify-center transition-colors",r?"bg-destructive/15 text-destructive hover:bg-destructive/25":"bg-success/15 text-success hover:bg-success/25"),children:n(r?"columns.status_text.banned":"columns.status_text.normal")})})},enableSorting:!0,filterFn:(o,r,c)=>c.includes(o.getValue(r))},{accessorKey:"plan_id",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.subscription")}),cell:({row:o})=>e.jsx("div",{className:"min-w-[10em] break-all",children:o.original?.plan?.name||"-"}),enableSorting:!1,enableHiding:!1},{accessorKey:"group_id",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.group")}),cell:({row:o})=>e.jsx("div",{className:"flex flex-wrap gap-1",children:e.jsx(B,{variant:"outline",className:y("px-2 py-0.5 font-medium","bg-secondary/50 hover:bg-secondary/70","border border-border/50","transition-all duration-200","cursor-default select-none","flex items-center gap-1.5 whitespace-nowrap"),children:o.original?.group?.name||"-"})}),enableSorting:!1},{accessorKey:"total_used",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.used_traffic")}),cell:({row:o})=>{const r=Oe(o.original?.total_used),c=Oe(o.original?.transfer_enable),u=o.original?.total_used/o.original?.transfer_enable*100||0;return e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{className:"w-full",children:e.jsxs("div",{className:"w-full space-y-1",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-muted-foreground",children:r}),e.jsxs("span",{className:"text-xs text-muted-foreground",children:[u.toFixed(1),"%"]})]}),e.jsx("div",{className:"h-1.5 w-full rounded-full bg-secondary",children:e.jsx("div",{className:y("h-full rounded-full transition-all",u>90?"bg-destructive":"bg-primary"),style:{width:`${Math.min(u,100)}%`}})})]})}),e.jsx(de,{side:"bottom",children:e.jsxs("p",{className:"text-sm",children:[n("columns.total_traffic"),": ",c]})})]})})}},{accessorKey:"transfer_enable",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.total_traffic")}),cell:({row:o})=>e.jsx("div",{className:"font-medium text-muted-foreground",children:Oe(o.original?.transfer_enable)})},{accessorKey:"expired_at",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.expire_time")}),cell:({row:o})=>{const r=o.original.expired_at,c=Date.now()/1e3,u=r!=null&&r<c,i=r!=null?new Date(r*1e3):null,d=i!=null?Math.ceil((i.getTime()-Date.now())/(1e3*60*60*24)):null;return e.jsx(be,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{className:"block w-full",children:e.jsx(B,{variant:"outline",className:y("w-full justify-center transition-colors",u?"border-destructive/50 bg-destructive/10 text-destructive":r?"border-success/50 bg-success/10 text-success":"border-primary/40 bg-primary/5 text-primary/90"),children:r==null?n("columns.expire_status.permanent"):Ce(r,"YYYY-MM-DD")})}),e.jsx(de,{side:"bottom",className:"space-y-1 p-3",children:r!=null?e.jsxs(e.Fragment,{children:[e.jsxs("p",{className:"text-sm text-muted-foreground",children:[n("columns.expire_time"),": ",Ce(r)]}),e.jsx("p",{className:y("text-sm font-medium",u?"text-destructive":"text-success"),children:u?n("columns.expire_status.expired",{days:Math.abs(d??0)}):n("columns.expire_status.remaining",{days:d??0})})]}):e.jsx("p",{className:"text-sm font-medium",children:n("columns.expire_status.permanent")})})]})})}},{accessorKey:"balance",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.balance")}),cell:({row:o})=>{const r=pt(o.original?.balance);return e.jsxs("div",{className:"flex items-center gap-1 font-medium",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"¥"}),e.jsx("span",{className:"tabular-nums text-foreground",children:r})]})}},{accessorKey:"commission_balance",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.commission")}),cell:({row:o})=>{const r=pt(o.original?.commission_balance);return e.jsxs("div",{className:"flex items-center gap-1 font-medium",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"¥"}),e.jsx("span",{className:"tabular-nums text-foreground",children:r})]})}},{accessorKey:"created_at",header:({column:o})=>e.jsx(z,{column:o,title:n("columns.register_time")}),cell:({row:o})=>e.jsx("div",{className:"truncate",children:Ce(o.original?.created_at)}),size:1e3},{id:"actions",header:({column:o})=>e.jsx(z,{column:o,className:"justify-end",title:n("columns.actions")}),cell:({row:o,table:r})=>e.jsxs(Es,{modal:!0,children:[e.jsx(Rs,{asChild:!0,children:e.jsx("div",{className:"text-center",children:e.jsx(G,{variant:"ghost",className:"h-8 w-8 p-0 hover:bg-muted","aria-label":n("columns.actions"),children:e.jsx(ua,{className:"size-4"})})})}),e.jsxs(Cs,{align:"end",className:"min-w-[40px]",children:[e.jsx(Ne,{onSelect:c=>{c.preventDefault(),t(o.original),l(!0)},className:"p-0",children:e.jsxs(G,{variant:"ghost",className:"w-full justify-start px-2 py-1.5",children:[e.jsx(nh,{className:"mr-2"}),n("columns.actions_menu.edit")]})}),e.jsx(Ne,{onSelect:c=>c.preventDefault(),className:"p-0",children:e.jsx(Or,{defaultValues:{email:o.original.email},trigger:e.jsxs(G,{variant:"ghost",className:"w-full justify-start px-2 py-1.5",children:[e.jsx(lh,{className:"mr-2 "}),n("columns.actions_menu.assign_order")]})})}),e.jsx(Ne,{onSelect:()=>{ha(o.original.subscribe_url).then(()=>{A.success(n("common:copy.success"))})},className:"p-0",children:e.jsxs(G,{variant:"ghost",className:"w-full justify-start px-2 py-1.5",children:[e.jsx(rh,{className:"mr-2"}),n("columns.actions_menu.copy_url")]})}),e.jsx(Ne,{onSelect:()=>{ws.resetSecret(o.original.id).then(({data:c})=>{c&&A.success("重置成功")})},children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ih,{className:"mr-2 "}),n("columns.actions_menu.reset_secret")]})}),e.jsx(Ne,{onSelect:()=>{},className:"p-0",children:e.jsxs(st,{className:"flex items-center px-2 py-1.5",to:`/finance/order?user_id=eq:${o.original?.id}`,children:[e.jsx(oh,{className:"mr-2"}),n("columns.actions_menu.orders")]})}),e.jsx(Ne,{onSelect:()=>{r.setColumnFilters([{id:"invite_user_id",value:"eq:"+o.original?.id}])},children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ch,{className:"mr-2 "}),n("columns.actions_menu.invites")]})}),e.jsx(Ne,{onSelect:c=>c.preventDefault(),className:"p-0",children:e.jsx(Gr,{user_id:o.original?.id,dialogTrigger:e.jsxs(G,{variant:"ghost",className:"w-full justify-start px-2 py-1.5",children:[e.jsx(dh,{className:"mr-2 "}),n("columns.actions_menu.traffic_records")]})})}),e.jsx(Ne,{onSelect:c=>c.preventDefault(),className:"p-0",children:e.jsx(ah,{title:n("columns.actions_menu.delete_confirm_title"),description:n("columns.actions_menu.delete_confirm_description",{email:o.original.email}),cancelText:n("common:cancel"),confirmText:n("common:confirm"),variant:"destructive",onConfirm:async()=>{try{const{data:c}=await ws.destroy(o.original.id);c&&(A.success(n("common:delete.success")),s())}catch{A.error(n("common:delete.failed"))}},children:e.jsxs(G,{variant:"ghost",className:"w-full justify-start px-2 py-1.5 text-destructive hover:text-destructive",children:[e.jsx(mh,{className:"mr-2"}),n("columns.actions_menu.delete")]})})})]})]})}]},Wr=m.createContext(void 0),In=()=>{const s=m.useContext(Wr);if(!s)throw new Error("useUserEdit must be used within an UserEditProvider");return s},Yr=({children:s,refreshData:a})=>{const[t,l]=m.useState(!1),[n,o]=m.useState(null),r={isOpen:t,setIsOpen:l,editingUser:n,setEditingUser:o,refreshData:a};return e.jsx(Wr.Provider,{value:r,children:s})},xh=x.object({id:x.number(),email:x.string().email(),invite_user_email:x.string().email().nullable().optional(),password:x.string().optional().nullable(),balance:x.coerce.number(),commission_balance:x.coerce.number(),u:x.number(),d:x.number(),transfer_enable:x.number(),expired_at:x.number().nullable(),plan_id:x.number().nullable(),banned:x.number(),commission_type:x.number(),commission_rate:x.number().nullable(),discount:x.number().nullable(),speed_limit:x.number().nullable(),device_limit:x.number().nullable(),is_admin:x.number(),is_staff:x.number(),remarks:x.string().nullable()});function Jr(){const{t:s}=V("user"),{isOpen:a,setIsOpen:t,editingUser:l,refreshData:n}=In(),[o,r]=m.useState(!1),[c,u]=m.useState([]),i=ye({resolver:_e(xh),defaultValues:{id:0,email:"",invite_user_email:null,password:null,balance:0,commission_balance:0,u:0,d:0,transfer_enable:0,expired_at:null,plan_id:null,banned:0,commission_type:0,commission_rate:null,discount:null,speed_limit:null,device_limit:null,is_admin:0,is_staff:0,remarks:null}});return m.useEffect(()=>{a&&es.getList().then(({data:d})=>{u(d)})},[a]),m.useEffect(()=>{if(l){const d=l.invite_user?.email,{invite_user:h,..._}=l;i.reset({..._,invite_user_email:d||null,password:null})}},[l,i]),e.jsx(qr,{open:a,onOpenChange:t,children:e.jsxs(Dn,{className:"max-w-[90%] space-y-4",children:[e.jsxs(Pn,{children:[e.jsx(En,{children:s("edit.title")}),e.jsx(Rn,{})]}),e.jsxs(we,{...i,children:[e.jsx(v,{control:i.control,name:"email",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.email")}),e.jsx(b,{children:e.jsx(D,{...d,placeholder:s("edit.form.email_placeholder")})}),e.jsx(P,{...d})]})}),e.jsx(v,{control:i.control,name:"invite_user_email",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.inviter_email")}),e.jsx(b,{children:e.jsx(D,{value:d.value||"",onChange:h=>d.onChange(h.target.value?h.target.value:null),placeholder:s("edit.form.inviter_email_placeholder")})}),e.jsx(P,{...d})]})}),e.jsx(v,{control:i.control,name:"password",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.password")}),e.jsx(b,{children:e.jsx(D,{type:"password",value:d.value||"",onChange:d.onChange,placeholder:s("edit.form.password_placeholder")})}),e.jsx(P,{...d})]})}),e.jsxs("div",{className:"grid gap-2 md:grid-cols-2",children:[e.jsx(v,{control:i.control,name:"balance",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.balance")}),e.jsx(b,{children:e.jsxs("div",{className:"flex",children:[e.jsx(D,{type:"number",value:d.value||"",onChange:d.onChange,placeholder:s("edit.form.balance_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"¥"})]})}),e.jsx(P,{...d})]})}),e.jsx(v,{control:i.control,name:"commission_balance",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.commission_balance")}),e.jsx(b,{children:e.jsxs("div",{className:"flex",children:[e.jsx(D,{type:"number",value:d.value||"",onChange:d.onChange,placeholder:s("edit.form.commission_balance_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"¥"})]})}),e.jsx(P,{...d})]})}),e.jsx(v,{control:i.control,name:"u",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.upload")}),e.jsx(b,{children:e.jsxs("div",{className:"flex",children:[e.jsx(D,{value:d.value/1024/1024/1024||"",onChange:h=>d.onChange(parseInt(h.target.value)*1024*1024*1024),placeholder:s("edit.form.upload_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"GB"})]})}),e.jsx(P,{...d})]})}),e.jsx(v,{control:i.control,name:"d",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.download")}),e.jsx(b,{children:e.jsxs("div",{className:"flex",children:[e.jsx(D,{type:"number",value:d.value/1024/1024/1024||"",onChange:h=>d.onChange(parseInt(h.target.value)*1024*1024*1024),placeholder:s("edit.form.download_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"GB"})]})}),e.jsx(P,{...d})]})})]}),e.jsx(v,{control:i.control,name:"transfer_enable",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.total_traffic")}),e.jsx(b,{children:e.jsxs("div",{className:"flex",children:[e.jsx(D,{type:"number",value:d.value/1024/1024/1024||"",onChange:h=>d.onChange(parseInt(h.target.value)*1024*1024*1024),placeholder:s("edit.form.total_traffic_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"GB"})]})}),e.jsx(P,{})]})}),e.jsx(v,{control:i.control,name:"expired_at",render:({field:d})=>e.jsxs(f,{className:"flex flex-col",children:[e.jsx(j,{children:s("edit.form.expire_time")}),e.jsxs(Ss,{open:o,onOpenChange:r,children:[e.jsx(ks,{asChild:!0,children:e.jsx(b,{children:e.jsxs(E,{type:"button",variant:"outline",className:y("w-full pl-3 text-left font-normal",!d.value&&"text-muted-foreground"),onClick:()=>r(!0),children:[d.value?Ce(d.value):e.jsx("span",{children:s("edit.form.expire_time_placeholder")}),e.jsx(Kt,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),e.jsx(bs,{className:"w-auto p-0",align:"start",side:"top",sideOffset:4,onInteractOutside:h=>{h.preventDefault()},onEscapeKeyDown:h=>{h.preventDefault()},children:e.jsxs("div",{className:"flex flex-col space-y-3 p-3",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(E,{type:"button",variant:"outline",className:"flex-1",onClick:()=>{d.onChange(null),r(!1)},children:s("edit.form.expire_time_permanent")}),e.jsx(E,{type:"button",variant:"outline",className:"flex-1",onClick:()=>{const h=new Date;h.setMonth(h.getMonth()+1),h.setHours(23,59,59,999),d.onChange(Math.floor(h.getTime()/1e3)),r(!1)},children:s("edit.form.expire_time_1month")}),e.jsx(E,{type:"button",variant:"outline",className:"flex-1",onClick:()=>{const h=new Date;h.setMonth(h.getMonth()+3),h.setHours(23,59,59,999),d.onChange(Math.floor(h.getTime()/1e3)),r(!1)},children:s("edit.form.expire_time_3months")})]}),e.jsx("div",{className:"rounded-md border",children:e.jsx(rt,{mode:"single",selected:d.value?new Date(d.value*1e3):void 0,onSelect:h=>{if(h){const _=new Date(d.value?d.value*1e3:Date.now());h.setHours(_.getHours(),_.getMinutes(),_.getSeconds()),d.onChange(Math.floor(h.getTime()/1e3))}},disabled:h=>h<new Date,initialFocus:!0,className:"rounded-md border-none"})}),e.jsxs("div",{className:"space-y-1.5",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm font-medium text-muted-foreground",children:s("edit.form.expire_time_specific")}),e.jsx(E,{type:"button",variant:"ghost",size:"sm",onClick:()=>{const h=new Date;h.setHours(23,59,59,999),d.onChange(Math.floor(h.getTime()/1e3))},className:"h-6 px-2 text-xs",children:s("edit.form.expire_time_today")})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(D,{type:"datetime-local",step:"1",value:Ce(d.value,"YYYY-MM-DDTHH:mm:ss"),onChange:h=>{const _=new Date(h.target.value);isNaN(_.getTime())||d.onChange(Math.floor(_.getTime()/1e3))},className:"flex-1"}),e.jsx(E,{type:"button",variant:"outline",onClick:()=>r(!1),children:s("edit.form.expire_time_confirm")})]})]})]})})]}),e.jsx(P,{})]})}),e.jsx(v,{control:i.control,name:"plan_id",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.subscription")}),e.jsx(b,{children:e.jsxs(X,{value:d.value!==null?String(d.value):"null",onValueChange:h=>d.onChange(h==="null"?null:parseInt(h)),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:s("edit.form.subscription_none")})}),e.jsxs(J,{children:[e.jsx($,{value:"null",children:s("edit.form.subscription_none")}),c.map(h=>e.jsx($,{value:String(h.id),children:h.name},h.id))]})]})})]})}),e.jsx(v,{control:i.control,name:"banned",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.account_status")}),e.jsx(b,{children:e.jsxs(X,{value:d.value.toString(),onValueChange:h=>d.onChange(parseInt(h)),children:[e.jsx(Y,{children:e.jsx(Z,{})}),e.jsxs(J,{children:[e.jsx($,{value:"1",children:s("columns.status_text.banned")}),e.jsx($,{value:"0",children:s("columns.status_text.normal")})]})]})})]})}),e.jsx(v,{control:i.control,name:"commission_type",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.commission_type")}),e.jsx(b,{children:e.jsxs(X,{value:d.value.toString(),onValueChange:h=>d.onChange(parseInt(h)),children:[e.jsx(Y,{children:e.jsx(Z,{placeholder:s("edit.form.subscription_none")})}),e.jsxs(J,{children:[e.jsx($,{value:"0",children:s("edit.form.commission_type_system")}),e.jsx($,{value:"1",children:s("edit.form.commission_type_cycle")}),e.jsx($,{value:"2",children:s("edit.form.commission_type_onetime")})]})]})})]})}),e.jsx(v,{control:i.control,name:"commission_rate",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.commission_rate")}),e.jsx(b,{children:e.jsxs("div",{className:"flex",children:[e.jsx(D,{type:"number",value:d.value||"",onChange:h=>d.onChange(parseInt(h.currentTarget.value)||null),placeholder:s("edit.form.commission_rate_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"%"})]})})]})}),e.jsx(v,{control:i.control,name:"discount",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.discount")}),e.jsx(b,{children:e.jsxs("div",{className:"flex",children:[e.jsx(D,{type:"number",value:d.value||"",onChange:h=>d.onChange(parseInt(h.currentTarget.value)||null),placeholder:s("edit.form.discount_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"%"})]})}),e.jsx(P,{})]})}),e.jsx(v,{control:i.control,name:"speed_limit",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.speed_limit")}),e.jsx(b,{children:e.jsxs("div",{className:"flex",children:[e.jsx(D,{type:"number",value:d.value||"",onChange:h=>d.onChange(parseInt(h.currentTarget.value)||null),placeholder:s("edit.form.speed_limit_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"Mbps"})]})}),e.jsx(P,{})]})}),e.jsx(v,{control:i.control,name:"device_limit",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.device_limit")}),e.jsx(b,{children:e.jsxs("div",{className:"flex",children:[e.jsx(D,{type:"number",value:d.value||"",onChange:h=>d.onChange(parseInt(h.currentTarget.value)||null),placeholder:s("edit.form.device_limit_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"台"})]})}),e.jsx(P,{})]})}),e.jsx(v,{control:i.control,name:"is_admin",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.is_admin")}),e.jsx("div",{className:"py-2",children:e.jsx(b,{children:e.jsx(ee,{checked:d.value===1,onCheckedChange:h=>d.onChange(h?1:0)})})})]})}),e.jsx(v,{control:i.control,name:"is_staff",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.is_staff")}),e.jsx("div",{className:"py-2",children:e.jsx(b,{children:e.jsx(ee,{checked:d.value===1,onCheckedChange:h=>d.onChange(h?1:0)})})})]})}),e.jsx(v,{control:i.control,name:"remarks",render:({field:d})=>e.jsxs(f,{children:[e.jsx(j,{children:s("edit.form.remarks")}),e.jsx(b,{children:e.jsx(Ts,{className:"h-24",value:d.value||"",onChange:h=>d.onChange(h.currentTarget.value??null),placeholder:s("edit.form.remarks_placeholder")})}),e.jsx(P,{})]})}),e.jsxs(Ur,{children:[e.jsx(E,{variant:"outline",onClick:()=>t(!1),children:s("edit.form.cancel")}),e.jsx(E,{type:"submit",onClick:()=>{i.handleSubmit(d=>{ws.update(d).then(({data:h})=>{h&&(A.success(s("edit.form.success")),t(!1),n())})})()},children:s("edit.form.submit")})]})]})]})})}function hh(){const[s]=tr(),[a,t]=m.useState({}),[l,n]=m.useState({is_admin:!1,is_staff:!1}),[o,r]=m.useState([]),[c,u]=m.useState([]),[i,d]=m.useState({pageIndex:0,pageSize:20});m.useEffect(()=>{const p=s.get("email");p&&r(w=>w.some(H=>H.id==="email")?w:[...w,{id:"email",value:p}])},[s]);const{refetch:h,data:_,isLoading:T}=ne({queryKey:["userList",i,o,c],queryFn:()=>ws.getList({pageSize:i.pageSize,current:i.pageIndex+1,filter:o,sort:c})}),[S,C]=m.useState([]),[N,g]=m.useState([]);m.useEffect(()=>{at.getList().then(({data:p})=>{C(p)}),es.getList().then(({data:p})=>{g(p)})},[]);const k=S.map(p=>({label:p.name,value:p.id})),R=N.map(p=>({label:p.name,value:p.id}));return e.jsxs(Yr,{refreshData:h,children:[e.jsx(ph,{data:_?.data??[],rowCount:_?.total??0,sorting:c,setSorting:u,columnVisibility:l,setColumnVisibility:n,rowSelection:a,setRowSelection:t,columnFilters:o,setColumnFilters:r,pagination:i,setPagination:d,refetch:h,serverGroupList:S,permissionGroups:k,subscriptionPlans:R}),e.jsx(Jr,{})]})}function ph({data:s,rowCount:a,sorting:t,setSorting:l,columnVisibility:n,setColumnVisibility:o,rowSelection:r,setRowSelection:c,columnFilters:u,setColumnFilters:i,pagination:d,setPagination:h,refetch:_,serverGroupList:T,permissionGroups:S,subscriptionPlans:C}){const{setIsOpen:N,setEditingUser:g}=In(),k=Je({data:s,columns:uh(_,T,g,N),state:{sorting:t,columnVisibility:n,rowSelection:r,columnFilters:u,pagination:d},rowCount:a,manualPagination:!0,manualFiltering:!0,manualSorting:!0,enableRowSelection:!0,onRowSelectionChange:c,onSortingChange:l,onColumnFiltersChange:i,onColumnVisibilityChange:o,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),onPaginationChange:h,getSortedRowModel:vs(),getFacetedRowModel:Ls(),getFacetedUniqueValues:Vs(),initialState:{columnVisibility:{commission_balance:!1,created_at:!1,is_admin:!1,is_staff:!1,permission_group:!1,plan_id:!1},columnPinning:{right:["actions"]}}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(eh,{table:k,refetch:_,serverGroupList:T,permissionGroups:S,subscriptionPlans:C}),e.jsx(is,{table:k})]})}function gh(){const{t:s}=V("user");return e.jsxs(Ve,{children:[e.jsxs(Fe,{children:[e.jsx(Xe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("manage.title")}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:s("manage.description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx("div",{className:"w-full",children:e.jsx(hh,{})})})]})]})}const fh=Object.freeze(Object.defineProperty({__proto__:null,default:gh},Symbol.toStringTag,{value:"Module"}));function jh({column:s,title:a,options:t}){const l=new Set(s?.getFilterValue());return e.jsxs(Ss,{children:[e.jsx(ks,{asChild:!0,children:e.jsxs(G,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(Rc,{className:"mr-2 h-4 w-4"}),a,l?.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(ke,{orientation:"vertical",className:"mx-2 h-4"}),e.jsx(B,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:l.size}),e.jsx("div",{className:"hidden space-x-1 lg:flex",children:l.size>2?e.jsxs(B,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[l.size," selected"]}):t.filter(n=>l.has(n.value)).map(n=>e.jsx(B,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:n.label},`selected-${n.value}`))})]})]})}),e.jsx(bs,{className:"w-[200px] p-0",align:"start",children:e.jsxs(Us,{children:[e.jsx(nt,{placeholder:a}),e.jsxs(Ks,{children:[e.jsx(lt,{children:"No results found."}),e.jsx(as,{children:t.map(n=>{const o=l.has(n.value);return e.jsxs($e,{onSelect:()=>{o?l.delete(n.value):l.add(n.value);const r=Array.from(l);s?.setFilterValue(r.length?r:void 0)},children:[e.jsx("div",{className:y("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",o?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(Ic,{className:y("h-4 w-4")})}),n.icon&&e.jsx(n.icon,{className:"mr-2 h-4 w-4 text-muted-foreground"}),e.jsx("span",{children:n.label})]},`option-${n.value}`)})}),l.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(St,{}),e.jsx(as,{children:e.jsx($e,{onSelect:()=>s?.setFilterValue(void 0),className:"justify-center text-center",children:"Clear filters"})})]})]})]})})]})}const vh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M19 11H5a1 1 0 0 0 0 2h14a1 1 0 0 0 0-2"})});function bh({table:s}){const{t:a}=V("ticket");return e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-4",children:[e.jsx(Bt,{defaultValue:s.getColumn("status")?.getFilterValue(),onValueChange:t=>s.getColumn("status")?.setFilterValue(t),children:e.jsxs(Ct,{className:"grid w-full grid-cols-2",children:[e.jsx(Ee,{value:"0",children:a("status.pending")}),e.jsx(Ee,{value:"1",children:a("status.closed")})]})}),s.getColumn("level")&&e.jsx(jh,{column:s.getColumn("level"),title:a("columns.level"),options:[{label:a("level.low"),value:qe.LOW,icon:vh,color:"gray"},{label:a("level.medium"),value:qe.MIDDLE,icon:Kr,color:"yellow"},{label:a("level.high"),value:qe.HIGH,icon:Br,color:"red"}]})]})})}function yh(){return e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"text-foreground",children:[e.jsx("circle",{cx:"4",cy:"12",r:"2",fill:"currentColor",children:e.jsx("animate",{id:"spinner_qFRN",begin:"0;spinner_OcgL.end+0.25s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),e.jsx("circle",{cx:"12",cy:"12",r:"2",fill:"currentColor",children:e.jsx("animate",{begin:"spinner_qFRN.begin+0.1s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),e.jsx("circle",{cx:"20",cy:"12",r:"2",fill:"currentColor",children:e.jsx("animate",{id:"spinner_OcgL",begin:"spinner_qFRN.begin+0.2s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})})]})}const Nh=Zs("flex gap-2 max-w-[60%] items-end relative group",{variants:{variant:{received:"self-start",sent:"self-end flex-row-reverse"},layout:{default:"",ai:"max-w-full w-full items-center"}},defaultVariants:{variant:"received",layout:"default"}}),Qr=m.forwardRef(({className:s,variant:a,layout:t,children:l,...n},o)=>e.jsx("div",{className:y(Nh({variant:a,layout:t,className:s}),"relative group"),ref:o,...n,children:m.Children.map(l,r=>m.isValidElement(r)&&typeof r.type!="string"?m.cloneElement(r,{variant:a,layout:t}):r)}));Qr.displayName="ChatBubble";const _h=Zs("p-4",{variants:{variant:{received:"bg-secondary text-secondary-foreground rounded-r-lg rounded-tl-lg",sent:"bg-primary text-primary-foreground rounded-l-lg rounded-tr-lg"},layout:{default:"",ai:"border-t w-full rounded-none bg-transparent"}},defaultVariants:{variant:"received",layout:"default"}}),Xr=m.forwardRef(({className:s,variant:a,layout:t,isLoading:l=!1,children:n,...o},r)=>e.jsx("div",{className:y(_h({variant:a,layout:t,className:s}),"break-words max-w-full whitespace-pre-wrap"),ref:r,...o,children:l?e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(yh,{})}):n}));Xr.displayName="ChatBubbleMessage";const wh=m.forwardRef(({variant:s,className:a,children:t,...l},n)=>e.jsx("div",{ref:n,className:y("absolute top-1/2 -translate-y-1/2 flex opacity-0 group-hover:opacity-100 transition-opacity duration-200",s==="sent"?"-left-1 -translate-x-full flex-row-reverse":"-right-1 translate-x-full",a),...l,children:t}));wh.displayName="ChatBubbleActionWrapper";const Zr=m.forwardRef(({className:s,...a},t)=>e.jsx(Ts,{autoComplete:"off",ref:t,name:"message",className:y("max-h-12 px-4 py-3 bg-background text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 w-full rounded-md flex items-center h-16 resize-none",s),...a}));Zr.displayName="ChatInput";const ei=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"m13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29l-4.3 4.29a1 1 0 0 0 0 1.42a1 1 0 0 0 1.42 0l4.29-4.3l4.29 4.3a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.42Z"})}),si=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M15.098 12.634L13 11.423V7a1 1 0 0 0-2 0v5a1 1 0 0 0 .5.866l2.598 1.5a1 1 0 1 0 1-1.732M12 2a10 10 0 1 0 10 10A10.01 10.01 0 0 0 12 2m0 18a8 8 0 1 1 8-8a8.01 8.01 0 0 1-8 8"})}),sl=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"m11.29 12l3.54-3.54a1 1 0 0 0 0-1.41a1 1 0 0 0-1.42 0l-4.24 4.24a1 1 0 0 0 0 1.42L13.41 17a1 1 0 0 0 .71.29a1 1 0 0 0 .71-.29a1 1 0 0 0 0-1.41Z"})}),Ch=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M21.71 20.29L18 16.61A9 9 0 1 0 16.61 18l3.68 3.68a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.39M11 18a7 7 0 1 1 7-7a7 7 0 0 1-7 7"})}),Sh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M3.71 16.29a1 1 0 0 0-.33-.21a1 1 0 0 0-.76 0a1 1 0 0 0-.33.21a1 1 0 0 0-.21.33a1 1 0 0 0 .21 1.09a1.2 1.2 0 0 0 .33.21a.94.94 0 0 0 .76 0a1.2 1.2 0 0 0 .33-.21a1 1 0 0 0 .21-1.09a1 1 0 0 0-.21-.33M7 8h14a1 1 0 0 0 0-2H7a1 1 0 0 0 0 2m-3.29 3.29a1 1 0 0 0-1.09-.21a1.2 1.2 0 0 0-.33.21a1 1 0 0 0-.21.33a.94.94 0 0 0 0 .76a1.2 1.2 0 0 0 .21.33a1.2 1.2 0 0 0 .33.21a.94.94 0 0 0 .76 0a1.2 1.2 0 0 0 .33-.21a1.2 1.2 0 0 0 .21-.33a.94.94 0 0 0 0-.76a1 1 0 0 0-.21-.33M21 11H7a1 1 0 0 0 0 2h14a1 1 0 0 0 0-2M3.71 6.29a1 1 0 0 0-.33-.21a1 1 0 0 0-1.09.21a1.2 1.2 0 0 0-.21.33a.94.94 0 0 0 0 .76a1.2 1.2 0 0 0 .21.33a1.2 1.2 0 0 0 .33.21a1 1 0 0 0 1.09-.21a1.2 1.2 0 0 0 .21-.33a.94.94 0 0 0 0-.76a1.2 1.2 0 0 0-.21-.33M21 16H7a1 1 0 0 0 0 2h14a1 1 0 0 0 0-2"})}),kh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M9 12H7a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2m-1-2h4a1 1 0 0 0 0-2H8a1 1 0 0 0 0 2m1 6H7a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2m12-4h-3V3a1 1 0 0 0-.5-.87a1 1 0 0 0-1 0l-3 1.72l-3-1.72a1 1 0 0 0-1 0l-3 1.72l-3-1.72a1 1 0 0 0-1 0A1 1 0 0 0 2 3v16a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3v-6a1 1 0 0 0-1-1M5 20a1 1 0 0 1-1-1V4.73l2 1.14a1.08 1.08 0 0 0 1 0l3-1.72l3 1.72a1.08 1.08 0 0 0 1 0l2-1.14V19a3 3 0 0 0 .18 1Zm15-1a1 1 0 0 1-2 0v-5h2Zm-6.44-2.83a.8.8 0 0 0-.18-.09a.6.6 0 0 0-.19-.06a1 1 0 0 0-.9.27A1.05 1.05 0 0 0 12 17a1 1 0 0 0 .07.38a1.2 1.2 0 0 0 .22.33a1.2 1.2 0 0 0 .33.21a.94.94 0 0 0 .76 0a1.2 1.2 0 0 0 .33-.21A1 1 0 0 0 14 17a1.05 1.05 0 0 0-.29-.71a2 2 0 0 0-.15-.12m.14-3.88a1 1 0 0 0-1.62.33A1 1 0 0 0 13 14a1 1 0 0 0 1-1a1 1 0 0 0-.08-.38a.9.9 0 0 0-.22-.33"})});function Th(){return e.jsxs("div",{className:"flex h-full flex-col space-y-4 p-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(ce,{className:"h-8 w-3/4"}),e.jsx(ce,{className:"h-4 w-1/2"})]}),e.jsx("div",{className:"flex-1 space-y-4",children:[1,2,3].map(s=>e.jsx(ce,{className:"h-20 w-2/3"},s))})]})}function Dh(){return e.jsx("div",{className:"space-y-4 p-4",children:[1,2,3,4].map(s=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(ce,{className:"h-5 w-4/5"}),e.jsx(ce,{className:"h-4 w-2/3"}),e.jsx(ce,{className:"h-3 w-1/2"})]},s))})}function Ph({ticket:s,isActive:a,onClick:t}){const{t:l}=V("ticket"),n=o=>{switch(o){case qe.HIGH:return"bg-red-50 text-red-600 border-red-200";case qe.MIDDLE:return"bg-yellow-50 text-yellow-600 border-yellow-200";case qe.LOW:return"bg-green-50 text-green-600 border-green-200";default:return"bg-gray-50 text-gray-600 border-gray-200"}};return e.jsxs("div",{className:y("flex cursor-pointer flex-col border-b p-4 hover:bg-accent/50",a&&"bg-accent"),onClick:t,children:[e.jsxs("div",{className:"flex max-w-[280px] items-center justify-between gap-2",children:[e.jsx("h4",{className:"flex-1 truncate font-medium",children:s.subject}),e.jsx(B,{variant:s.status===Hs.CLOSED?"secondary":"default",className:"shrink-0",children:s.status===Hs.CLOSED?l("status.closed"):l("status.processing")})]}),e.jsx("div",{className:"mt-1 max-w-[280px] truncate text-sm text-muted-foreground",children:s.user?.email}),e.jsxs("div",{className:"mt-2 flex items-center justify-between text-xs",children:[e.jsx("time",{className:"text-muted-foreground",children:Ce(s.updated_at)}),e.jsx("div",{className:y("rounded-full border px-2 py-0.5 text-xs font-medium",n(s.level)),children:l(`level.${s.level===qe.LOW?"low":s.level===qe.MIDDLE?"medium":"high"}`)})]})]})}function Eh({ticketId:s,dialogTrigger:a}){const{t}=V("ticket"),l=Is(),n=m.useRef(null),o=m.useRef(null),[r,c]=m.useState(!1),[u,i]=m.useState(""),[d,h]=m.useState(!1),[_,T]=m.useState(s),[S,C]=m.useState(""),[N,g]=m.useState(!1),{setIsOpen:k,setEditingUser:R}=In(),{data:p,isLoading:w,refetch:I}=ne({queryKey:["tickets",r],queryFn:()=>r?ft.getList({filter:[{id:"status",value:[Hs.OPENING]}]}):Promise.resolve(null),enabled:r}),{data:H,refetch:O,isLoading:K}=ne({queryKey:["ticket",_,r],queryFn:()=>r?ft.getInfo(_):Promise.resolve(null),refetchInterval:r?5e3:!1,retry:3}),oe=H?.data,te=(p?.data||[]).filter(le=>le.subject.toLowerCase().includes(S.toLowerCase())||le.user?.email.toLowerCase().includes(S.toLowerCase())),q=(le="smooth")=>{if(n.current){const{scrollHeight:ys,clientHeight:Fs}=n.current;n.current.scrollTo({top:ys-Fs,behavior:le})}};m.useEffect(()=>{if(!r)return;const le=requestAnimationFrame(()=>{q("instant"),setTimeout(()=>q(),1e3)});return()=>{cancelAnimationFrame(le)}},[r,oe?.messages]);const L=async()=>{const le=u.trim();!le||d||(h(!0),ft.reply({id:_,message:le}).then(()=>{i(""),O(),q(),setTimeout(()=>{o.current?.focus()},0)}).finally(()=>{h(!1)}))},U=async()=>{ft.close(_).then(()=>{A.success(t("actions.close_success")),O(),I()})},ms=()=>{oe?.user&&l("/finance/order?user_id="+oe.user.id)},De=oe?.status===Hs.CLOSED;return e.jsxs(pe,{open:r,onOpenChange:c,children:[e.jsx(rs,{asChild:!0,children:a??e.jsx(G,{variant:"outline",children:t("actions.view_ticket")})}),e.jsxs(ue,{className:"flex h-[90vh] max-w-6xl flex-col gap-0 p-0",children:[e.jsx(ge,{}),e.jsxs("div",{className:"flex h-full",children:[e.jsx(G,{variant:"ghost",size:"icon",className:"absolute left-2 top-2 z-50 md:hidden",onClick:()=>g(!N),children:e.jsx(sl,{className:y("h-4 w-4 transition-transform",!N&&"rotate-180")})}),e.jsxs("div",{className:y("absolute inset-y-0 left-0 z-40 flex flex-col border-r bg-background transition-transform duration-200 ease-in-out md:relative",N?"-translate-x-full":"translate-x-0","w-80 md:w-80 md:translate-x-0"),children:[e.jsxs("div",{className:"space-y-4 border-b p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"font-semibold",children:t("list.title")}),e.jsx(G,{variant:"ghost",size:"icon",className:"hidden h-8 w-8 md:flex",onClick:()=>g(!N),children:e.jsx(sl,{className:y("h-4 w-4 transition-transform",!N&&"rotate-180")})})]}),e.jsxs("div",{className:"relative",children:[e.jsx(Ch,{className:"absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground"}),e.jsx(D,{placeholder:t("list.search_placeholder"),value:S,onChange:le=>C(le.target.value),className:"pl-8"})]})]}),e.jsx(Nt,{className:"flex-1",children:e.jsx("div",{className:"w-full",children:w?e.jsx(Dh,{}):te.length===0?e.jsx("div",{className:"flex h-full items-center justify-center p-4 text-muted-foreground",children:t(S?"list.no_search_results":"list.no_tickets")}):te.map(le=>e.jsx(Ph,{ticket:le,isActive:le.id===_,onClick:()=>{T(le.id),window.innerWidth<768&&g(!0)}},le.id))})})]}),e.jsxs("div",{className:"relative flex flex-1 flex-col",children:[!N&&e.jsx("div",{className:"absolute inset-0 z-30 bg-black/20 md:hidden",onClick:()=>g(!0)}),K?e.jsx(Th,{}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex flex-col space-y-4 border-b p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("h2",{className:"text-2xl font-semibold",children:oe?.subject}),e.jsx(B,{variant:De?"secondary":"default",children:t(De?"status.closed":"status.processing")}),!De&&e.jsx(ns,{title:t("actions.close_confirm_title"),description:t("actions.close_confirm_description"),confirmText:t("actions.close_confirm_button"),variant:"destructive",onConfirm:U,children:e.jsxs(G,{variant:"ghost",size:"sm",className:"gap-1 text-muted-foreground hover:text-destructive",children:[e.jsx(ei,{className:"h-4 w-4"}),t("actions.close_ticket")]})})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Ut,{className:"h-4 w-4"}),e.jsx("span",{children:oe?.user?.email})]}),e.jsx(ke,{orientation:"vertical",className:"h-4"}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(si,{className:"h-4 w-4"}),e.jsxs("span",{children:[t("detail.created_at")," ",Ce(oe?.created_at)]})]}),e.jsx(ke,{orientation:"vertical",className:"h-4"}),e.jsx(B,{variant:"outline",children:oe?.level!=null&&t(`level.${oe.level===qe.LOW?"low":oe.level===qe.MIDDLE?"medium":"high"}`)})]})]}),oe?.user&&e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(G,{variant:"outline",size:"icon",className:"h-8 w-8",title:t("detail.user_info"),onClick:()=>{R(oe.user),k(!0)},children:e.jsx(Ut,{className:"h-4 w-4"})}),e.jsx(Gr,{user_id:oe.user.id,dialogTrigger:e.jsx(G,{variant:"outline",size:"icon",className:"h-8 w-8",title:t("detail.traffic_records"),children:e.jsx(Sh,{className:"h-4 w-4"})})}),e.jsx(G,{variant:"outline",size:"icon",className:"h-8 w-8",title:t("detail.order_records"),onClick:ms,children:e.jsx(kh,{className:"h-4 w-4"})})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:e.jsx("div",{ref:n,className:"h-full space-y-4 overflow-y-auto p-6",children:oe?.messages?.length===0?e.jsx("div",{className:"flex h-full items-center justify-center text-muted-foreground",children:t("detail.no_messages")}):oe?.messages?.map(le=>e.jsx(Qr,{variant:le.is_from_admin?"sent":"received",className:le.is_from_admin?"ml-auto":"mr-auto",children:e.jsx(Xr,{children:e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"whitespace-pre-wrap break-words",children:le.message}),e.jsx("div",{className:"text-right",children:e.jsx("time",{className:"text-[10px] text-muted-foreground",children:Ce(le.created_at)})})]})})},le.id))})}),e.jsx("div",{className:"border-t p-4",children:e.jsxs("div",{className:"relative flex items-center space-x-2",children:[e.jsx(Zr,{ref:o,disabled:De||d,placeholder:t(De?"detail.input.closed_placeholder":"detail.input.reply_placeholder"),className:"flex-1 resize-none rounded-lg border bg-background p-3 focus-visible:ring-1",value:u,onChange:le=>i(le.target.value),onKeyDown:le=>{le.key==="Enter"&&!le.shiftKey&&(le.preventDefault(),L())}}),e.jsx(G,{disabled:De||d||!u.trim(),onClick:L,children:t(d?"detail.input.sending":"detail.input.send")})]})})]})]})]})]})]})}const Rh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M19 4H5a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3m-.41 2l-5.88 5.88a1 1 0 0 1-1.42 0L5.41 6ZM20 17a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V7.41l5.88 5.88a3 3 0 0 0 4.24 0L20 7.41Z"})}),Ih=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M21.92 11.6C19.9 6.91 16.1 4 12 4s-7.9 2.91-9.92 7.6a1 1 0 0 0 0 .8C4.1 17.09 7.9 20 12 20s7.9-2.91 9.92-7.6a1 1 0 0 0 0-.8M12 18c-3.17 0-6.17-2.29-7.9-6C5.83 8.29 8.83 6 12 6s6.17 2.29 7.9 6c-1.73 3.71-4.73 6-7.9 6m0-10a4 4 0 1 0 4 4a4 4 0 0 0-4-4m0 6a2 2 0 1 1 2-2a2 2 0 0 1-2 2"})}),Lh=s=>{const{t:a}=V("ticket");return[{accessorKey:"id",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.id")}),cell:({row:t})=>e.jsx(B,{variant:"outline",children:t.getValue("id")}),enableSorting:!1,enableHiding:!1},{accessorKey:"subject",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.subject")}),cell:({row:t})=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Rh,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"max-w-[500px] truncate font-medium",children:t.getValue("subject")})]}),enableSorting:!1,enableHiding:!1,size:4e3},{accessorKey:"level",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.level")}),cell:({row:t})=>{const l=t.getValue("level"),n=l===qe.LOW?"default":l===qe.MIDDLE?"secondary":"destructive";return e.jsx(B,{variant:n,className:"whitespace-nowrap",children:a(`level.${l===qe.LOW?"low":l===qe.MIDDLE?"medium":"high"}`)})},filterFn:(t,l,n)=>n.includes(t.getValue(l))},{accessorKey:"status",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.status")}),cell:({row:t})=>{const l=t.getValue("status"),n=t.original.reply_status,o=l===Hs.CLOSED?a("status.closed"):a(n===0?"status.replied":"status.pending"),r=l===Hs.CLOSED?"default":n===0?"secondary":"destructive";return e.jsx(B,{variant:r,className:"whitespace-nowrap",children:o})}},{accessorKey:"updated_at",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.updated_at")}),cell:({row:t})=>e.jsxs("div",{className:"flex items-center space-x-2 text-muted-foreground",children:[e.jsx(si,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:Ce(t.getValue("updated_at"))})]}),enableSorting:!0},{accessorKey:"created_at",header:({column:t})=>e.jsx(z,{column:t,title:a("columns.created_at")}),cell:({row:t})=>e.jsx("div",{className:"text-sm text-muted-foreground",children:Ce(t.getValue("created_at"))}),enableSorting:!0,meta:{isFlexGrow:!0}},{id:"actions",header:({column:t})=>e.jsx(z,{className:"justify-end",column:t,title:a("columns.actions")}),cell:({row:t})=>{const l=t.original.status!==Hs.CLOSED;return e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx(Eh,{ticketId:t.original.id,dialogTrigger:e.jsx(G,{variant:"ghost",size:"icon",className:"h-8 w-8",title:a("actions.view_details"),children:e.jsx(Ih,{className:"h-4 w-4"})})}),l&&e.jsx(ns,{title:a("actions.close_confirm_title"),description:a("actions.close_confirm_description"),confirmText:a("actions.close_confirm_button"),variant:"destructive",onConfirm:async()=>{ft.close(t.original.id).then(()=>{A.success(a("actions.close_success")),s()})},children:e.jsx(G,{variant:"ghost",size:"icon",className:"h-8 w-8",title:a("actions.close_ticket"),children:e.jsx(ei,{className:"h-4 w-4"})})})]})}}]};function Vh(){const[s,a]=m.useState({}),[t,l]=m.useState({}),[n,o]=m.useState([{id:"status",value:"0"}]),[r,c]=m.useState([]),[u,i]=m.useState({pageIndex:0,pageSize:20}),{refetch:d,data:h}=ne({queryKey:["orderList",u,n,r],queryFn:()=>ft.getList({pageSize:u.pageSize,current:u.pageIndex+1,filter:n,sort:r})}),_=Je({data:h?.data??[],columns:Lh(d),state:{sorting:r,columnVisibility:t,rowSelection:s,columnFilters:n,pagination:u},rowCount:h?.total??0,manualPagination:!0,manualFiltering:!0,manualSorting:!0,enableRowSelection:!0,onRowSelectionChange:a,onSortingChange:c,onColumnFiltersChange:o,onColumnVisibilityChange:l,getCoreRowModel:Qe(),getFilteredRowModel:js(),getPaginationRowModel:ls(),onPaginationChange:i,getSortedRowModel:vs(),getFacetedRowModel:Ls(),getFacetedUniqueValues:Vs(),initialState:{columnPinning:{right:["actions"]}}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(bh,{table:_,refetch:d}),e.jsx(is,{table:_,showPagination:!0})]})}function Fh(){const{t:s}=V("ticket");return e.jsxs(Yr,{refreshData:()=>{},children:[e.jsxs(Ve,{children:[e.jsxs(Fe,{children:[e.jsx(Xe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ue,{}),e.jsx(Ke,{})]})]}),e.jsxs(Ae,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(Vh,{})})]})]}),e.jsx(Jr,{})]})}const Mh=Object.freeze(Object.defineProperty({__proto__:null,default:Fh},Symbol.toStringTag,{value:"Module"}));export{qh as a,$h as c,Ah as g,Hh as r};
