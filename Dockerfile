FROM phpswoole/swoole:php8.4-alpine

COPY --from=mlocati/php-extension-installer /usr/bin/install-php-extensions /usr/local/bin/

# 先安装系统依赖，包括编译 PHP 扩展可能需要的 -dev 包和 git
RUN apk --no-cache add \
    shadow \
    sqlite \
    mysql-client \
    mysql-dev \
    mariadb-connector-c \
    git \
    patch \
    supervisor \
    redis \
    # build-base 提供了编译工具链 (gcc, make 等)
    build-base \
    # autoconf 和 libtool 有时也需要
    autoconf \
    libtool && \
    # 创建用户和组
    addgroup -S -g 1000 www && adduser -S -G www -u 1000 www && \
    (getent group redis || addgroup -S redis) && \
    (getent passwd redis || adduser -S -G redis -H -h /data redis)

# 安装 PHP 扩展，注意 CFLAGS 可以合并到一起
RUN CFLAGS="-O0 -g0" install-php-extensions pcntl bcmath zip redis

WORKDIR /www

COPY .docker /

# Add build arguments (虽然在这里硬编码了URL和分支，但保留变量是好习惯)
ARG CACHEBUST
ARG REPO_URL
ARG BRANCH_NAME

# 克隆代码
RUN echo "Attempting to clone branch: main from https://github.com/cedar2025/Xboard.git" && \
    rm -rf ./* && \
    rm -rf .git && \
    git config --global --add safe.directory /www && \
    git clone https://github.com/cedar2025/Xboard.git . && \
    ls -al /www # 验证克隆是否成功

COPY .docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Composer 安装和权限设置
RUN composer install --no-cache --no-dev \
    && php artisan storage:link \
    && chown -R www:www /www \
    && chmod -R 775 /www/storage /www/bootstrap/cache # 更精确的权限设置 \
    && mkdir -p /data \
    && chown redis:redis /data && \
    ls -al /www/vendor # 验证 Composer 安装成功

ENV ENABLE_WEB=true \
    ENABLE_HORIZON=true \
    ENABLE_REDIS=false

EXPOSE 7001
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]