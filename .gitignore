/node_modules
/config/v2board.php
/config/googleCloudStorageKey.json
/public/hot
/public/storage
/public/env.example.js
*.user.ini
/storage/*.key
/vendor
.env
.env.backup
.phpunit.result.cache
.idea
.lock
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
composer.phar
composer.lock
yarn.lock
docker-compose.yml
.DS_Store
/docker
storage/laravels.conf
storage/laravels.pid
storage/update_pending
storage/laravels-timer-process.pid
cli-php.ini
frontend
docker-compose.yaml
bun.lockb
compose.yaml
.scribe